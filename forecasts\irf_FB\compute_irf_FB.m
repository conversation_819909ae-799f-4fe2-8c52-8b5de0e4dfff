function  [out] = compute_irf(parsed_model, shockList, responseList,  varargin)
%--------------------------------------------------------------------------------------------%
% compute_irf	--> generates impulse-responses
%
% INPUTS:	scenaria	--> parsed model, OR structure of parsed models
%    		runs		--> the length of IRF grafs (default is 40 periods)
%   		shift		--> shift of anticipated shocks
%   		anticipate	--> run also ANTICIPATED shocks (true/false) 
%
% OUTPUT:   out --> cell structure of the form 'out.shock_name.response_variable_name'
%
% LastChange: za, 08/03/2011
%--------------------------------------------------------------------------------------------%

%--defaults setting--%
default	= 	{	'runs',         20,	...		
			    'shift',        8,	...
			    'anticipate',   false,...
				'expectations',	false,...
			    'sdate',        1,    ...		
				'transform',	0, ...
                'shocksize',	0, ...
	     	};

options = passopt(default,varargin{1:end});

m = parsed_model;

% get model parameters
p = get(m,'params');
shockListM      = intersect(shockList,get(m,'eList')');
responseListM   = intersect(responseList,get(m,'xList')');

shock_date = (options.sdate + options.shift); % : (options.sdate + options.shift + 110); % permanent
simrange = options.sdate : (options.runs + options.shift);

% pl = plan(m,shock_date:shock_date+39);
% pl = exogenize(pl,'i_star',shock_date:shock_date+39);
% pl = endogenize(pl,'eps_Istar',shock_date:shock_date+39);

% iterate through shocks and simulate responses
for ishock = 1 : length(shockListM)
    maxlag = (-1)*(get(m,'maxlag'));
    d0 = zerodb(m, options.sdate-maxlag:options.sdate+options.runs-1);
    % assign the shock value
    switch options.shocksize
          case 0,            
            d0.(shockListM{ishock})(shock_date) = p.(strcat('std_',shockListM{ishock}));  
          case 1
            d0.(shockListM{ishock})(shock_date) = p.(strrep(strcat('std_',shockListM{ishock}),'_exp',''));  
          case 2,                          
            d0.(shockListM{ishock})(shock_date) = 0.0025;           
        otherwise, 
           error('Unknow shock size case!');
    end 
	% simulate
    if options.anticipate
		if isa(options.expectations,'expDesign')
			s = simulate(m, d0, simrange, ...
				'deviation', true, 'anticipate', true, ...
				... 'plan', pl, ...
				'expectations',options.expectations);
		else
			s = simulate(m, d0, simrange, ...
				'deviation', true, 'anticipate', true);
		end
	else
		s = simulate(m, d0, simrange, ...
			'deviation', true, 'anticipate', false);
    end
    % simulate
    d = dboverlay(d0, s);
	if options.transform
		% transformations
		d = some_transformations_FB(d, p);
		out.(shockListM{ishock}) = d * [responseListM; strcat('zz_',responseListM); shockListM{ishock}];
	else
		out.(shockListM{ishock}) = d * [responseListM; shockListM{ishock}];
	end
end



