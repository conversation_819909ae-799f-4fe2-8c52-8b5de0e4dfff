function [ sr_opt sr_opt_old sr_opt_cmp] = sr_options_get_into_old_structure(sr_ID)

% This function creates a data structure that stores data for forecast
% specification like: input data, labels, dates etc.
% this function is called by drivers for filtering and forecasting, also
% drivers for reporting are using it.
% Syntax
% function sr_opt = sr_options()

% @ last revision fk apr-2012
%% --------------------------------------------------------------------- %%
% Paths
path_opt.indata_dir       = '..\database\Input-data';
path_opt.outdata_dir      = '..\database\Output-data';
path_opt.outreport_dir    = '..\database\Output-reports';
path_opt.outgraph_dir     = '..\database\Output-graphs';
paths_ = fieldnames(path_opt);

% nejprve ulozit do
save([path_opt.outdata_dir '\' sr_ID.report_prefix '-sr_opt.mat'], 'sr_ID');  
sr_opt=sr_ID;clear sr_ID;

% add paths
for i = 1:length(paths_)
	sr_opt.(paths_{i}) = path_opt.(paths_{i});
end

%finding histcore - prefer indata
if isequal(exist([sr_opt.indata_dir '\' sr_opt.histcore_name], 'file'), 2)
		sr_opt.histcore_name=[sr_opt.indata_dir '\' sr_opt.histcore_name];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.histcore_name], 'file'), 2)
        sr_opt.histcore_name=[sr_opt.outdata_dir '\' sr_opt.histcore_name];
else
    error('Histcore file not found!');
end

%finding postbaseline - prefer indata
if isequal(exist([sr_opt.indata_dir '\' sr_opt.histcore_name_pbs], 'file'), 2)
		sr_opt.histcore_name_pbs=[sr_opt.indata_dir '\' sr_opt.histcore_name_pbs];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.histcore_name_pbs], 'file'), 2)
        sr_opt.histcore_name_pbs=[sr_opt.outdata_dir '\' sr_opt.histcore_name_pbs];
else
    error('Posbaseline file not found!');
end

%finding adhocdata - prefer indata
if isequal(exist([sr_opt.indata_dir '\' sr_opt.adhoc_data], 'file'), 2)
		sr_opt.adhoc_data=[sr_opt.indata_dir '\' sr_opt.adhoc_data];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.adhoc_data], 'file'), 2)
        sr_opt.adhoc_data=[sr_opt.outdata_dir '\' sr_opt.adhoc_data];
else
    error('Posbaseline file not found!');
end

% name of current forecast database
sr_opt.filter_name       = [sr_opt.outdata_dir '\' sr_opt.report_prefix '-filter.csv'];
sr_opt.data_name         = [sr_opt.outdata_dir '\' sr_opt.report_prefix '-forecast.csv'];
sr_opt.data_name_pure    = [sr_opt.outdata_dir '\' sr_opt.report_prefix '-forecast_pure.csv'];

%finding previous forecast database - prefer indata
if isequal(exist([sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-forecast.csv'], 'file'), 2)
		 sr_opt.olddata_name=[sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-forecast.csv'];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-forecast.csv'], 'file'), 2)
         sr_opt.olddata_name=[sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-forecast.csv'];
else
    error('Previous forecast file not found!');
end

%finding previous forecast pure database - prefer indata
if isequal(exist([sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-forecast_pure.csv'], 'file'), 2)
		 sr_opt.olddata_name_pure=[sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-forecast_pure.csv'];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-forecast_pure.csv'], 'file'), 2)
         sr_opt.olddata_name_pure=[sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-forecast_pure.csv'];
else
    error('Previous forecast pure file not found!');
end

%finding previous filtering database;
if isequal(exist([sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-filter.csv'], 'file'), 2)
		 sr_opt.oldfilter_name=[sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-filter.csv'];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-filter.csv'], 'file'), 2)
         sr_opt.oldfilter_name=[sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-filter.csv'];
else
    error('Previous filter file not found!');
end
 
% Load old options - prefer indata
if isequal(exist([sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-sr_opt.mat'], 'file'), 2)
    load([sr_opt.indata_dir '\' sr_opt.oldfrc_prefix '-sr_opt.mat']);
    sr_opt_old = sr_ID; clear sr_ID;
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-sr_opt.mat'], 'file'), 2)
    load([sr_opt.outdata_dir '\' sr_opt.oldfrc_prefix '-sr_opt.mat']);
    sr_opt_old = sr_ID; clear sr_ID;
else
    error(['Basic options for ' sr_opt.oldfrc_prefix ' not found!']);
end

%finding previous histcore database;
if isequal(exist([sr_opt.indata_dir '\' sr_opt_old.histcore_name], 'file'), 2)
		 sr_opt.oldhistc_name=[sr_opt.indata_dir '\' sr_opt_old.histcore_name];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt_old.histcore_name], 'file'), 2)
         sr_opt.oldhistc_name=[sr_opt.outdata_dir '\' sr_opt_old.histcore_name];
else
    sr_opt.oldhistc_name='';
end

%finding previous posbaseline database;
if isequal(exist([sr_opt.indata_dir '\' sr_opt_old.histcore_name_pbs], 'file'), 2)
		 sr_opt.oldhistc_name_pbs=[sr_opt.indata_dir '\' sr_opt_old.histcore_name_pbs];
elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt_old.histcore_name_pbs], 'file'), 2)
         sr_opt.oldhistc_name_pbs=[sr_opt.outdata_dir '\' sr_opt_old.histcore_name_pbs];
else
    sr_opt.oldhistc_name_pbs='';
end

% add paths
for i = 1:length(paths_)
	sr_opt_old.(paths_{i}) = path_opt.(paths_{i});
end

sr_opt_cmp.cmpreport_prefix = [sr_opt.report_prefix '-v-' sr_opt.oldfrc_prefix]; % define compare report prefix
sr_opt_cmp.lgnd             = sr_opt.lgnd;
sr_opt_cmp.lgnd_eng         = sr_opt.lgnd_eng;					% Legend labels for graphs - english version
sr_opt_cmp.lgnd_alt         = sr_opt.lgnd_alt;              	% Labels for alternative scenarios

% add paths
for i = 1:length(paths_)
	sr_opt_cmp.(paths_{i}) = path_opt.(paths_{i});
end
end % function
