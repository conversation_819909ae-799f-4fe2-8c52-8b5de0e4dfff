%**************************************************************************
%% DRIVER_FILTER
%**************************************************************************

close all
clear all
disp([sprintf('\n'), 'DRIVER_FILTER']);

%**************************************************************************
%% Set options

do_filter_book		= 0; 
do_filter_detail	= 0;
do_filter_compare   = 0;
do_filter_pt        = 0; 
% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('GLOBALSETTINGS.mat');

disp(['FB_ID: ', FB_ID]);

% load settings
settings = load_settings(FB_ID);
%**************************************************************************
%% Read model and parameters
SS.filtering    = true;
SS              = setparam_FB(settings, 'filtering', SS.filtering);
m = readmodel('../../Utilities-core/model/inc_g3_FB.model',SS);

%**************************************************************************
%% Load data
% h = dbload(settings.histcore_name, 'leadingRow', 'date', 'freq=', 4,...
% 'delimiter=', ','); % loading from non-IRIS data source
h = dbload(settings.histcore_name_adj);
h = changedata_FB(h,settings,SS);
f_outputgap = dbload([settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.csv']);
% dbo original database
% d pre-filter database
dbm = histdata_FB(h, settings);

% remove forecast from database
d = dbclip(dbm,settings.hrng);                                  

% add tunes
% dtunes = get_tunes_FB(SS);

dtunes.tune_dot_y_star_trend_shift = tseries();
dtunes.tune_dot_y_star_trend_fund = tseries();
dtunes.tune_y_star_gap = tseries();

dtunes.tune_dot_y_star_trend_fund(settings.shist:settings.ehist) = f_outputgap.dot_y_star_trend_fund;
dtunes.tune_y_star_gap(settings.shist:settings.ehist) = f_outputgap.y_star_gap;
dtunes.tune_dot_y_star_trend_shift(settings.shist:settings.ehist) = f_outputgap.tune_dot_y_star_trend_shift;
%**************************************************************************
%% Run the filtering step
dbfilter = filterhistory(m, d, dtunes, SS,settings.hrng);
f = dbfilter.mean;

% add out of model core variables
f = make_outofcore_FB(f, SS, settings);
% make transformations
f_model = make_transformations_FB(f, SS, false);
% add reporting layer of data
ff = make_transformations_present_FB(f_model, h, settings, SS);

%**************************************************************************
%% Report
if do_filter_book
    book_filter_FB(ff, m, settings, ...
        ['Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-book-fb']);
end

if do_filter_detail
    book_filter_detail_FB(ff, m, settings, ...
        ['Detailed Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-detail-fb']);
end

if do_filter_compare
    ff_old  = dbload([settings.outdata_dir '\' settings.oldfrc_prefix '-filter-fbs.csv']);
    settings_old  = load([settings.outdata_dir '\' settings.oldfrc_prefix '-settings.mat']);
    book_filter_compare_FB(ff, ff_old, m, settings,settings_old, ...
        ['Compare Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-v-' settings.oldfrc_prefix '-filter-comp-fb']);
end

if do_filter_pt
    book_filter_pt_FB(ff, m, settings, ...
        ['Detailed Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-detail-pt']);
end

%**************************************************************************
%% Save 
% model structure
save([settings.outdata_dir '\' settings.report_prefix '-kalman-fb.mat']','m');
% SS of filter model
save([settings.outdata_dir '\' settings.report_prefix '-filterSS-fb.mat'],'-struct', 'SS');
% db with filter tunes
save([settings.outdata_dir '\' settings.report_prefix '-tunes-fb.mat'],'-struct', 'dtunes');
% filter in database: database for filtering
dbsave(dbm,[settings.outdata_dir '\' settings.report_prefix '-pre-filter-fb.csv'],Inf,'format','%.16e');
% filter db: only model variables
save([settings.outdata_dir '\' settings.report_prefix '-filterdata-fb.mat'],'-struct','f');
% filter db: model and reporting level of variables, csv
dbsave(ff,[settings.outdata_dir '\' settings.report_prefix '-filter-fbs.csv'],Inf,'format','%.16e');
%%


ff_base = dbload(['N:\ZahrBlock\Model_final\forecasts\database\Output-data' '\' '2025sz01_fbs' '-filter-fbs.csv']);
ff_base_free = dbload(['N:\ZahrBlock\Model_final\forecasts\database\Output-data' '\' '2025sz01_fbs_free' '-filter-fbs.csv']);
ff_us_judg = dbload(['N:\ZahrBlock\Model_2025\forecasts\database\Output-data' '\' '2025sz01_fbs' '-filter-fbs.csv']);
ff_us_free = dbload(['N:\ZahrBlock\Model_2025\forecasts\database\Output-data' '\' '2025sz01_fbs_free' '-filter-fbs.csv']);
figure();
hold on;
plot([ff_us_free.zz_y_star_gap ff_base.zz_y_star_gap ff.zz_y_star_gap])
legend('ZB US rate free','ZB baseline','ZB US new', 'Location', 'best');
hold off;

% ff = dbclip(ff, settings.shist:settings.ehist);
% figure();
% hold on;
% plot([ff.ne_zz_dot_y_star ff.dot_y_star_ss]);
% hold off;

[ff_us_free.zz_y_star_gap ff_base.zz_y_star_gap ff.zz_y_star_gap]


long = dbload(['N:\ZahrBlock\Model_2025\forecasts\database\Output-data' '\' '2025sz01_fbs_new' '-outputgap-fb.csv']);


dd_base= dbload(['N:\ZahrBlock\Model_final\forecasts\database\Output-data' '\' '2025sz01_fbs' '-forecast-fb.csv']);