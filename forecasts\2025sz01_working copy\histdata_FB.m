function [dbm] = histdata_FB(h, settings)
%*************************************************************************
% HISTDATA_FB
%    - creates 'dbm'= DataBaseModel
%    - includes OBS variables into dbm
% @ last revision: JZ, March 2023
%*************************************************************************

dbm.net4_euribor3m                   = h.net4_euribor3m;
dbm.net4_shadoweuribor3m             = h.net4_shadoweuribor3m;
dbm.i_star                           = (1 + h.net4_euribor3m / 100)^(1 / 4);
dbm.i_star_eu                        = (1 + h.net4_shadoweuribor3m / 100)^(1 / 4);
dbm.i_star_us                        = (1 + h.net4_ussofr3m/ 100)^(1 / 4);
dbm.i_star_shadow                    = (1 + h.net4_shadoweuribor3m / 100)^(1 / 4);
dbm.i_star_eq                        = (1 + h.equilibrium_i_star / 100)^(1/4);
dbm.eonia3m                          = (1 + h.net4_eonia3m / 100)^(1/4);

dbm.y_star                           = h.gdp_emu;
if ~settings.FB_gdp_decomp
    dbm.gdp_emu_gap                  = h.gdp_emu_gap;       
    dbm.y_star_trend                 = h.gdp_emu_trend;
    dbm.y_star_trend_fund            = h.gdp_emu_trend_fund;
    dbm.dot_y_star_trend_shift       = (1 + h.gdp_emu_trend_shift / 100)^(1/4);
    dbm.prem_usdeur                      = h.usdeur;
end

dbm.pstar_energy_tilde               = h.ppi_emu_energy;
dbm.pstar_other_tilde                = h.ppi_emu_other;
dbm.pstar_tilde                      = h.ppi_emu;

dbm.cpi_star_tilde                   = h.cpi_emu;

dbm.usdeur                           = h.usdeur;

%% Observations - filter database dbm
dbm.obs_I_STAR      	= 400 * (dbm.i_star - 1);
dbm.obs_I_STAR_SHADOW	= 400 * (dbm.i_star_shadow - 1);
dbm.obs_I_STAR_EU       = 400 * (dbm.i_star_eu - 1);
dbm.obs_I_STAR_US       = 400 * (dbm.i_star_us - 1);
dbm.obs_I_STAR_EQ       = 400 * (dbm.i_star_eq - 1);

dbm.obs_Y_STAR          = 100 * log(dbm.y_star); 

if ~settings.FB_gdp_decomp
    dbm.obs_Y_STAR_GAP              = 100*dbm.gdp_emu_gap;
    dbm.obs_Y_STAR_TREND            = 100*log(dbm.y_star_trend);
    dbm.obs_DOT_Y_STAR_TREND_SHIFT	= 400 * (dbm.dot_y_star_trend_shift - 1);
    dbm.obs_PREM_USDEUR = dbm.prem_usdeur;
end
    
dbm.obs_PSTAR_TILDE         = 100 * log(dbm.pstar_tilde); 
dbm.obs_PSTAR_ENERGY_TILDE  = 100 * log(dbm.pstar_energy_tilde); 
dbm.obs_PSTAR_OTHER_TILDE   = 100 * log(dbm.pstar_other_tilde); 

dbm.obs_CPI_STAR_TILDE	= 100 * log(dbm.cpi_star_tilde);

dbm.obs_USDEUR	= 100 * log(dbm.usdeur);

%% Expert judgements - won't appear in reports

disp('histdata_FB: Tuning data in model after transformations');


%% Weighted average of 3M EURIBOR and its shadow counterpart (for UIP)
dbm = shadowRates_average(dbm,settings);

end

%<eof>