function decomp_explode(decomp_list,groups,groups_nms)

 %keyboard;

%% driver_decomposition <stolen>

settings = evalin('base','settings');

%% report options

displaygroups       = 1;

grouping_type       =  evalin('base','grouping_type');			% 1 - Zuzka - READ_GROUPS.M 
																% 0 - Franta - SET_GROUPS.M
autogroups			= 'MANUAL';					% 0, [] - no grouping into group of factors
                                                % 'Factors' - group into factors no time...
												% first part (?_X_X): Forecast, ForecastINI, Grip, Fulfillment ie 'GRIP_01_??'
                                                % 'GRIP_01_??'  - first part is usually defined with respect to
                                                % decomp.type, second is detail level, third some special case
automatic_groups    = 1;			% 0 - manual setting of groups, 1 - basic (previous choice), 2 - more detail (doesn't work...), 
									% 3 - even more detail, 4 - even more detail
									% 5 - no grouping
                                                
firstgroups			= 0;			% if # groups is specified, for each var from decomp_list only # of sorted factors is reported

doXLS				= 0;			% export contributions in .xls format
doReport			= 0;			% report in PDF
doGraphs			= 1;			% MATLAB graphs
doGRIP				= 0;			% GRIP picture !!! Check code!!!
Grip_graph_format   = evalin('base','Grip_graph_format');   % set output format of grip figure,  htm, doc, ppt
Grip_graph_lims     = evalin('base','Grip_graph_lims');     % sets the size of axis in graph
output_ID			= evalin('base','output_ID');			% this string is added to saved report and xls name; could be also empty string:
															% '2text' use this ID for official version exported to xls (graph in text)

language			= evalin('base','language');			% language version EN/CZ
plot_range          = evalin('base','plot_range');			% range which will be depicted
zz_transform		= evalin('base','zz_transform');		% transformation to %

graph_style         = evalin('base','graph_style');         % 1 - new, 0 - old;
click_legend        = 1;									% Turn on/off onClick feature for detailed decomposition
click_legend_order  = evalin('base','click_legend_order');  % 0 - original no ordering in groups#1234, 1 - order sum contribs from most positive to most negative, 2 - order of abs contribs from most significant to least
custom_datatips     = 1;                                    % Turn on/off onClick custom datatip feature


%% decomposition

decomp =evalin('base','decomp');toc                         

report_decomposition(decomp, settings, plot_range, ...
	'decomp_list',decomp_list, ...
	'grouping_type',grouping_type, ...
	'autogroups',autogroups, ...
	'automatic_groups',automatic_groups, ...
	'firstgroups',firstgroups,...
	'xls',doXLS, ...
	'report',doReport, ...
	'graphs',doGraphs, ...
	'grip',doGRIP,...
	'output_id',output_ID, ...
	'language',language, ...
	'zz_transform',zz_transform, ...
    'graph_style', graph_style, ...
    'grip_type', Grip_graph_format, ...
    'grip_lim', Grip_graph_lims, ...
    'displaygroups', displaygroups, ...
    'click_legend', click_legend, ...
    'click_legend_order', click_legend_order, ...
    'custom_datatips', custom_datatips, ...
	'manual_groups_names', groups_nms, ...
	'manual_groups', groups);

end %<eof>