pause(0.1);

%% Start an ActiveX session with PowerPoint:
ppt = actxserver('PowerPoint.Application');
ppt.visible = 1;

%% Open existing presentation:
% Otevrit prezentaci
disp([sprintf('\n'),'<PERSON> se otevira ''.ppt'' pro prezentaci na plneni,']);
disp(['...pokud to dlouho trva, tak je soubor nejspis otevreny']);
disp(['...a je potreba ho nejprve zavrit !!!']);

try 
	session_ppt = invoke(ppt.Presentations,'Open',Pptfile,[],[],1); % 1 = visible :)
	disp([sprintf('\n'), 'Otevreni ''.ppt'' souboru OK...']);
catch
	disp([sprintf('\n'), 'Nepodarilo se otevrit ''.ppt'' soubor s prezentaci plneni...']);
end

%% Get current number of slides:
slide_count = get(session_ppt.Slides,'Count');

%% Add a new slide (with title object):
slide_count = int32(double(slide_count)+1);
new_slide = invoke(session_ppt.Slides,'Add',slide_count,11);

