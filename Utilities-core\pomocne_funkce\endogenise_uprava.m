function this = endogenise_uprava(this,list,dates,flag)
% ENDOGENISE  Endogenise some of the model shocks on the specified range.
%
% -Syntax
%
%    p = endogenise(m,list,dates)
%    p = endogenise(p,list,dates,flag)
%
% -Input arguments
%
% * |p| [ plan ] - Simulation plan.
%
% * |list| [ cellstr | char ] - List of the model shocks that will be
% endogenised.
%
% * |dates| [ numeric ] - Dates at which the shocks will be endogenised.
%
% * |flag| [ 1 | 1i ] - Switch between anticipated and unanticipated
% shocks; if missing, 1 is assumed.
%
% -Output arguments
%
% * |p| [ plan ] - Simulation plan with new information on endogenised
% shocks included.
%
% -Description
%
% -Example

% -The IRIS Toolbox.
% -Copyright (c) 2007-2011 Jaro<PERSON>.

if nargin < 4
   flag = 1;
end

% Parse required input arguments.
P = inputParser();
P.addRequired('p',@isplan);
P.addRequired('list',@(x) ischar(x) || iscellstr(x));
P.addRequired('dates',@isnumeric);
P.addRequired('flag',@(x) isnumericscalar(x) && (x == 1 || x == 1i));
P.parse(this,list,dates,flag);

% Convert char list to cell of str.
if ischar(list)
   list = regexp(list,'[A-Za-z]\w*','match');
end

if isempty(list)
   return
end

%**************************************************************************

[dates,outofrange] = dateindex(this,dates);

if ~isempty(outofrange)
   % Report invalid dates.
   irisoverhead.error('plan', ...
      'Dates out of simulation plan range: %s.', ...
      dat2charlist(outofrange));
end

nlist = numel(list);
valid = true([1,nlist]);

for i = 1 : nlist
   index = strcmp(this.nlist,list{i});
   if any(index)
      if flag == 1
         this.nanchorsreal(index,dates) = true;
      else
         this.nanchorsimag(index,dates) = true;
      end
   else
      index = strcmp(this.xlist,list{i});
      if any(index)
         this.xanchors(index,dates) = false;
      else
         valid(i) = false;
      end
   end
end

% Report invalid names.
if any(~valid)
   irisoverhead.error('plan', ...
      'Cannot endogenise this name: ''%s''.', ...
      list{~valid});
end

end