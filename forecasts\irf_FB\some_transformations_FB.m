function [dout,varargout] = some_transformations(d, p)

% [dout, islog] = some_transformations(d, p)
%
% Reduced version of make transformations.
% Enrich the given database d with some useful variable transformations prefixed
% with zz_. If the tuneflag is true, then also variables of the form
% tune_<varname> are transformed (in the same way as varname) and stored
% as zz_tune_<varname> 
% In islog, the information similar to get(model, 'log') is returned
% about all newly added variables
% 
% @ last revision za Nov-2010


  islog = dbempty();

   % the following will be transformed as 100*(variable^4-1) %400*(variable-1)
  trans1_names = {'dot_y_star_trend_fund',...
                    'dot_y_star_trend',...
                    'dot_y_star',...
                    'dot_y_star_trend_shift',...
                    'i_star',...
                    'i_star_eq',...
                    'i_star_eu',...
                    'dot_pstar_other_tilde',...
                    'dot_pstar_energy_tilde',...
                    'dot_pstar_tilde',...
                    'dot_cpi_star_tilde',...
                    'dot_pstar_RP_tilde',...
                    'dot_usdeur',...
                    'prem_usdeur',...
                    'dot_z_eq',...
                    'shadow_rate_gap',...
                    'r_star_gap'};
  
  % the following will be transformed as 100*log(variable/variable_ss(=1))
   trans2_names = {'usdeur'};

%'Lambda',  'h', 'Mu', 'K', 'y', 'k', 'ell', ...
%                   'rmcn_other','rmcn_oil', 'kappa_tfp', 'kappa_inv', 'nX', 'yX', ...
%                   'rmcx', 'nC', 'yC', 'rmcc', 'nJ', 'rmcj', ...
%                   'yG', 'rmcg', 'pK', 'n_star_aQ', ...
%                   'p_star','rmcw','rmcy'};
  
  % the following will be transformed as 100*variable (are not in logs)
  trans4_names = {'b',...   
                'eps_y_star_gap',...
                'eps_dot_y_star_trend_fund',...
                'eps_dot_y_star_trend_shift',...
                'eps_Istar',...
                'eps_i_star_eq',...
                'eps_shadow_rate',...
                'eps_USDEUR',...
                'eps_dot_z_eq',...
                'eps_prem_usdeur',...
                'eps_pstar_other_tilde',...
                'eps_pstar_tilde',...
                'eps_pstar_energy_tilde',...
                'eps_pstar_RP_tilde',...
                'eps_dot_cpi_star_tilde'};

  % the following will be be transformed as 100*variable/(1+beta)
%   trans5_names = {'eps_costpushC', 'eps_labor', 'eps_costpushJ', ...
%                   'eps_costpushX', 'eps_costpushNother','eps_costpushNoil'};
%               
  % the following will be be transformed as 100*(variable-1)
  trans6_names = {'y_star_gap', 'z_gap', 'energy_share_ppi_star_gap'};       
  
  % the following will be transformed as 100*(variable*variable{-1}*variable{-2}*variable{-3}-1)
  trans7_names = {'dot_pstar_other_tilde',...
                    'dot_pstar_energy_tilde',...
                    'dot_pstar_tilde',...
                    'dot_cpi_star_tilde',...
                    'dot_usdeur'};
              
   % the following will be be transformed as 100*log(variable)
%   trans8_names = {'target'};     
  
  dout = d;
  
%   dout = dbbatch(dout, 'zz_$0', '400*(d.$0-1)', 'namelist', trans1_names);
  dout = dbbatch(dout, 'zz_$0', '100*(d.$0^4-1)', 'nameList', trans1_names);
  islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans1_names,'fresh',true));
  
  dout = dbbatch(dout, 'zz_$0', '100*log(d.$0)', 'nameList', trans2_names);
  islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans2_names,'fresh',true));

  dout = dbbatch(dout, 'zz_$0', '100*d.$0', 'nameList', trans4_names);
  islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans4_names,'fresh',true));

%   dout = dbbatch(dout, 'zz_$0', '100*d.$0/(1+p.beta)', 'nameList', trans5_names);
%   islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans5_names,'fresh',true));

  dout = dbbatch(dout, 'zz_$0', '100*(d.$0-1)', 'nameList', trans6_names);
  islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans6_names,'fresh',true));
  
  dout = dbbatch(dout, 'zz_$04', '100*(d.$0*d.$0{-1}*d.$0{-2}*d.$0{-3}-1)', 'nameList', trans7_names);
  islog = dbmerge(islog,dbbatch(dout, 'zz_$04', 'false', 'nameList', trans7_names,'fresh',true));

  %--add legend to Y-o-Y growts--%
  for i = 1:length(trans7_names)
      name        = ['zz_', trans7_names{i}, '4'];
	  if isfield(dout,name)
		dout.(name) = comment(dout.(name), strrep(get(dout.(name(1:end-1)),'comment'), 'QoQ', 'YoY'));
	  end
  end
  
%  dout = dbbatch(dout, 'zz_$0', '100*log(d.$0)', 'nameList', trans8_names);
%  islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans8_names,'fresh',true));
%   
  
  if nargout > 1
    varargout{1} = islog;
  end
