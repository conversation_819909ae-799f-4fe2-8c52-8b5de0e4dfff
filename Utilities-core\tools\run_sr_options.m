function sr_opt = run_sr_options(sr_opt)
% This function sets more ranges needed for sropt structure
% @ last revision ZH mar-2017

%%

disp(['Report: ' sr_opt.report_prefix]);

%% Pre-computed dates

sr_opt.start_pred   = sr_opt.ehist+1;
sr_opt.end_exo      = sr_opt.ehist;

%% Pre-computed ranges

sr_opt.hrng         = sr_opt.shist:sr_opt.ehist;				% filtering
sr_opt.fcastrng     = sr_opt.start_pred:sr_opt.end_pred;		% forecast
sr_opt.comprng      = sr_opt.start_pred:sr_opt.end_comp;		% computation of prediction

sr_opt.nstarrng     = sr_opt.start_pred:sr_opt.end_pred_long;	% foreign demand
sr_opt.istarrng     = sr_opt.start_pred:sr_opt.end_pred_long;   % foreign interest rate
sr_opt.pstarrng		= sr_opt.start_pred:sr_opt.end_pred_long;	% foreign inflation rate
sr_opt.regrng       = sr_opt.start_pred:sr_opt.end_pred;		% regulated prices

sr_opt.hardNTFrng   = sr_opt.start_pred:sr_opt.end_hardNTFfix;	% hard NTF fix (consumption, investment, export, import and wages)

sr_opt.grng			= sr_opt.start_pred:sr_opt.end_pred_long;	% real government
sr_opt.gprng		= sr_opt.start_pred:sr_opt.end_pred_long;	% nom. government
sr_opt.targetrng    = sr_opt.start_pred:sr_opt.end_pred_long;	% inflation target

sr_opt.usdeurrng    = sr_opt.start_pred:sr_opt.end_pred_long;	% USDEUR 
sr_opt.brentrng     = sr_opt.start_pred:sr_opt.end_pred_long;	% Brent USD price

%% Optional changes in pre-computed ranges

names = {'nstar','pstar','istar','reg','g','gp','target','usdeur','brent'};
for ix = 1: length(names)
    if isfield(sr_opt,['end_' names{ix}])
        sr_opt.([names{ix} 'rng']) = sr_opt.start_pred:sr_opt.(['end_' names{ix}]);
        sr_opt = rmfield(sr_opt,['end_' names{ix}]);
    end
end

%% Optional prefix extensions

if isfield(sr_opt,'prefix_ext')
    for ix = 1: size(sr_opt.prefix_ext,1);
        sr_opt.extend.(sr_opt.prefix_ext{ix,1}) = [sr_opt.report_prefix sr_opt.prefix_ext{ix,2}];
        disp(['Prefix ' sr_opt.prefix_ext{ix,1} ' for ' [sr_opt.report_prefix sr_opt.prefix_ext{ix,2}] ' was created.']);
    end
    sr_opt = rmfield(sr_opt,'prefix_ext');
end

end