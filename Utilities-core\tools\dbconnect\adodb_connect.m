function ado_connection=adodb_connect(connection_str, timeout)
%% FUNCTION NAME:
%   ado_connection =    adodb_connect(connection_str, timeout)
%
% DESCRIPTION:
%   Connects to ADO OLEDB using the Microsoft ActiveX Data Source Control
%
% INPUT:
%  connection_str - (string) containing information needed for connecting to
%   the database. See here
%    http://www.codeproject.com/KB/database/connectionstrings.aspx
%  timeout - CommandTimeout in seconds (default=60 seconds if unspecified)
%
% OUTPUT:
%    ado_connection - ADO connection object
%
% ASSUMPTIONS AND LIMITATIONS:
%   second argument may be empty
% ADO Documentation:
% ado_connection: http://www.w3schools.com/ado/ado_ref_connection.asp
% CursorLocation: http://www.w3schools.com/ado/prop_conn_cursorlocation.asp
% CommandTimeout: http://www.w3schools.com/ado/prop_conn_commandtimeout.asp
% Open:           http://www.w3schools.com/ado/met_conn_open.asp
% EXAMPLE:
%   Open DB connection without manual authentification, static needs to be
%   added in odbc
%   DB = adodb_connect('Provider=OraOLEDB.Oracle;Data Source=static;OSAuthent=1;');
% REVISION HISTORY:
%   $Revision: R2013b$
%   $Author: F. Brazdik$
%   $Date: April 30, 2020$
% REFERENCE
%   Jaroslaw Tuszynski (2020). adodb_tools (https://www.mathworks.com/matlabcentral/fileexchange/29615-adodb_tools), MATLAB Central File Exchange. Retrieved April 30, 2020.
%

if nargin<2, timeout = 60; end
ado_connection = actxserver('ADODB.Connection'); % Create activeX control
ado_connection.CursorLocation = 'adUseClient';   % Uses a client-side cursor supplied by a local cursor library
ado_connection.CommandTimeout = timeout;         % Specify connection timeout
ado_connection.Open(connection_str);             % Open connection


