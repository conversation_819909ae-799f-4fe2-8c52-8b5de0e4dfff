%% Startup a new Excel session...

excel = actxserver('Excel.Application');
excel.visible = 1;

% % Absolutni cesta k souboru textu plneni a XLS tabulkam
% cesta_ted = pwd;
% lomitka = findstr(cesta_ted,'\');
% 
%     % Excel
%     lomitka_vstup = findstr(Tablesfile,'\');
%     nazev_souboru_XLS = Tablesfile(lomitka_vstup(end)+1:end);

% Otevrit excel tabulku odkud se bude kopirovat
disp([sprintf('\n'),'Ted se otevira ''.xls'' pro tabulky na plneni,']);
disp(['...pokud to dlouho trva, tak je soubor nejspis otevreny']);
disp(['...a je potreba ho nejprve zavrit !!!']);

try 
	session_xls = invoke(excel.Workbooks,'Open',Tablesfile);
	disp([sprintf('\n'), '<PERSON><PERSON>vre<PERSON> ''.xls'' souboru OK...']);
catch
	disp([sprintf('\n'), 'Nepodarilo se otevrit ''.xls'' soubor s tabulkami do prezentace...']);
end
