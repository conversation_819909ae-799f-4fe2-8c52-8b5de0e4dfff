function [d_rep] = datamodel_replicate(d_orig, SS , varargin)

default =	{	...
	'check',			false, ...				%- do check
	'range_hist',		[], ...					%- historical range
	'range_fcast',		[], ...					%- fcast range
	'kalman',			[], ...					%- solved filtering model
	'model',			[], ...					%- solved simulating model
	'tunes',			dbempty, ...			%- tunes database
    'plan',             dbempty, ...			%- plan used for simulations  
    'anticipate',       true,...                %- default simulation opt (if needed)
	'expectations'		false, ...				%- limited rational expectations
	'limit',			1e-15,...				%- computation error limit
    };

options = passopt(default,varargin{1:end});

%% Checking input

if ~isempty(options.kalman)
	if isempty(options.range_hist)
		error('Range for filter replication is missing!');
	else
		repFilter = true;
		mh = options.kalman;
		range_hist = options.range_hist;
	end
else
	repFilter = false;
end
if ~isempty(options.model)
	if isempty(options.range_fcast)
		error('Range for forecast replication is missing!');
	else
		repFcast = true;
		mf = options.model;
		range_fcast = options.range_fcast;
		if isequal(options.plan, dbempty)
			options.plan = plan(mf, range_fcast);
		end
	end
else
	repFcast = false;
end
if ~repFilter && ~repFcast
	error('Replication model is missing!');
end

%% Replication

d = d_orig;

if repFilter % Filter replication
	
	yList = get(mh,'yList');
	eList = get(mh,'eList');
	xList = get(mh,'xList');
	d = d*yList;
	d = dbclip(d,range_hist);
	d = d*dbnames(d,'nameFilter','^obs_\w*');

	f = filterhistory(mh, d, options.tunes, SS, range_hist);
	f = f.mean;

	f = f*(dbnames(f)-dbnames(f,'nameFilter','^tune_\w*'));
	f = dbextend(f,options.tunes);
% 	% replace tunes in f_rep with the actual tunes
% 	tune_names = dbnames(tunes);
% 	var_names = strrep(tune_names,'tune_','');
% 	for i = 1:length(tune_names)
% 		rng = tunes.(tune_names{i}).range;
% 		rng_rest = rng(isnan(tunes.(tune_names{i})(rng)));
% 		rng = rng(~isnan(tunes.(tune_names{i})(rng)));
% 		f.(tune_names{i}) = tseries;
% 		f.(tune_names{i})(rng) = f.(var_names{i});
% 		f.(tune_names{i})(rng_rest) = NaN;
% 	end

	f = dbclip(f, range_hist);
	d = dbextend(d, f);

	if repFcast
		eps_names = dbnames(d_orig,'nameFilter','^eps_\w*');
		d_eps = dbclip(d_orig*eps_names,range_fcast);
		d = dbextend(d,d_eps);
 		d = d*[yList,eList,xList];
    else
        d_rep = d*[yList,eList,xList];%d_rep = d; 
	end
	
end

if repFcast % Forecast replication
	
	eList = get(mf,'eList');
	xList = get(mf,'xList');
	exog = get_exogenized(options.plan);
	exog_names = dbnames(exog);
	for ix = 1:length(exog_names)
		rng = range(exog.(exog_names{ix}));
		d.(exog_names{ix})(rng) = d_orig.(exog_names{ix})(rng);
	end
	%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% 	disp('datamodel_replicate: POKUS O OPRAVU KVOLI SHADOW RATES!!! Potom odstranit...');
% 	if isfield(d_orig,'i_star');
% 		d.i_star = d_orig.i_star;
% 	end
	%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
	if isa(options.expectations,'expDesign')
		d_rep = simulate(mf, d, range_fcast, ...
			'plan', options.plan,...
			'anticipate', options.anticipate, ...
			'expectations', options.expectations);
	else
		d_rep = simulate(mf, d, range_fcast, ...
			'plan', options.plan,...
			'anticipate', options.anticipate);
	end
	d_rep = d_rep*(dbnames(d_rep) - ...
		dbnames(d_rep,'nameFilter','^tune_\w*') - ...
		dbnames(d_rep,'nameFilter','^mes_\w*') - ...
		dbnames(d_rep,'nameFilter','^omega_\w*') - ...
		dbnames(d_rep,'nameFilter','^obs_\w*'));
	d_rep = dbextend(d, d_rep);
	if isequal(exist('yList','var'),1)
		d_rep = d_rep*[yList,eList,xList];
	else d_rep = d_rep*[eList,xList];
	end
	
end

if options.check
	
	if repFilter && ~repFcast
		d_orig = dbclip(d_orig,range_hist);
	end
	if ~repFilter && repFcast
		d_orig = dbclip(d_orig,range_fcast);
		d_orig = d_orig*(dbnames(d_orig) - ...
			dbnames(d_orig,'nameFilter','^tune_\w*') - ...
			dbnames(d_orig,'nameFilter','^mes_\w*') - ...
			dbnames(d_orig,'nameFilter','^omega_\w*') - ...
			dbnames(d_orig,'nameFilter','^obs_\w*'));
	end
	
	d_aux = dbfun(@minus,d_orig,d_rep,'fresh',true);
	d_aux = dbfun(@abs,d_aux);
    %d_aux = dbclip(d_aux,range_hist(1):range_hist(end));
	d_aux = dbfun(@max,d_aux);

	list_ = fieldnames(d_aux);
	sum_ = 0;
	for i = 1:length(list_)
        if isempty(d_aux.(list_{i})) 
        else sum_ = sum_ + d_aux.(list_{i});
          % if d_aux.(list_{i}) > 0.001 disp([list_{i}, ': ', num2str(d_aux.(list_{i}))]); end
        end
	end
	if sum_<options.limit
% 		disp(['Forecast replicated, sum: ' num2str(sum_)]);
		d_rep.modelchange = false;
    else
		warning(['Replication failed! Sum: ' num2str(sum_)]);
		d_rep.modelchange = true;
	end
	
end
