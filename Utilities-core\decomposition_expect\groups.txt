groups_nms = {
	'mod'	'Zmeny modelu'	'Model Changes'
	'is'	'Revize a vlada'	'Revisions and Government'
	'foreign'	'Zahranici'	'Foreign'
	'reg'	'Regulovane ceny'	'Administered Prices'
	'rest'	'Ostatni'	'Rest'
};
groups = { ...
	{'all__model__new_model_change', ...
		'all__model__old_model_change', ...
			}, ...
	{'all__fixexptra__dot_g', ...
		'all__fixexptra__dot_gp', ...
		'all__fixexptra__target', ...
		'all__fixsurtra__dot_g', ...
		'all__fixsurtra__dot_gp', ...
		'all__fixsurtra__target', ...
		'all__obsrev__obs_C', ...
		'all__obsrev__obs_CPI', ...
		'all__obsrev__obs_GP', ...
		'all__obsrev__obs_I', ...
		'all__obsrev__obs_J', ...
		'all__obsrev__obs_L', ...
		'all__obsrev__obs_N', ...
		'all__obsrev__obs_N_STAR', ...
		'all__obsrev__obs_P', ...
		'all__obsrev__obs_PC', ...
		'all__obsrev__obs_PG', ...
		'all__obsrev__obs_PJ', ...
		'all__obsrev__obs_PN', ...
		'all__obsrev__obs_PREG', ...
		'all__obsrev__obs_PX', ...
		'all__obsrev__obs_P_STAR_TILDE', ...
		'all__obsrev__obs_S', ...
		'all__obsrev__obs_W', ...
		'all__obsrev__obs_X', ...
		'all__obsrls__eps_aG', ...
		'all__obsrls__eps_costpushG', ...
		'all__obsrls__eps_gov', ...
		'all__obsrls__eps_kappa_aG', ...
		'all__obsrls__eps_target', ...
		'all__obsrls__obs_GP', ...
		'all__obsrls__obs_PG', ...
		'all__obsrls__obs_TARGET4', ...
		'all__resexpnew__eps_target', ...
		'all__resexpold__eps_target', ...
		'all__resexptra__eps_target', ...
		'all__ressurtra__eps_costpushG', ...
		'all__ressurtra__eps_gov', ...
		'all__tunesnew__tune_b', ...
		'all__tunesnew__tune_k', ...
		'all__tunesnew__tune_kappa_costpushC', ...
		'all__tunesold__tune_b', ...
		'all__tunesold__tune_c', ...
		'all__tunesold__tune_dot_pN', ...
		'all__tunesold__tune_dot_pY', ...
		'all__tunesold__tune_k', ...
		'all__tunesold__tune_kappa_costpushC', ...
		'all__tunesrls__tune_dot_pG', ...
			}, ...
	{'all__fixexptra__dot_n_star', ...
		'all__fixexptra__dot_p_star_tilde', ...
		'all__fixsurtra__dot_n_star', ...
		'all__fixsurtra__dot_p_star_tilde', ...
		'all__fixsurtra__i_star', ...
		'all__obsrls__eps_Istar', ...
		'all__obsrls__eps_Nstar', ...
		'all__obsrls__eps_Pstar', ...
		'all__obsrls__obs_I_STAR', ...
		'all__obsrls__obs_N_STAR', ...
		'all__obsrls__obs_P_STAR_TILDE', ...
		'all__resexptra__eps_Nstar', ...
		'all__resexptra__eps_Pstar', ...
			}, ...
	{'all__fixsurtra__dot_pREG', ...
		'all__obsrls__eps_pREG', ...
		'all__obsrls__obs_PREG', ...
		'all__ressurtra__eps_pREG', ...
			}, ...
	{'all__fixexptra__i_star', ...
		'all__inimod__rls_exp_change', ...
		'all__obsrls__eps_A', ...
		'all__obsrls__eps_XD', ...
		'all__obsrls__eps_aJ', ...
		'all__obsrls__eps_aL', ...
		'all__obsrls__eps_aO', ...
		'all__obsrls__eps_aQ', ...
		'all__obsrls__eps_aX', ...
		'all__obsrls__eps_aux_pc', ...
		'all__obsrls__eps_costpushC', ...
		'all__obsrls__eps_costpushJ', ...
		'all__obsrls__eps_costpushN', ...
		'all__obsrls__eps_costpushX', ...
		'all__obsrls__eps_costpushY', ...
		'all__obsrls__eps_habit', ...
		'all__obsrls__eps_inv', ...
		'all__obsrls__eps_kappa_aJ', ...
		'all__obsrls__eps_labor', ...
		'all__obsrls__eps_mpolicy', ...
		'all__obsrls__eps_prem', ...
		'all__obsrls__eps_tfp', ...
		'all__obsrls__eps_uip', ...
		'all__obsrls__eps_wedge_euler', ...
		'all__obsrls__obs_C', ...
		'all__obsrls__obs_CPI', ...
		'all__obsrls__obs_I', ...
		'all__obsrls__obs_J', ...
		'all__obsrls__obs_L', ...
		'all__obsrls__obs_N', ...
		'all__obsrls__obs_P', ...
		'all__obsrls__obs_PC', ...
		'all__obsrls__obs_PJ', ...
		'all__obsrls__obs_PN', ...
		'all__obsrls__obs_PX', ...
		'all__obsrls__obs_S', ...
		'all__obsrls__obs_W', ...
		'all__obsrls__obs_X', ...
		'all__resexpnew__eps_Istar', ...
		'all__resexpnew__eps_Nstar', ...
		'all__resexpnew__eps_Pstar', ...
		'all__resexpnew__eps_aX', ...
		'all__resexpnew__eps_costpushC', ...
		'all__resexpnew__eps_uip', ...
		'all__resexpold__eps_Istar', ...
		'all__resexpold__eps_Nstar', ...
		'all__resexpold__eps_Pstar', ...
		'all__resexptra__eps_A', ...
		'all__resexptra__eps_Istar', ...
		'all__resexptra__eps_aX', ...
		'all__resexptra__eps_costpushC', ...
		'all__resexptra__eps_uip', ...
		'all__ressurnew__eps_A', ...
		'all__ressurnew__eps_Nstar', ...
		'all__ressurnew__eps_XD', ...
		'all__ressurnew__eps_aux_pc', ...
		'all__ressurnew__eps_costpushC', ...
		'all__ressurnew__eps_costpushG', ...
		'all__ressurnew__eps_costpushJ', ...
		'all__ressurnew__eps_costpushX', ...
		'all__ressurnew__eps_gov', ...
		'all__ressurnew__eps_habit', ...
		'all__ressurnew__eps_kappa_aJ', ...
		'all__ressurnew__eps_pREG', ...
		'all__ressurnew__eps_uip', ...
		'all__ressurold__eps_costpushG', ...
		'all__ressurold__eps_gov', ...
		'all__ressurold__eps_pREG', ...
		'all__ressurtra__eps_costpushC', ...
		'all__ressurtra__eps_habit', ...
		'all__ressurtra__eps_uip', ...
		'all__tunesrls__tune_c', ...
		'all__tunesrls__tune_dot_aJ', ...
		'all__tunesrls__tune_dot_aO', ...
		'all__tunesrls__tune_dot_aX', ...
		'all__tunesrls__tune_kappa_costpushC', ...
			}, ...
};
