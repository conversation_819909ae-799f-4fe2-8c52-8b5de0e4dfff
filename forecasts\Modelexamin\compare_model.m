
function [found_diff] = compare_model(List_small, List_full, ss_small, ss_full)

    occurence = ismember(List_small,List_full);

    found_diff(1,:) = {'variable/parameter', 'small model','large model'};
    ii=2;
    for i=1:length(List_small)
        comp_name = List_small{i};
        if occurence(i)==1 % in the case that parameter is in both model, comapare them
            value_diff = ss_small.(comp_name)-ss_full.(comp_name); % check difference in values
            if value_diff > 9e-5 || value_diff < -9e-5  %~= 0
                found_diff(ii,:) = {comp_name ss_small.(comp_name) ss_full.(comp_name)}; % if value of parameter varies, report it
                ii=ii+1;
            end
        else
            found_diff(ii,:) = {List_small{i} ss_small.(comp_name) 'only in small model'}; % report parameter if it is only in a small model
            ii=ii+1;
        end
    end


end
