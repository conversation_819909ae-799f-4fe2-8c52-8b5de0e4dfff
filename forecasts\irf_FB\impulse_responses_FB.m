function [irf,m] = impulse_responses(inputList,shockList,responseList,shockType,sr_opt)

% @ last revision: JZ, May 2020

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Run impulse responses

% INPUT
% inputList			- cell array of strings 
%                   - 1st column - report prefixes of model object files
%                   - 2nd column - nicknames of models
% shockList         - cell array of strings - names of shock variables
% responseList      - cell array of strings - names of response variables
% shockType         - string identifier for type of shocks 
% sr_options        - options for IRF (includes sr_options as well as 
%                     more options from driver_irf
% 
% OUTPUT
% irf               - structure array of all impulse responses
%                     irf.(model).(shock_type).(shock).(response)

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%% Load models

% Models ready to load
% e.g. m.m01 = load([sr_opt.outdata_dir '\' '2011sz01-model.mat']);

[numModel, ~] = size(inputList);

if numModel > 0
    modelID			= cell(1,numModel);
    modelNick		= cell(1,numModel);
    for ix = 1 : numModel
        opt = load_settings(inputList{ix});
        modelID{ix}     = ['m',num2str(ix,'%02.0f')];  % e.g. 'm01'
        modelNick{ix}   = inputList{ix,2};
        aux = load(opt.model_fb_name);
        m.(modelID{ix}) = aux.m;
        aux_m = struct();
        aux_m.(modelID{ix}) = aux.m;
        ListOfAllShocks.(modelID{ix}) = doAllList(aux_m,'shock');
        if ~isa(m.(modelID{ix}),'model')
            error(['Model ' modelNick{ix} ' in ' opt.model_name ' is not valid model object!']);
        end
        if isfield(opt,'expectations_fb_name')
            aux = load(opt.expectations_fb_name);
            expectations.(modelID{ix}) = aux.expect_scheme;           
            expect_scheme_list.(modelID{ix}) = expectations.(modelID{ix}).cluster{1};
            for ii = 2:length(expectations.(modelID{ix}).cluster)
                expect_scheme_list.(modelID{ix}) = [expect_scheme_list.(modelID{ix}) expectations.(modelID{ix}).cluster{ii}];
            end
            if ~isa(expectations.(modelID{ix}),'expDesign')
                error(['Expectations design in ' opt.model_name ' is not valid model object!']);
            end
        else expectations.(modelID{ix}) = false;
        end
        % 		ss.(modelID{ix}) = load(opt.ss_name);
    end
end

%% Check shock type

switch lower(shockType)
    case {'e','exp','expect','expected'}
        disp('Shock type selected: expected (scheme).');
        typeShock.expect = true;
    case {'ef','expfull','expectfull','expectedfull'}
        disp('Shock type selected: expected (fully).');
        typeShock.expect = true;
    case {'u','ue','unexp','unexpect','unexpected'}
        disp('Shock type selected: unexpected.');
        typeShock.unexpect = true;
    case {'b','both','all'}
        disp('Shock type selected: expected (scheme) and unexpected.');
        typeShock.expect = true;
        typeShock.unexpect = true;
    otherwise
        error('Invalid shock type selected!');
end
typeShockID = fieldnames(typeShock);


%% Check shocks and response names

% Shocks

% Rename shocks to compare g3+ and g3++


if ~isempty(intersect(shockList,'all'))
    disp('All shocks selected.');
    shockList = doAllList(m,'shock');
end

shockList(strncmpi(shockList,'eps_exp',7))  = []; % remove shocks with eps_exp in the name; will be added later in a special list
shockList(strcmp(shockList,'eps_aJnp_fi'))  = []; % remove FI shocks 
shockList(strcmp(shockList,'eps_habit_fi')) = []; % remove FI shocks
shockList(strcmp(shockList,'eps_habit_covid')) = []; % remove covid shocks

specShockList = analyseVarList(m,modelNick,shockList,'shock');

% add eps_exp shocks if present in given model
% for i = 1 : numModel
%     for s = 1:length(specShockList.(modelID{i}))
%         shock_name = specShockList.(modelID{i}){s};
%         if (length(shock_name)<7)||(~strcmp(shock_name(5:7),'exp')) 
%             exp_shock_name = ['eps_exp_',shock_name(5:end)];            
%             if max(cell2mat(strfind(ListOfAllShocks.(modelID{i}),exp_shock_name)))==1
%                 listLength = length(specShockList.(modelID{i})); 
%                 specShockList.(modelID{i}){listLength+1,1}=exp_shock_name;
%             end
%         end
%     end
% end
    
% Responses
if ~isempty(intersect(responseList,'all'))
    disp('All responses selected.');
    responseList = doAllList(m,'response');
end
specResponseList = analyseVarList(m,modelNick,responseList,'response');

%% Impulse responses
% irf.modelID.typeShockID.hitPeriodID.shockList.responseList
shockListGlobal = shockList;
for i = 1 : numModel
    for j = 1 : length(typeShockID)
        shockList = shockListGlobal;
        if typeShock.(typeShockID{j})
            % when calculating expected shock IRF, switch to eps_exp shock
            % if defined in given model
            %             if strcmp(typeShockID{j},'expect')
            %                 for s=1:length(shockList)
            %                     shock_name = char(shockList{s});
            %                     if (length(shock_name)<7)||(~strcmp(shock_name(5:7),'exp'))
            %                         exp_shock_name = ['eps_exp_',shock_name(5:end)];
            %                         if max(cell2mat(strfind(ListOfAllShocks.(modelID{i}),exp_shock_name)))==1
            %                             shockList{s}=exp_shock_name;
            %                         end
            %                     end
            %                 end
            %             end
            auxShockList = setdiff(shockList,specShockList.(modelID{i}));
            if strcmp(typeShockID{j},'unexpect') % remove shocks with eps_exp in the name; will be added later if needed
                auxShockList(strncmpi(auxShockList,'eps_exp',7)) = [];
            elseif strcmp(typeShockID{j},'expect') && any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType)) && ~isempty(expectations.(modelID{i})) % replace eps shocks with exp_eps shockskeep shocks with eps_exp; will be added later in a special list
%                 auxShockList = auxShockList(~cellfun(@(s)isempty(regexp(s,'eps_exp', 'once')),auxShockList));
                auxShockList = strrep(auxShockList,'eps_', 'eps_exp_');
                if sum(strcmp(auxShockList,'eps_exp_target')) > 0
                    auxShockList(strcmp(auxShockList,'eps_exp_target')) = [];
                    auxShockList{end+1} = 'eps_target';
                end
            elseif strcmp(typeShockID{j},'expect') && (any(strcmpi({'e','exp','expect','expected'},shockType)) || any(strcmpi({'b','both','all'},shockType))) && ~isempty(expectations.(modelID{i}))
                shocksToAdd = strrep(shockList,'eps_', 'eps_exp_');
                auxShockList = [auxShockList; shocksToAdd];
                shocksToRemove = setdiff(auxShockList,expect_scheme_list.(modelID{i}));
                auxShockList = setdiff(auxShockList,shocksToRemove);
                expect_scheme_list.(modelID{i})(strncmpi(expect_scheme_list.(modelID{i}),'eps_exp',7)) = [];
                expect_scheme_list.(modelID{i}) = strrep(expect_scheme_list.(modelID{i}),'eps_', 'eps_exp_');
                auxShockList = setdiff(auxShockList,expect_scheme_list.(modelID{i}));
            end
            
            auxResponseList = setdiff(responseList,specResponseList.(modelID{i}));
            if isempty(auxShockList) || isempty(auxResponseList)
                error(['No shocks or responses exist for model ' modelNick{i} '!']);
            end
            hitPeriodID = cell(1,length(sr_opt.hitPeriod));
            for k = 1 : length(sr_opt.hitPeriod)
                hitPeriodID{k} = ['hit' num2str(sr_opt.hitPeriod(k),'%02.0f') 'Q'];
                irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{k}) = compute_irf_FB(m.(modelID{i}), ...
                    auxShockList,auxResponseList, ...
                    'runs',sr_opt.responseLength,...
                    'shift',sr_opt.hitPeriod(k)-1,...
                    'anticipate',isequal(typeShockID{j},'expect'),...
                    'expectations',expectations.(modelID{i}),...
                    'transform',sr_opt.varTransform, ...
                    'shocksize',sr_opt.shocksize ...
                    );
            end
            if isfield(sr_opt,'missingPeriod')
                hitPeriodID = cell(1,length(sr_opt.missingPeriod));
                for k = 1 : length(sr_opt.missingPeriod)
                    hitPeriodID{k} = ['hit' num2str(sr_opt.missingPeriod(k),'%02.0f') 'Q'];
                    irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{k}) = compute_irf(m.(modelID{i}), ...
                        auxShockList,auxResponseList, ...
                        'runs',sr_opt.responseLength,...
                        'shift',sr_opt.missingPeriod(k)-1,...
                        'anticipate',isequal(typeShockID{j},'expect'),...
                        'expectations',expectations.(modelID{i}),...
                        'transform',sr_opt.varTransform, ...
                        'shocksize',sr_opt.shocksize ...
                        );
                end
            end
        end
        dbnan = dbempty;
        for k = 1 : length(specResponseList.(modelID{i}))
            dbnan.(specResponseList.(modelID{i}){k}) = tseries();
            if sr_opt.varTransform
                dbnan.(['zz_' specResponseList.(modelID{i}){k}]) = tseries();
            end
        end
        for k = 1 : length(hitPeriodID)
            for l = 1 : length(auxShockList)
                irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{k}).(auxShockList{l}) = ...
                    dbmerge(irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{k}).(auxShockList{l}),dbnan);
            end
        end
        dbnan = dbempty;
        for k = 1 : length(responseList)
            dbnan.(responseList{k}) = tseries();
            if sr_opt.varTransform
                dbnan.(['zz_' responseList{k}]) = tseries();
            end
        end
        for k = 1 : length(specShockList.(modelID{i}))
            irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{1}).(specShockList.(modelID{i}){k}) = dbnan;
            irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{1}).(specShockList.(modelID{i}){k}).(specShockList.(modelID{i}){k}) = ...
                tseries();
        end
        % when calculating expected shock IRF to eps_exp shock, rename the
        % field in irf structure to eps only (needed due to mk_report_irf, mk_graph_irf issues)
        if strcmp(typeShockID{j},'expect') && (any(strcmpi({'e','exp','expect','expected'},shockType)) || any(strcmpi({'b','both','all'},shockType)))
            try
                for s=1:length(auxShockList)
                    shock_name = char(auxShockList{s});
                    if (length(shock_name)>=7)&&(strcmp(shock_name(5:7),'exp'))
                        unexp_shock_name = ['eps_',shock_name(9:end)];
                        for h = 1:length(hitPeriodID)
                            irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{h}).(shock_name).(unexp_shock_name) = [];
                            irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{h}).(shock_name).(unexp_shock_name) = irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{h}).(shock_name).(shock_name);
                            irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{h}).(unexp_shock_name) = irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{h}).(shock_name);
                            irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{h}) = rmfield(irf.(modelID{i}).(typeShockID{j}).(hitPeriodID{h}), shock_name);
                        end
                    end
                end
            catch
            end
        end
    end
end

% shockList = shockListGlobal;
shockList = auxShockList;

%% Save results
save([sr_opt.new.outdata_dir '\' 'IRF.mat'], 'irf');
if sr_opt.doSaving
    disp(['Storing results in the directory: ' sprintf('\n') absolute_path(sr_opt.new.outdata_dir)]);
    for i = 1 : numModel
%         keyboard;
        eval(['irf_' modelID{i} ' = irfTransform4Save(irf.' modelID{i} ');']);
        dbsave(eval(['irf_' modelID{i}]),...
            [sr_opt.new.outdata_dir '\' 'IRF_' modelNick{i} '.csv']);
        [data, result] = readtext([sr_opt.new.outdata_dir '\' 'IRF_' modelNick{i} '.csv'], ',','','"');
        for j = 1:size(data,2)
            data(2,j) = strrep(data(2,j),'"','');
        end
        if exist([sr_opt.new.outdata_dir '\' 'IRF_' modelNick{i} '.xls'], 'file')==2
            delete([sr_opt.new.outdata_dir '\' 'IRF_' modelNick{i} '.xls']);
        end
        [SUCCESS,MESSAGE] = xlswrite([sr_opt.new.outdata_dir '\' 'IRF_' modelNick{i} '.xls'], data);
    end
end

%% Graphs
if sr_opt.doGraphs || sr_opt.doPPT
    disp('Drawing Matlab graphs.');
    if sr_opt.doPPT
        disp(['Storing PPT file in the directory: ' sprintf('\n') absolute_path(sr_opt.new.outgraph_dir)]);
    end
    mk_graph_irf_FB(irf,shockList,specShockList,responseList,specResponseList, ...
                 modelNick,sr_opt);
end

%% Report

if sr_opt.doReport
    disp(['Reporting results in the directory: ' sprintf('\n') absolute_path(sr_opt.new.outreport_dir)]);
    mk_report_irf_FB(irf,shockType,shockList,specShockList,responseList,specResponseList, ...
                  modelNick,sr_opt,expectations,...
                  [sr_opt.new.outreport_dir '\' 'IRF_FB' '.pdf']);
end





%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% --------------------------------------------------------------------- %%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function [m] = createModelObject(strPathName,SS)

index = max([findstr(strPathName,'\')  findstr(strPathName,'/')]);
strPath = strPathName(1:index-1);
strName = strPathName(index+1:end-2);

currentPath = cd;
cd(strPath);
eval(char(['SS = ' strName '(SS);']));
cd(currentPath);
m = readmodel(SS);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function varList = doAllList(m,varID)

% Generates list of model variables of type varID from all models in m

switch varID
    case 'shock'
        modelAttrib = 'enames';
    case 'response'
        modelAttrib = 'xnames';
    otherwise
        error('doAllList: Incorrect input in varID!');
end

modelID = fieldnames(m);
varList = {};
for i = 1 : length(modelID)
    varNames = [get(m.(modelID{i}),modelAttrib)]';
    varList = union(varList,varNames);
end
% if isequal(varID,'response')
%     varList = union(varList,'dot_gdp');
% end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function specVarList = analyseVarList(m,mNick,list,varID)

% Generates warning when some variable from list not involved in particular model in m

switch varID
    case 'shock'
        modelAttrib = 'eList';
    case 'response'
        modelAttrib = 'xList';
    otherwise
        error('doAllList: Incorrect input in varID!');
end

modelID = fieldnames(m);
numModel = length(modelID);

specVarList = struct();

varNamesUnion = {};
for i = 1 : numModel
    varNames = get(m.(modelID{i}),modelAttrib);
    varNamesUnion = union(varNamesUnion,varNames);
 	specVarList.(modelID{i}) = setdiff(list,varNames);
	if ~isempty(specVarList.(modelID{i}))
        disp(['No such ' varID 's in model ' mNick{i} ':']);
        disp(specVarList.(modelID{i}));
    end
end
% if ~isempty(setdiff(list,varNamesUnion))
%     disp(['These ' varID 's are not in any model:']);
%     disp(setdiff(list,varNamesUnion));
%     error(['Check ' varID 's!']);
% end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function irf_model = irfTransform4Save(irfM)

typeShockID		= fieldnames(irfM);
hitPeriod		= fieldnames(irfM.(typeShockID{1}));
shockListM		= fieldnames(irfM.(typeShockID{1}).(hitPeriod{1}));
responseListM	= setdiff(fieldnames(irfM.(typeShockID{1}).(hitPeriod{1}).(shockListM{1})),shockListM);

for i = 1 : length(typeShockID)
    for j = 1 : length(shockListM)
        for k = 1 : length(responseListM)
			for l = length(hitPeriod)
				if isequal(shockListM{j},responseListM{k})
					strName = [shockListM{j}];
					strComment = [irfM.(typeShockID{i}).(hitPeriod{l}).(shockListM{j}).(shockListM{j}).comment ... (Shock Comment)
						];
				else
					strName = [typeShockID{i} '_' shockListM{j} '_TO_' responseListM{k} '_IN_' hitPeriod{l}(4:end)];
					strComment = [upper(typeShockID{i}(1)) typeShockID{i}(2:end) 'ed ' ...  Expected
						char(irfM.(typeShockID{i}).(hitPeriod{l}).(shockListM{j}).(shockListM{j}).comment) ... (Shock Comment)
						' To ' ... To
						char(irfM.(typeShockID{i}).(hitPeriod{l}).(shockListM{j}).(responseListM{k}).comment) ... (Variable Comment)
						' In ' hitPeriod{l}(4:end-1) '. Quarter'];
				end
				irf_model.(strName) = irfM.(typeShockID{i}).(hitPeriod{l}).(shockListM{j}).(responseListM{k});
				irf_model.(strName) = comment(irf_model.(strName),strComment);
			end
        end
    end
end

