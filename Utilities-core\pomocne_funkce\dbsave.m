function saved = dbsave(d,fname,dates,varargin)
% DBSAVE  Save database as CSV file.
%
% -Syntax
%
%   list = dbsave(d,fname)
%   list = dbsave(d,fname,dates,...)
%
% -Output arguments
%
% * |list| [ cellstr ] - - List of actually saved database entries.
%
% -Input arguments
%
% * |d| [ struct ] - Database whose tseries and numeric entries will be
% saved.
%
% * fname [ char ] - Filename under which the CSV will be saved, including
% its extension.
%
% * dates [ numeric | *Inf* ] Dates or date range on which the tseries
% objects will be saved.
%
% -Options
%
% * |'class'| [ *true* | false ] - Include row with class specification.
%
% * |'comment'| [ *true*| false ] - Include comments for tseries objects.
%
% * |'decimal'| [ numeric | *empty* ] - Number of decimals up to which the
% data will be saved; if empty the |'format'| option is used.
%
% * |'format'| [ char | *'%.8e'* ] Numeric format that will be used to
% represent the data, see |sprintf| for details on formatting, The format
% must start with a |'%'|, and must not include identifiers specifying
% order of processing, i.e. the |'$'| signs, or left-justify flags, the
% |'-'| signs.
%
% * |'freqletters'| [ char | *'YHQBM'* ] - Five letters to represent the
% five possible date frequencies (annual, semi-annual, quarterly,
% bimonthly, monthly).
%
% * |'nan'| [ char | *'NaN'* ] - String that will be used to represent
% NaNs.
%
% -Description
%
% The data saved include also imaginary parts of complex numbers.
%
% -Example
%

% -The IRIS Toolbox.
% -Copyright (c) 2007-2011 Jaromir Benes.
    
    % Allow both dbsave(d,fname) and dbsave(fname,d).
    if ischar(d) && isstruct(fname)
        [d,fname] = deal(fname,d);
    end
    
    if nargin < 3
        dates = Inf;
    end
    
    % Parse input arguments.
    P = inputParser();
    P.addRequired('d',@isstruct);
    P.addRequired('fname',@ischar);
    P.addRequired('dates',@isnumeric);
    P.parse(d,fname,dates);
    
    % Parse options.
    options = passvalopt('data.dbsave',varargin{:});
    
    % Run dates/datdefaults to substitute the default (irisget) date format
    % options for 'config'.
    options = datdefaults(options);
    
    % Remove double quotes from the date format string. This is because the
    % double quotes are used to delimit the CSV cells.
    [flag,options.dateformat] = strfun.findremove(options.dateformat,'"');
    if flag
        warning('iris:data', ...
            '\n*** Double quotes removed from date format string.');
    end
    
    % Set up the formatting string.
    if isempty(options.decimal)
        format = options.format;
    else
        format = ['%.',sprintf('%g',options.decimal),'f'];
    end
    
% *************************************************************************
    
    if isequal(dates,Inf)
        dates = dbrange(d);
    else
        dates = dates(:)';
    end
    
    list = fieldnames(d)';
    
    saved = {};
    data = nan([length(dates),1]);
    nameRow = {};
    classRow = {};
    commentRow = {};
    savedIndex = false(size(list));
    
    for i = 1 : numel(list)
        if istseries(d.(list{i}))
            tmpdata = d.(list{i})(dates);
            tmpcomment = comment(d.(list{i}));
            savedIndex(i) = true;
            tmpclass = 'tseries';
        elseif isnumeric(d.(list{i}))
            tmpdata = d.(list{i});
            tmpcomment = {''};
            savedIndex(i) = true;
            tmpclass = class(d.(list{i}));
        else
            continue
        end
        tmpdata = double(tmpdata);
        tmpsize = size(tmpdata);
        tmpdata = tmpdata(:,:);
        [tmprows,tmpcols] = size(tmpdata);
        if tmpcols == 0
            continue
        elseif tmpcols > 1
            tmpcomment(end+1:tmpcols) = {''};
        end
        % Add data, expand first dimension if necessary.
        nRows = size(data,1);
        if nRows < tmprows
            if ~any(isnan(imag(data)) | imag(data) ~= 0,1)
                data(end+1:tmprows,:) = NaN+NaN*1i;
            else
                data(end+1:tmprows,:) = NaN;
            end    
        elseif size(data,1) > tmpsize(1)
            if ~any(isnan(imag(data)) | imag(data) ~= 0,1)
                tmpdata(end+1:nRows,:) = NaN+NaN*1i;
            else
                tmpdata(end+1:nRows,:) = NaN;
            end    
        end
        data = [data,tmpdata];
        nameRow{end+1} = list{i};
        classRow{end+1} = [tmpclass,sprintf('[%g]',tmpsize)];
        commentRow(end+(1:tmpcols)) = tmpcomment;
        if tmpcols > 1
            nameRow(end+(1:tmpcols-1)) = {''};
            classRow(end+(1:tmpcols-1)) = {''};
        end
    end
    data(:,1) = [];
    
    if isempty(data)
        return
    end
    saved = list(savedIndex);
    
    % Create an empty buffer.
    c = '';
    nl = sprintf('\n');
    
    % Write name row.
    c = [c,'""',sprintf(',"%s"',nameRow{:})];
    
    % Write comments.
    if options.comment
        c = [c,nl,'Comments',sprintf(',"%s"',commentRow{:})];
    end
    
    % Write classes.
    if options.class
        c = [c,nl,'Class[Size]',sprintf(',"%s"',classRow{:})];
    end
    
    % Create cellstr with date strings.
    dates = dat2str(dates(:),options);
    ndates = length(dates);
    
    % Handle escape characters.
    dates = strrep(dates,'\','\\');
    dates = strrep(dates,'%','%%');
    
    % Create format string fot the imaginary parts of data; they need to be
    % always printed with a plus or minus sign.
    iformat = [format,'i'];
    if isempty(strfind(iformat,'%+')) && isempty(strfind(iformat,'%0+'))
        iformat = strrep(iformat,'%','%+');
    end
        
    % Find columns that have at least one non-zero imag. These column will
    % be printed as complex numbers.
    if any(isnan(imag(data)) | imag(data) ~= 0,1)
        data(isnan(real(data)) & (isnan(imag(data)) | imag(data)==0))=NaN+NaN*1i;
    end    
    nrow = size(data,1);
    ncol = size(data,2);
    % Combine real and imag columns in an extended data matrix.
    xdata = zeros(nrow,2*ncol);
    xdata(:,1:2:end) = real(data);
    idata = imag(data);
    xdata(:,2:2:end) = idata;
    % Find imag columns and create positions of zero-only imag columns that
    % will be removed.
    icol = any(isnan(idata) | idata ~= 0,1);
    removecol = 2*(1 : ncol);
    removecol(icol) = [];
    % Remove zero-only imag columns from the extended data matrix.
    xdata(:,removecol) = [];
    % Create a sequence of formats for one line.
    formatline = cell(1,ncol);
    % Format string for columns that have imaginary numbers.
    formatline(icol) = {[',',format,iformat]};
    % Format string for columns that only have real numbers.
    formatline(~icol) = {[',',format]};
    formatline = [formatline{:}];
        
    % We must create a format line for each date because the date strings
    % vary.
    br = sprintf('\n');
    formatData = '';
    for i = 1 : size(data,1)
        if i <= ndates
            thisDate = ['"',dates{i},'"'];
        else
            thisDate = '""';
        end
        formatData = [formatData,br,thisDate,formatline]; %#ok<AGROW>
    end
    cc = sprintf(formatData,xdata.');

    % control for NaNs in complex parts
    cc = strrep(cc,'NaNi','+NaNi');    
    % Replace NaNs in the date/data matrix with a user-supplied string. We
    % don't protect NaNs in date strings; these too will be replaced.
    if ~strcmpi(options.nan,'NaN')
        cc = strrep(cc,'NaN',options.nan);
    end
    
    % Splice the headings and the data, and save the buffer. No need to put
    % a line break between |c| and |cc| because that the |cc| does start
    % with a line break.
    char2file([c,cc],fname);
    
end