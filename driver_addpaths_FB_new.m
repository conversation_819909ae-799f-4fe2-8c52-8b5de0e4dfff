%% Foreign block path adding 
%% 2022 review of ForBlock 

%% Add IRIS 
try 
irisget();
display('IRIS already running')
catch
addpath K:\Dynare-IrisToolBox\IRIS8_CNB_verze;
irisstartup;
end

%% base path is actual directory of script
disp([sprintf('\n'), 'DRIVER_ADDPATH Foreign Block']);
addpath([pwd '\Utilities-core_new']);
addpath(genpath([pwd '\Utilities-extra']));

%% Adding model forecast folder
cd Utilities-core_new
addpath(genpath([pwd '\decomposition_expect']),genpath([pwd '\model']), genpath([pwd '\pomocne_funkce']),...
genpath([pwd '\python']),genpath([pwd '\reports']),genpath([pwd '\Support_functions']),genpath([pwd '\tools']));
cd ..\forecasts\baseline
