function [tuneold_filename ...
    tunenew_filename ...
    tunerev_filename ...
    rev_filename ...
    rls_filename ...
    fcastold_filename ...
    fcastnew_filename ...
    ini_filename] = define_names_for_decomposition(sr_opt, options, decomp_type)
% this code tries to find relevant filenames for decompositions


tuneold_filename    = [];
tunenew_filename    = [];
tunerev_filename    = [];
rev_filename        = [];
rls_filename        = [];
fcastold_filename   = [];
fcastnew_filename   = [];
ini_filename		= [];

detail_str = ['detail' num2str(options.detail_level)];

if options.ini2obs
    
    part_decomp_str = {'rev'};
    if ~options.obs2shocks
        part_decomp_str = [part_decomp_str, {'tuneold','tunerev'}];
    end
    if options.is_trans
        part_decomp_str = [part_decomp_str, {'rls'}];
        if ~options.obs2shocks
            part_decomp_str = [part_decomp_str, {'tunenew'}];
        end
    end
    if ~options.just_filter
        part_decomp_str = [part_decomp_str, {'fcastold','fcastnew'}];
    end
    
    if options.recompute
        for ix = 1:length(part_decomp_str)
            eval([part_decomp_str{ix} '_filename = [sr_opt.new.outdata_dir ''\'' sr_opt.cmp.cmpreport_prefix ''-'' decomp_type ''-'' part_decomp_str{ix} ''-'' detail_str ''.mat''];']);
        end
    else
        for ix = 1:length(part_decomp_str)
            current_detail = options.detail_level;
            while current_detail <= 2
                detail_str = ['detail' num2str(current_detail)];
                file_str = [sr_opt.new.outdata_dir '\' sr_opt.cmp.cmpreport_prefix '-' decomp_type '-' part_decomp_str{ix} '-' detail_str '.mat'];
                if isequal(exist(file_str, 'file'), 2)
                    eval([part_decomp_str{ix} '_filename = file_str;']);
                    load(file_str,['decomp_' (part_decomp_str{ix})]);
                    if eval(['isequal(decomp_' part_decomp_str{ix} '.new_ID, options.new_ID) && ' ...
                            'isequal(decomp_' part_decomp_str{ix} '.old_ID, options.old_ID) && ' ...
                            '(decomp_' part_decomp_str{ix} '.decomp_range(1) <= options.decomp_range(1)) && ' ...
                            '(decomp_' part_decomp_str{ix} '.decomp_range(end) >= options.decomp_range(end)) && ' ...
                            'isequal(decomp_' part_decomp_str{ix} '.plan_type, options.plan_type) && ' ...
                            'isequal(decomp_' part_decomp_str{ix} '.decomp_type, decomp_type)'])
                        break;
                    end
                end
                current_detail = current_detail + 1;
            end
            if isempty(eval([part_decomp_str{ix} '_filename']))
                detail_str = ['detail' num2str(options.detail_level)];
                eval([part_decomp_str{ix} '_filename = [sr_opt.new.outdata_dir ''\'' sr_opt.cmp.cmpreport_prefix ''-'' decomp_type ''-'' part_decomp_str{ix} ''-'' detail_str ''.mat''];']);
            end
        end
    end
    
else
    
    if options.recompute
        ini_filename = [sr_opt.new.outdata_dir '\' sr_opt.cmp.cmpreport_prefix '-' decomp_type '-ini-' detail_str '.mat'];
    else
        current_detail = options.detail_level;
        while current_detail <= 2
            detail_str = ['detail' num2str(current_detail)];
            file_str = [sr_opt.new.outdata_dir '\' sr_opt.cmp.cmpreport_prefix '-' decomp_type '-ini-' detail_str '.mat'];
            if isequal(exist(file_str, 'file'), 2)
                ini_filename = file_str;
                break;
            end
            current_detail = current_detail + 1;
        end
        if isempty(ini_filename)
            detail_str = ['detail' num2str(options.detail_level)];
            ini_filename = [sr_opt.new.outdata_dir '\' sr_opt.cmp.cmpreport_prefix '-' decomp_type '-ini-' detail_str '.mat'];
        end
    end
end

return

end  %--- of the MAIN function -----------------------------------------------------%



