function export2xls(decomp, decomp_list, groups_nms, varargin)
%----------------------------------------------------------------%
% inputs: 	
% decomp		-- decomposition of two forecasts (including filters)
% decomp_list	-- endogenous variables to report
% groups		-- factors groups
% groups_nms	-- names of factor groups
%----------------------------------------------------------------%
% @ last revision: za, CNB, Apr 2012

%---defaults and varargin---%
default =	{	...
    'plot_rng',			decomp.decomp_range,  ...
	'zz_transform', 	false, ...
	'fname',			'Decomposition' ...
    };
options = passopt(default,varargin{1:end});

disp(['Exporting to ' options.fname '.xlsx']);

% adjust plot_rng and others
options.plot_rng = max(decomp.decomp_range(1),options.plot_rng(1)) : ...
	min(decomp.decomp_range(end),options.plot_rng(end));

% check decomp_list
[~,all_loc] = ismember('all',decomp_list);
[~,allexp_loc] = ismember('all_exp',decomp_list);
if any(allexp_loc)
	decomp_list	= decomp.endog_vars;
elseif any(all_loc)
	index		= regexp(decomp.endog_vars,'_exp');
	index		= cellfun(@isempty,index);
	decomp_list	= decomp.endog_vars(index);
end
[~,decomp_loc] = ismember(decomp_list,decomp.endog_vars);
if ~all(decomp_loc)
	error('Wrong decomp_list!');
end

% prepare tseries
varname = cell(length(decomp_loc),1);
varnamecom = cell(length(decomp_loc),1);
um = cell(length(decomp_loc),1);
for ivar = 1:length(decomp_loc)
	varname{ivar} = decomp.endog_vars{decomp_loc(ivar)};
	varnamecom{ivar} = comment(decomp.d_new.(varname{ivar}));
    um{ivar} = tseries(options.plot_rng, ...
		decomp.store_matrix(options.plot_rng-decomp.decomp_range(1)+1,:,decomp_loc(ivar)));
	if options.zz_transform
		if strcmp(varname{ivar}(end),'4') || strcmp(varname{ivar},'y_star_gap') % annual or quarter data; if gap, then no annual
			annual = 1;
		else annual = 4;
		end
		um{ivar} = 100*annual*um{ivar};
	end
end

% store the results
for ivar = 1 : length(decomp_list)
	for j = 1:size(um{ivar},2)
		contrname = [strrep(strrep(groups_nms{j,1},': ','__'),'\',''), ...
			'_TO_',decomp_list{ivar}];
		contrib.(contrname) = um{ivar}{:,j};
		contrib.(contrname) = comment(contrib.(contrname), ...
			[char(decomp.input_datenames{j}),' -> ',char(varnamecom{ivar})]);
	end
end

dbsave(contrib,[options.fname '.csv'],Inf,'format','%.16e');

% convert to xls file
[data,~]= readtext([options.fname '.csv'],',','','"');
% remove string delimiters
for i1 = 1:size(data,1)
	data(i1,1) = strrep(data(i1,1),'"','');
end
for i2 = 1:size(data,2)
	data(1,i2) = strrep(data(1,i2),'"','');
	data(2,i2) = strrep(data(2,i2),'"','');
	data(3,i2) = strrep(data(3,i2),'"','');
end
if exist([options.fname '.xlsx'], 'file') == 2
   disp('Rewritting old xlsx file') 
   delete([options.fname '.xlsx']);
end;
if size(fullfile(pwd,[options.fname]),2)<214
    [~,~] = xlswrite([options.fname '.xlsx'],data);
else
    % dirty trick with the long file names
    fname = fullfile(pwd,[options.fname '.xlsx']);
    [~,~] = xlswrite(fname(1:214),data);
    movefile([fname(1:214) '.xls'],fname);
end;


end %-of the MAIN FUNCTION------------------------------------------------%

