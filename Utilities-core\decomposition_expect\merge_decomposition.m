%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function [decomp] = merge_decomposition(new_ID, old_ID, decomp_range, sr_opt, ...
	decomp_tuneold, decomp_tunenew, decomp_tunerev, decomp_rev, decomp_rls, ...
    decomp_fcastold, decomp_fcastnew, decomp_ini, d_new, d_old, ...
	plan_type, decomp_type, detail_level, limit, show_modelchange)

% last revision: za, mar 2013


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% MERGE DECOMPOSITIONS
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

tic;

disp('Merging decompositions...');

part_decomp_str = {...
    'tuneold'
    'tunenew'
    'tunerev'
    'rev'
    'rls'
    'fcastold'
    'fcastnew'
    'ini'
    };

nonactive_str = {};
for ix = 1:length(part_decomp_str)
	if isempty(sr_opt.cmp.([part_decomp_str{ix} '_filename']))
		nonactive_str = [nonactive_str; part_decomp_str{ix}];
	end
end
part_decomp_str = setdiff(part_decomp_str,nonactive_str);

% empty struct for noncomputed partial decompositions
for ix = 1:length(nonactive_str)
	eval(['decomp_' nonactive_str{ix} ' = struct([]);']);
end

%% adjust for model change

if ismember('ini',part_decomp_str)
    if decomp_ini.d_new.modelchange && show_modelchange
        d_model_new = d_new;
    else d_model_new = dbempty;
    end
    if decomp_ini.d_old.modelchange && show_modelchange
        d_model_old = d_old;
    else d_model_old = dbempty;
    end
elseif ismember({'fcastnew','fcastold'},part_decomp_str)
	if decomp_fcastnew.d_new.modelchange && show_modelchange
		d_model_new = d_new;
	else d_model_new = dbempty;
	end
	if decomp_fcastold.d_old.modelchange && show_modelchange
		d_model_old = d_old;
	else d_model_old = dbempty;
	end
elseif ismember('tuneold',part_decomp_str)
	if decomp_tuneold.d_old.modelchange && show_modelchange
		d_model_old = d_old;
	else d_model_old = dbempty;
	end
	if ismember('tunenew',part_decomp_str)
		if decomp_tunenew.d_new.modelchange && show_modelchange
			d_model_new = d_new;
		else d_model_new = dbempty;
		end
	else ismember('tunerev',part_decomp_str)
		if decomp_tunerev.d_new.modelchange && show_modelchange
			d_model_new = d_new;
		else d_model_new = dbempty;
		end
	end
end

%% merge decompositions

decomp = merge_decomp(decomp_range, ...
	'tuneold', decomp_tuneold, ...
	'tunerev', decomp_tunerev, ...
	'tunenew', decomp_tunenew, ...
	'rev', decomp_rev, ...
	'rls', decomp_rls, ...
	'fcastold', decomp_fcastold, ...
	'fcastnew', decomp_fcastnew, ...
	'ini', decomp_ini, ...
	'limit', limit, ...
	'modelchange_new', d_model_new, ...
	'modelchange_old', d_model_old);


% additional output for identification
decomp.new_ID			= new_ID;
decomp.old_ID			= old_ID;
decomp.plan_type		= plan_type;
decomp.decomp_type      = decomp_type;
decomp.detail_level     = detail_level;
decomp.limit			= limit;

% save
save([sr_opt.cmp.outdata_dir '\' sr_opt.cmp.cmpreport_prefix '-decomposition'], 'decomp');

toc

end