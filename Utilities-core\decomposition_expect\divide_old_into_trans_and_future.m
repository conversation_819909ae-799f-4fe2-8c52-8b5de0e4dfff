function [decomp_fcastold] = divide_old_into_trans_and_future(decomp_fcastold,m_new,d_rep_old,...
    f_rep_old,sr_opt,fcast_plan_decomp,is_detailed_obs,is_detailed_fix,is_detailed_eps,decomp_matrix_range,expectations)

warning off;

[ decomp1future ] = decomp_all(m_new, d_rep_old,f_rep_old, ...
	decomp_matrix_range, ...
	'plan', fcast_plan_decomp, ...
	'simrng', sr_opt.new.comprng,...
    'is_detailed_obs_', is_detailed_obs, ...
    'is_detailed_fix_', is_detailed_fix, ...
    'is_detailed_eps_', is_detailed_eps, ...
    'expectations',expectations);

warning on;

tf = ismember(decomp1future.input_datenames,decomp_fcastold.input_datenames);
decomp1future.store_matrix=decomp1future.store_matrix(:,tf,:);
decomp1future.input_datenames=decomp1future.input_datenames(tf);
decomp1future.input_vect=decomp1future.input_vect(tf,:);
decomp1future.input_names=decomp1future.input_names(tf);
decomp1future = rmfield(decomp1future, {'truediffmat','d_new','d_old'});

[~,index_factors]=ismember(decomp_fcastold.input_datenames,decomp1future.input_datenames);
decomp1trans = decomp_fcastold;

for xx=1:length(decomp1trans.input_datenames)
	if index_factors(xx)>0
		decomp1trans.store_matrix(:,xx,:) = decomp1trans.store_matrix(:,xx,:) - decomp1future.store_matrix(:,index_factors(xx),:);
	end
end
decomp1future = reset_input_datenames(decomp1future, 'fcastold');
decomp1trans = reset_input_datenames(decomp1trans, 'trans');

nper=size(decomp1trans.store_matrix,1);
nvars=size(decomp1trans.store_matrix,3);
nfar1a=size(decomp1trans.store_matrix,2);
nfar1b=size(decomp1future.store_matrix,2);

decomp_fcastold.store_matrix=permute(reshape([reshape(permute(decomp1trans.store_matrix,[2 1 3]),nfar1a,nper*nvars);...
	reshape(permute(decomp1future.store_matrix,[2 1 3]),nfar1b,nper*nvars)],...
	nfar1a+nfar1b,nper,nvars),[2 1 3]);

decomp_fcastold.input_datenames=[decomp1trans.input_datenames;decomp1future.input_datenames];
decomp_fcastold.input_names=[decomp1trans.input_names decomp1future.input_names];
decomp_fcastold.input_vect=[decomp1trans.input_vect;decomp1future.input_vect];







end