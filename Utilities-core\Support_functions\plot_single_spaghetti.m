function  plot_single_spaghetti(cur_db_name,...
                               endhist_first,endhist_last,hair_length,...
                               plotlim,model1_tag,...
                               decomp_plot_vars,lan_ver,PPTverze,print_title_page)
% keyboard;
%% Error checking 
if (hair_length>8) || (hair_length<1)
    error('hair_length must not exceed 8...');
end

n_per = endhist_last-endhist_first+1;
if n_per<1
   n_per %#ok<NOPRT>
   error('"<endhist_last-endhist_first>" difference must be strictly positive...'); 
end

%% Computation

% Standard computation hair_length = 8;
hair_length_user = hair_length;
hair_length = 8; 
cur_db = dbload(cur_db_name);

%% Load previous results

% Model 1
load(['..\database\Output-data' filesep 'computed_spaghetti_[' model1_tag '].mat']);
disp([sprintf('\n'), '||| Previous results loaded...']);

%% Test if ranges etc. OK
% keyboard;
names1 = fieldnames(s1); %#ok<NODEF>
range1 = get(s1.(names1{1}),'range');

min_pos = max([range1(1)]);
max_pos = min([range1(end)-hair_length]);

to_print_min = dat2str(min_pos);
to_print_max = dat2str(max_pos);

disp([sprintf('\n') 'Maximum possible <endhist_first:endhist_last> range - ' to_print_min{:} ':' to_print_max{:} sprintf('\n') 'hit F5 to continue...']);
% keyboard;

%% Reporting the differences between predictions
	
disp([sprintf('\n'), '||| Computing ex post transformations...']);

% Range intersection
start_now = max([range1(1); ...
                 endhist_first]);
end_now   = min([range1(end)-hair_length; ...
                 endhist_last]);

% Debugging - DO NOT ERASE!
% dat2str(range1(1))
% dat2str(range2(1))
% dat2str(range1(end)-hair_length)
% dat2str(range2(end)-hair_length)
% dat2str(start_now)
% dat2str(end_now)

%% Determine original hair_lengths
s1_temp = s1.(names1{1})(:);
s1_temp = s1_temp(:,1);
hair_length_s1 = length(s1_temp) - sum(isnan(s1_temp)) - 1; 

%% Model vs. data differences 
s1_diffs_pom = dbfun(@(x,y) y-x,cur_db,s1);
if size(s1.(names1{1}),2)>1
    s1_diffs = dbfun(@(x) tseries(range1(1):range1(end)-hair_length_s1,spdiags(x(:),0:-1:-hair_length_s1)),s1_diffs_pom);
else
    s1_diffs = dbfun(@(x) tseries(range1(1):range1(end)-hair_length_s1,x(:)'),s1_diffs_pom);
end
s1_diffs = dbclip(s1_diffs,start_now:end_now);

%% Clip data to plot according to user supplied range/hair_length
% Model #1 
clip_min =  strfind(range1,start_now);
clip_max =  strfind(range1,end_now);
s1_pom = dbfun(@(x) x(:),s1);
s1_pom = dbfun(@(x) x(clip_min:clip_max+hair_length_s1,clip_min:clip_max),s1_pom);
colnum = size(s1_pom.(names1{1}),2);
s1_pom = dbfun(@(x) x(1:colnum+hair_length_user,:),s1_pom);
to_be_truncated = rot90(isnan(s1_pom.(names1{1})),2);
for ii = 1:length(names1)
    s1_pom.(names1{ii})(to_be_truncated)=NaN;    
end
s1 = dbfun(@(x) tseries(start_now:end_now+hair_length_user,x),s1_pom);

%% Acknowledge the user supplied hair_length
s1_diffs = dbfun(@(x) tseries(start_now:end_now,x(:,1:hair_length_user+1)),s1_diffs);

%% Differences between models
s1s2_periods_compare     = dbfun(@(x) ...
							tseries(start_now:end_now, ...
							x(:) ), ...
							s1_diffs);

hair_length = hair_length_user;

% Linearni prevazeni vsech odchylek vychazejicich z daneho roku 
weights = [1 1:-1/hair_length:1/hair_length];

% -> absolutne...
s1s2_compare = dbfun(@(x) tseries(start_now:end_now,(x(:)*weights')./sum(weights)),s1s2_periods_compare);

% keyboard;

%% Plot the comparison

disp([sprintf('\n'), '||| Plotting...']);


n_per = end_now-start_now+1;
cur_db = dbclip(cur_db,plotlim{1}:plotlim{2});

for k = 1:size(decomp_plot_vars,1)


    var_exists1 = 0;
    
	name_now = get(cur_db.(decomp_plot_vars{k}),'comment');
    figname = figure('Name',name_now{:});    
    p1 = subplot(5,1,[1:4]);
    hold on;
    
    try
        h1 = plot(s1.(decomp_plot_vars{k}),'color','red','LineWidth',2);
        var_exists1 = 1;
    end
    plot(cur_db.(decomp_plot_vars{k}),'LineWidth',4);
    hold off;

    % Y limits
    axis('tight');
    ylims = get(gca,'Ylim');
    ylim_range = max(ylims)-min(ylims);
    if ylim_range==0,ylim_range = 0.1;end
    set(gca,'Ylim',[min(ylims)-0.1*ylim_range max(ylims)+0.01*ylim_range]);

    % Legend
    lines_handles=get(p1(end),'Children');
    if var_exists1
        leg_handle = legend(lines_handles([1 n_per+1]),'Data','Model');
        set(leg_handle,'Location','South','Orientation','horizontal');
    end
    legend('boxoff');

    % Rest
    title( get(cur_db.(decomp_plot_vars{k}),'comment'));
    box on;
    xlims = get(gca,'Xlim');
    xticks = get(gca,'Xtick');

    if var_exists1
        subplot(5,1,5);
            temp_ = s1s2_compare.(decomp_plot_vars{k});
            range_ = get(temp_,'range');

            temp_1 = max(temp_(:),0);
            temp_2 = min(temp_(:),0);

            temp_1 = tseries(range_,temp_1);
            temp_2 = tseries(range_,temp_2);

            temp_1(plotlim{1}-1)=0;
            temp_2(plotlim{1}-1)=0;

            temp_1(plotlim{2}+1)=0;
            temp_2(plotlim{2}+1)=0;

        hold on;
        conbar(plotlim{1}:plotlim{2},[temp_1 0],'colorMap',[0 0.5 0]);
        conbar(plotlim{1}:plotlim{2},[temp_2 0],'colorMap',[1 0 0]);
        hold off;
        set(gca,'xticklabel',[]);
        Ylims = get(gca,'Ylim');
        set(gca,'Ylim',[-max(abs(Ylims)) max(abs(Ylims))]);
        title(['Absolute error']);
        set(gca,'Xlim',xlims);
        set(gca,'Xtick',xticks);
        box on;

    end

    % report figures as .ps
    set(figname,'PaperOrientation','portrait');    
    set(figname,'PaperUnits','normalized');
    set(figname,'PaperPosition', [0.05 0.05 0.95 0.95]);

    if k == 1
         print(figname,            '-dpsc2', 'Spaghetti_Report.ps');
    else
         print(figname, '-append', '-dpsc2', 'Spaghetti_Report.ps');
    end

end

% create PDF
% model_names = strrep(model_names,'\_','_');
disp([sprintf('\n'), '||| Generating PDF...']);
filename = ['Output' filesep 'Spaghetti_Report_[' model1_tag '].pdf'];

success_ = 0;
while success_==0
    try
        ps2pdf('psfile', 'Spaghetti_Report.ps', 'pdffile', ...
          filename, ...
           'gspapersize', 'a4', 'deletepsfile', 1);
        disp([sprintf('\n') filename ' generated...' sprintf('\n')]);
        success_ = 1;
    catch
        disp([sprintf('\n') '<strong>Cannot open report file' sprintf('\n') filename sprintf('\n') 'for writing, close & F5...</strong>' sprintf('\n')]);
        disp(['<strong>...if the problem persists, open ps2pdf around line 297 and change the GhostScript path...</strong>' sprintf('\n')]);
        keyboard;
    end
end
% keyboard;
end


