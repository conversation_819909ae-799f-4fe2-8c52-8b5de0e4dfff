function DECOMP = merge_decomp(decomp_range, varargin)
%----------------------------------------------------------------%
% input: 	
%		islog:		model argument
%
%----------------------------------------------------------------%
% @ last revision: za, CNB, Mar 2012

default = {...
	'tuneold',			struct([]), ...
	'tunenew',			struct([]), ...
    'tunerev',			struct([]), ...
	'rev',				struct([]), ...
	'rls',				struct([]), ...
	'fcastold',			struct([]), ...
	'fcastnew',			struct([]), ...
    'ini',				struct([]), ...
	'limit',			1e-15, ...		%- computation error limit
	'modelchange_new',	dbempty, ...	%- model change in new simulation
	'modelchange_old',	dbempty, ...	%- model change in old simulation
    };

options = passopt(default,varargin{1:end});


%% copy individual decompositions to respected variables and remove from options

list = {'tuneold','tunenew','tunerev','rev','rls', ...
	'fcastold','fcastnew','ini'};
for i = 1:length(list)
	if ~isempty(options.(list{i}))
		decompstruct.(list{i}) = options.(list{i});
	end
	options = rmfield(options, list{i});
end
list = fieldnames(decompstruct);
	
%% databases
if isfield(decompstruct,'ini')
        if isequal(options.modelchange_new,dbempty)
            d_check_new = decompstruct.ini.d_new;
        else
            d_check_new = options.modelchange_new;
            d_new = decompstruct.ini.d_new;
        end
        if isequal(options.modelchange_old,dbempty)
            d_check_old = decompstruct.ini.d_old;
        else
            d_check_old = options.modelchange_old;
            d_old = decompstruct.ini.d_old;
        end     
else
    if isfield(decompstruct,'fcastnew')
        if isequal(options.modelchange_new,dbempty)
            d_check_new = decompstruct.fcastnew.d_new;
        else
            d_check_new = options.modelchange_new;
            d_new = decompstruct.fcastnew.d_new;
        end
        if isequal(options.modelchange_old,dbempty)
            d_check_old = decompstruct.fcastold.d_old;
        else
            d_check_old = options.modelchange_old;
            d_old = decompstruct.fcastold.d_old;
        end 
    else
        if isequal(options.modelchange_new,dbempty)
            d_check_new = decompstruct.tunenew.d_new;
        else
            d_check_new = options.modelchange_new;
            d_new = decompstruct.tunenew.d_new;
        end
        if isequal(options.modelchange_old,dbempty)
            d_check_old = decompstruct.tuneold.d_old;
        else
            d_check_old = options.modelchange_old;
            d_old = decompstruct.tuneold.d_old;
        end         
    end
end    

if isfield(decompstruct,'ini')
    fut_rng          = decompstruct.ini.fut_rng;             % take from decomp_ini
    trans_rng        = decompstruct.ini.trans_rng;           % take from decomp_ini
    hist_rng         = decompstruct.ini.hist_rng;            % take from decomp_ini
    islog            = decompstruct.ini.islog;
else
    hist_rng         = decompstruct.rev.hist_rng;            % take from decomp_rev
    trans_rng        = decompstruct.rev.trans_rng;           % take from decomp_rev
    fut_rng          = decompstruct.rev.fut_rng;             % take from decomp_rev
    islog            = decompstruct.rev.islog;
end


 %% MERGE       

%% initialise 
endog_vars = decompstruct.(list{1}).endog_vars;

% check decomp_range against partial decompositions ranges
for ix = 1:length(list)
	if (decomp_range(1) < decompstruct.(list{ix}).decomp_range(1)) || ... 
	   (decomp_range(end) > decompstruct.(list{ix}).decomp_range(end))
		error('Range of decomposition exceeds some of loaded partial decomposition ranges! Recompute...');
	end
end

nper = length(decomp_range); % # periods
nvars = length(endog_vars); % # endogenous vars
nfar = 0;  % # factors
auxMat = [];
input_names = [];
input_datenames = [];
input_vect = [];


for ix = 1:length(list)
	nfar = nfar + length(decompstruct.(list{ix}).input_datenames);
	auxMat = [auxMat; ...
		reshape(permute(decompstruct.(list{ix}).store_matrix,[2 1 3]), ...
		length(decompstruct.(list{ix}).input_datenames), nper*nvars)];
	input_names = [input_names, decompstruct.(list{ix}).input_names];
	input_datenames = [input_datenames; decompstruct.(list{ix}).input_datenames];
	input_vect = [input_vect; decompstruct.(list{ix}).input_vect];
end

store_matrix = permute(reshape([auxMat], nfar, nper, nvars), [2 1 3]);



%% define model changes
if ~isequal(options.modelchange_new,dbempty)
	input_names = [input_names, 'new_model_change'];
	input_datenames = [input_datenames; 'all__model__new_model_change'];
	input_vect = [input_vect; NaN, 31];
    aux = size(store_matrix,2)+1;
	for k = 1 : length(endog_vars)
        if islog.(endog_vars{k})
            store_matrix(:,aux,k) = ...
				-(log(d_new.(char(endog_vars(k)))(decomp_range) ./ ...
				d_check_new.(char(endog_vars(k)))(decomp_range)));
        else
            store_matrix(:,aux,k) = ...
				-(d_new.(char(endog_vars(k)))(decomp_range) - ...
				d_check_new.(char(endog_vars(k)))(decomp_range));
        end
	end
end
if ~isequal(options.modelchange_old,dbempty)
	input_names = [input_names, 'old_model_change'];
	input_datenames = [input_datenames; 'all__model__old_model_change'];
	input_vect = [input_vect; NaN, 31];
	aux = size(store_matrix,2)+1;
    old_endogvars = fieldnames(d_check_old);
	for k = 1 : length(endog_vars)
        if any(strcmp(endog_vars{k},old_endogvars))
            if islog.(endog_vars{k})
                store_matrix(:,aux,k) = ...
                    log(d_old.(char(endog_vars(k)))(decomp_range) ./ ...
                    d_check_old.(char(endog_vars(k)))(decomp_range));
            else
                store_matrix(:,aux,k) = ...
                    d_old.(char(endog_vars(k)))(decomp_range) - ...
                    d_check_old.(char(endog_vars(k)))(decomp_range);
            end
        else
            if islog.(endog_vars{k})
                store_matrix(:,aux,k) = ...
                    log(d_old.(char(endog_vars(k)))(decomp_range));
            else
                store_matrix(:,aux,k) = ...
                    d_old.(char(endog_vars(k)))(decomp_range);
            end
        end    
	end
end

%% fill truediffmat
truediffmat = NaN(length(decomp_range),length(endog_vars));
old_endogvars = fieldnames(d_check_old);
for k = 1 : length(endog_vars)
    if any(strcmp(endog_vars{k},old_endogvars))   
        if islog.(endog_vars{k})
            truediffmat(:,k) = ...
                log(d_check_new.(char(endog_vars(k)))(decomp_range) ./ ...
                d_check_old.(char(endog_vars(k)))(decomp_range));
        else
            truediffmat(:,k) = ...
                d_check_new.(char(endog_vars(k)))(decomp_range) - ...
                d_check_old.(char(endog_vars(k)))(decomp_range);
        end
    else
        if islog.(endog_vars{k})
            truediffmat(:,k) = ...
                log(d_check_new.(char(endog_vars(k)))(decomp_range));
        else
            truediffmat(:,k) = ...
                d_check_new.(char(endog_vars(k)))(decomp_range);
        end
    end    
end


%% output

DECOMP.store_matrix		= store_matrix;
DECOMP.truediffmat		= truediffmat;
DECOMP.endog_vars		= endog_vars;
DECOMP.input_names		= input_names;
DECOMP.input_vect		= input_vect;
DECOMP.input_datenames	= input_datenames;
DECOMP.decomp_range		= decomp_range;
DECOMP.d_new			= d_check_new;
DECOMP.d_old			= d_check_old;
DECOMP.hist_rng         = hist_rng;            % take from decomp_rev
DECOMP.trans_rng        = trans_rng;           % take from decomp_rev
DECOMP.fut_rng          = fut_rng;             % take from decomp_rev
DECOMP.islog            = islog;

check_decomposition(DECOMP,'limit',options.limit);

end  %--- of the MAIN function -----------------------------------------------------%





