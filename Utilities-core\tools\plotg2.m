function [handle] = plotg2(dseries,varargin)
%
% PLOTG  Line plot function for time series specially adjusted for reports.
%
% Syntax:
%   [h,rng,model_nmbr] = plotg(dseries,...)
%   [h,rng,model_nmbr] = plotg(dseries1,range,...)
%   [h,rng,model_nmbr] = plotg(dseries1,range,model number,...)
% Arguments:
%   h numeric; dseries1,dseries2 tseries; range numeric; model number
%   numeric
% Options:
%   'legend'
%   'type'          char style of figure 'doc1', 'doc2', 'ppt','ppt_hair'
%	'palette'		numeric - predefined set of colors and/or markers 
%							- 1 klasik
%							- 3 cerna a cervena
%   'colors'        numeric - # colors used from palette
%   'endhist'    numeric - till EndHistory solid - line plot
%                             from EndHistory+1 dashed - line plot
%   'title'         string of title
%   'history_shift' numeric lag option for comparing forecasts
%                   1..one lag, works for comparison of 2 and 3 series, the
%                   2nd and 3rd are plotted with the shift in respect to
%                   the first series
%
%   'inflation_target'  the inflation target series has to be followed with
%                  2 series defining the inflation target band, the number
%                  in this option specifies the position of target in the
%                  list of the series for plot
%
%   'two_axis' list of tseries that will be graphed on the second axis
%              [2 4] - 2nd and 4th tseries will be plotted on the 2nd axes.
%
%   'two_axis_style'   'bar' or 'line', 'bar' default
%   
%   'two_axis_limits'  set both axis limits as [bottom_left,top_left,bottom_right,top_right]
%
%   'x_ticks' sets ticks on horizontal axis,
%             =1 (default) for yearly ticks (e.g. 2006,2007,2008,...)
%             =2 for biquarterly ticks (e.g. 2006Q1,2006Q3,2007Q1,...)
%             =4 for quarterly ticks (e.g. 2006Q1,2006Q2,2006Q3,...)
%
%   'margin_top' adjusts top y-axis limit,
%             =0.05 (default) => limit is set 5% above max. data point (data range =100%)
%
%   'margin_bottom' does the same thing with lower y-axis limit
%
%
%   no more Matlab PLOT options
%
% G3 tools

% @ rewritten
%   jr, 29.10.2012, manually controlled right-axis scale (comment out lines 480-489)
%   Jakub Rysanek, 2.2.2011, XTick options and controlled Y-axis limit added
%   Frantisek Kopriva, CNB, 28.5.2010, 2-vertical-axis graphs enabled
%   Fb, april 2011, plotting in cycle and colors of series in gstyle
%   FK, april 2011, plotting bar differences on second axis
%   Fb, july 2013, labels
%   ZH, March 2014, properties 'colors' and 'palette'
%   JZ, February 2019, plotg2 - plotting various models with mutliple hit
%   periods in one graph, colour option 11 - different colour intensities

default = {
    'legend',{''},...
    'type','doc2',...
    'endhist',[],...
    'title',[],...
    'history_shift',0,...
    'post_process',[]...
    'pre_process',[],...
    'inflation_target',-Inf,...
    'two_axis',[],...
    'two_axis_style','bar',...
    'two_axis_limits',[],...
    'x_ticks',1,...
    'margin_top',0.05,...
    'margin_bottom',0.05,...
	'colors',8,...
	'palette',1,...
    };

if  ~isnumeric(varargin{2})
error('Error. Number of models need to be specified.')
end

if nargin == 1
    ts_range = get(dseries,'range');
    options = passopt(default);
elseif nargin == 2
    ts_range = varargin{1};
    options = passopt(default);
elseif nargin == 3
    ts_range = varargin{1};
    model_number = varargin{2};
    options = passopt(default);
elseif (nargin >4)
    ts_range = varargin{1};
    model_number = varargin{2};
    options = passopt(default,varargin(3:end));
else
    error('Incorrect inputs in plotg.')
end

% if end of history not specified, take the end of series
if isempty(options.endhist),
    options.endhist = ts_range(end);
end;

% check range
if ~((ts_range(1)<=options.endhist)&&(options.endhist<=ts_range(end))), error('End of history out of range!'); end
histr_rng    = ts_range(1): options.endhist;
frcst_rng    = options.endhist: ts_range(end);
hshft_rng    = ts_range(1): options.endhist-options.history_shift;
fshft_rng    = options.endhist-options.history_shift: ts_range(end);

model_nmbr = model_number;

% Number of series
series_nmbr = size(dseries,2);
series_2nAxis_nmbr=length(options.two_axis);

% Set tseries axes
tseries_axis=1:series_nmbr;
poradi_rad_2osy=setdiff(tseries_axis,options.two_axis);
tseries_axis=[poradi_rad_2osy(end:-1:1) options.two_axis];

% >>> set x ticks >>>
ticks = dat2dec(ts_range);
switch options.x_ticks
    case 1
        ticks = unique(floor(ticks));
    case 2
        ticks = unique(floor(2*ticks))./2;
    case 4
        ticks = dat2dec(ts_range(1)):0.25:dat2dec(ts_range(end));
    otherwise
        error('plotg: ---> x_ticks option processes only values 1,2 or 4 <---');
end
x_labels_temp = dat2str(qq(ticks));

frequency=get(dseries,'freq');
if frequency==1
    ticks = ticks + 0.5; % makes graphs IRIS 8 compatible
end
if frequency==4
    ticks = ticks + 0.125; % makes graphs IRIS 8 compatible
end
if frequency==12
    ticks = ticks + 0.041666666667; % makes graphs IRIS 8 compatible
end
% <<<

% >>> determine ylim range >>>
if isempty(options.two_axis)
    max_data = max(max(dseries(ts_range)));
    min_data = min(min(dseries(ts_range)));
    data_range = max_data - min_data;
    if data_range~=0
        y_axis_top_limit    = max_data + options.margin_top * data_range;
        y_axis_bottom_limit = min_data - options.margin_bottom * data_range;
    else
        y_axis_top_limit    = max_data + 1;
        y_axis_bottom_limit = min_data - 1;
    end
else
    max_data = max(max(dseries(ts_range,tseries_axis(1:series_nmbr-series_2nAxis_nmbr))));
    min_data = min(min(dseries(ts_range,tseries_axis(1:series_nmbr-series_2nAxis_nmbr))));
    max_data2 = max(max(dseries(ts_range,tseries_axis(series_nmbr-series_2nAxis_nmbr+1:series_nmbr))));
    min_data2 = min(min(dseries(ts_range,tseries_axis(series_nmbr-series_2nAxis_nmbr+1:series_nmbr))));
    
    data_range = max_data - min_data;
    if data_range~=0
        y_axis_top_limit    = max_data + options.margin_top * data_range;
        y_axis_bottom_limit = min_data - options.margin_bottom * data_range;
    else
        y_axis_top_limit    = max_data + 1;
        y_axis_bottom_limit = min_data - 1;
    end
    
    data_range2 = max_data2 - min_data2;
    
    if data_range2~=0
        if max_data>0 & min_data<0
            if abs(max_data2)>abs(min_data2)
                y_axis_top_limit2    = max_data2 + options.margin_top * data_range2;
                y_axis_bottom_limit2 = y_axis_top_limit2*y_axis_bottom_limit/y_axis_top_limit;
                while y_axis_bottom_limit2>min_data2
                    y_axis_top_limit2    = 1.2*y_axis_top_limit2;
                    y_axis_bottom_limit2 = 1.2*y_axis_bottom_limit2;
                end
            else
                y_axis_bottom_limit2 = min_data2 - options.margin_bottom * data_range2;
                y_axis_top_limit2    = y_axis_bottom_limit2*y_axis_top_limit/y_axis_bottom_limit;
                while y_axis_top_limit2<max_data2
                    y_axis_top_limit2    = 1.2*y_axis_top_limit2;
                    y_axis_bottom_limit2 = 1.2*y_axis_bottom_limit2;
                end
            end
        end
        
    else
        y_axis_top_limit2    = y_axis_top_limit;
        y_axis_bottom_limit2 = y_axis_bottom_limit;
    end
end
% <<<

% SET GRAPH STYLE
[gstyle, result] =  define_graph_style('plotg',options);
if result
    disp('Wrong graph style!');
end;

colors_def = [0         0       1;
              0         0.5     0;
              1         0       0;
              0.75      0       0.75;              
              0.5273    0.8047  0.9792
              0         0       0];

line_Color = {};
hitperiod_nmbr = series_nmbr/model_nmbr;
for i1 = 1:model_nmbr
    for i2 = 1:hitperiod_nmbr
        line_Color{(i1-1)*hitperiod_nmbr+i2}=colors_def(i1,:)+(i2-1)/(hitperiod_nmbr)*([1 1 1]-colors_def(i1,:));
    end
end
line_Color{model_nmbr*hitperiod_nmbr+1}=colors_def(end,:);

if isempty(options.two_axis)
    cla;
	if options.colors == 0	
		for jj=1:series_nmbr
			gstyle.lineprop.colors{jj} = rand(1,3);
			gstyle.lineprop.markers{jj} = 'none';
        end
    elseif options.colors == 11
        for jj=1:series_nmbr
		gstyle.lineprop.colors{jj} = line_Color{jj};
		gstyle.lineprop.markers{jj} = 'none';
        end
  	else
		colser_nmbr = min(length(gstyle.lineprop.colors),options.colors);
		if series_nmbr > colser_nmbr
			for jj = colser_nmbr+1:series_nmbr
				gstyle.lineprop.colors{jj} = rand(1,3);
				gstyle.lineprop.markers{jj} = 'none';
			end
        end
    end
%     if isempty(options.colorscheme) ~= 1;
%         
%     end
    
    
    
    
	for line_nmb =series_nmbr:-1:1,
		if (line_nmb == options.inflation_target+1) || (line_nmb == options.inflation_target+2)
			pl(series_nmbr-line_nmb+1) = plot(ts_range, tseries(histr_rng,dseries(histr_rng,line_nmb)),'Color',gstyle.lineprop.inflband_color,'Marker',gstyle.lineprop.inflband_marker,'MarkerSize',gstyle.MarkerSize);
			hold on;
			plot(ts_range, tseries(frcst_rng,dseries(frcst_rng,line_nmb)),'Color',gstyle.lineprop.inflband_color,'LineStyle','-');
			hold on;
		elseif (line_nmb == options.inflation_target)
			pl(series_nmbr-line_nmb+1) = plot(ts_range, tseries(histr_rng,dseries(histr_rng,line_nmb)),'Color',gstyle.lineprop.infltarget_color,'Marker',gstyle.lineprop.inflband_marker,'MarkerSize',gstyle.MarkerSize);
			hold on;
			plot(ts_range, tseries(frcst_rng,dseries(frcst_rng,line_nmb)),'Color',gstyle.lineprop.infltarget_color,'LineStyle','-');
			hold on;
		elseif (((series_nmbr-3*(options.inflation_target>1)) == 2) || ((series_nmbr-3*(options.inflation_target>1)) == 3)) && (line_nmb >1)
			pl(series_nmbr-line_nmb+1) = plot(ts_range, tseries(hshft_rng,dseries(hshft_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'Marker',gstyle.lineprop.markers{line_nmb},'MarkerSize',gstyle.MarkerSize);
			hold on;
			plot(ts_range, tseries(fshft_rng,dseries(fshft_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'LineStyle','--');
			hold on;
		else
			pl(series_nmbr-line_nmb+1) = plot(ts_range, tseries(histr_rng,dseries(histr_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'Marker',gstyle.lineprop.markers{line_nmb},'MarkerSize',gstyle.MarkerSize);
			hold on;
			plot(ts_range, tseries(frcst_rng,dseries(frcst_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'LineStyle','--');
			hold on;
		end;
	end;
	hold off;
else
    cla;
	if options.colors == 0	
		for jj=1:series_nmbr
			gstyle.lineprop.colors{jj} = rand(1,3);
			gstyle.lineprop.markers{jj} = 'none';
        end
    elseif options.colors == 11
        for jj=1:series_nmbr
		gstyle.lineprop.colors{jj} = [0+(jj-1)/(series_nmbr) 0+(jj-1)/(series_nmbr) 1+(jj-1)/(series_nmbr)];
		gstyle.lineprop.markers{jj} = 'none';
        end
    elseif options.colors == 12
        for jj=1:series_nmbr
            gstyle.lineprop.colors{jj} = [1 0+(jj-1)/(series_nmbr) 0+(jj-1)/(series_nmbr)];
            gstyle.lineprop.markers{jj} = 'none';
        end
    elseif options.colors == 13
        for jj=1:series_nmbr
            gstyle.lineprop.colors{jj} = [0+(jj-1)/(series_nmbr) 0 0+(jj-1)/(series_nmbr)];
            gstyle.lineprop.markers{jj} = 'none';
        end
	else
		colser_nmbr = min(length(gstyle.lineprop.colors),options.colors);
		if series_nmbr > colser_nmbr
			for jj = colser_nmbr+1:series_nmbr
				gstyle.lineprop.colors{jj} = rand(1,3);
				gstyle.lineprop.markers{jj} = 'none';
			end
		end;
	end
    for line_nmb =1:series_nmbr-series_2nAxis_nmbr,
        if (line_nmb == options.inflation_target+1) || (line_nmb == options.inflation_target+2)
            pl_hist(line_nmb) = plot(ts_range, tseries(histr_rng,dseries(histr_rng,line_nmb)),'Color',gstyle.lineprop.inflband_color,'Marker',gstyle.lineprop.inflband_marker,'MarkerSize',gstyle.MarkerSize);
            hold on;
            pl(line_nmb) = plot(ts_range, tseries(frcst_rng,dseries(frcst_rng,line_nmb)),'Color',gstyle.lineprop.inflband_color,'LineStyle','-');
            hold on;
        elseif (line_nmb == options.inflation_target)
            pl_hist(line_nmb) = plot(ts_range, tseries(histr_rng,dseries(histr_rng,line_nmb)),'Color',gstyle.lineprop.infltarget_color,'Marker',gstyle.lineprop.inflband_marker,'MarkerSize',gstyle.MarkerSize);
			hold on;
            pl(line_nmb) = plot(ts_range, tseries(frcst_rng,dseries(frcst_rng,line_nmb)),'Color',gstyle.lineprop.infltarget_color,'LineStyle','-');
            hold on;
        elseif (((series_nmbr-3*(options.inflation_target>1)) == 2) || ((series_nmbr-3*(options.inflation_target>1)) == 3)) && (line_nmb >1)
            pl_hist(line_nmb) = plot(ts_range, tseries(hshft_rng,dseries(hshft_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'Marker',gstyle.lineprop.markers{line_nmb},'MarkerSize',gstyle.MarkerSize);
            hold on;
            pl(line_nmb) = plot(ts_range, tseries(fshft_rng,dseries(fshft_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'LineStyle','--');
            hold on;
		else
            pl_hist(line_nmb) = plot(ts_range, tseries(histr_rng,dseries(histr_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'Marker',gstyle.lineprop.markers{line_nmb},'MarkerSize',gstyle.MarkerSize);
            hold on;
            pl(line_nmb) = plot(ts_range, tseries(frcst_rng,dseries(frcst_rng,line_nmb)),'Color',gstyle.lineprop.colors{line_nmb},'LineStyle','--');
            hold on;
        end;
    end;
    hold off;
		
    ax(1)=get(pl(1,1),'Parent');
    set(ax(1),'Color','none');
    
    if ~strcmp(options.title,'')
        set(gca,'Title',text('String',options.title));
        set(get(gca,'Title'),'FontName',gstyle.TitleFontName);
        set(get(gca,'Title'),'FontWeight',gstyle.TitleFontWeight);
        set(get(gca,'Title'),'FontSize',gstyle.TitleFontSize);
    end
    
    ax(2) = axes('Position',get(ax(1),'Position'),'Units',get(ax(1),'Units'),...
        'YAxisLocation','right');
    if strcmp(options.two_axis_style,'line')
        for ii=(series_nmbr-series_2nAxis_nmbr+1):series_nmbr
            pom=tseries(histr_rng,dseries(histr_rng,tseries_axis(ii)));
            hold on;pl_hist(tseries_axis(ii)) = feval(@plot,ts_range, pom);
            pom=tseries(frcst_rng,dseries(frcst_rng,tseries_axis(ii)));
            hold on;pl(tseries_axis(ii)) = feval(@plot,ts_range, pom);
            set(pl_hist(ii),'Color',gstyle.lineprop.colors{ii},'Marker',gstyle.lineprop.markers{ii},'MarkerSize',gstyle.MarkerSize);
            set(pl(ii),'Color',gstyle.lineprop.colors{ii},'LineStyle','--');
        end;
    else
        for ii=(series_nmbr-series_2nAxis_nmbr+1):series_nmbr
            pom=tseries(histr_rng,dseries(histr_rng,tseries_axis(ii)));
            hold on;pl_hist(tseries_axis(ii)) = feval(@bar,ts_range, pom, 'FaceColor', gstyle.barprop.colors{ii-series_nmbr+series_2nAxis_nmbr}); %  gstyle.barprop.width, - bylo by potreba upravit iridovskou fci bar
            pom=tseries(frcst_rng,dseries(frcst_rng,tseries_axis(ii)));
            hold on;pl(tseries_axis(ii)) = feval(@bar,ts_range, pom, 'FaceColor', gstyle.barprop.colors{ii-series_nmbr+series_2nAxis_nmbr}); %  gstyle.barprop.width, - bylo by potreba upravit iridovskou fci bar
        end;
    end;
end;

%% pre-processing
for ii=1:size(options.pre_process,1)
    eval(char(options.pre_process(ii,:)));
end;
%% Plot modifications of the actual graph
if isempty(options.two_axis)
    h = get(gcf,'children');
    a = get(gca,'children');
    set(a,'LineWidth',2);
    
    set(gcf,'Units','centimeters') ;
    set(gcf,'Position',gstyle.figure_size);
    set(gca,'FontName',gstyle.AxesFontName);
    set(gca,'FontWeight',gstyle.AxesFontWeight);
    set(gca,'FontSize',gstyle.AxesFontSize);
    
    if ~strcmp(options.title,'')
        set(gca,'Title',text('String',options.title));
        set(get(gca,'Title'),'FontName',gstyle.TitleFontName);
        set(get(gca,'Title'),'FontWeight',gstyle.TitleFontWeight);
        set(get(gca,'Title'),'FontSize',gstyle.TitleFontSize);
    end
    
    if ~strcmp(options.legend,'')
        lg = legend(fliplr(pl(end-length(options.legend)+1:end)),options.legend,'Location',[0 0.4 0.1 0.2]); %the legend
        set(lg, 'FontName', gstyle.LegendFontName);
        set(lg, 'FontWeight',gstyle.LegendFontWeight);
        set(lg, 'FontSize', 10);
    end
    
    set(gca,'YGrid','on');
    set(gca,'XTick',ticks);
    ylim([y_axis_bottom_limit y_axis_top_limit]);
    
    % modify xticklabel
    xtick = x_labels_temp;
    %     xtick = cellstr(get(gca,'XTickLabel'));
    if length(xtick{1}) == 6
        for i = 1:length(xtick),
            year=xtick{i}(3:4); season = xtick{i}(6);
            switch iscell(xtick)
                case strcmp(season,'1');
                    xtick{i} = strcat('I/',year);
                case strcmp(season,'2');
                    xtick{i} = strcat('II');
                case strcmp(season,'3');
                    xtick{i} = strcat('III');
                case strcmp(season,'4');
                    xtick{i} = strcat('IV');
            end
        end
        set(gca,'XTickLabel',char(xtick));
    end
    
    if frequency==1
       for i = 1:length(xtick) 
        year=xtick{i}(3:4);
        xtick{i} = year;       
       end 
       set(gca,'XTickLabel',char(xtick));
    end
    
    hold off;
else
%     keyboard;
    gcf_children=get(gcf,'children');
    if length(gcf_children)>2
        set(gcf,'children',[ax(1) ax(2) gcf_children(3:end)']);
    else
        set(gcf,'children',[ax(1) ax(2)]);
    end
    set(ax(1),'children',flipud(get(ax(1),'children')));
    set(ax(2),'children',flipud(get(ax(2),'children')));
    set(gcf,'Units','centimeters') ;
    set(gcf,'Position',gstyle.figure_size);
    set(ax(1),'FontName',gstyle.AxesFontName);
    set(ax(1),'FontWeight',gstyle.AxesFontWeight);
    set(ax(1),'FontSize',gstyle.AxesFontSize);
    set(ax(2),'FontName',gstyle.AxesFontName);
    set(ax(2),'FontWeight',gstyle.AxesFontWeight);
    set(ax(2),'FontSize',gstyle.AxesFontSize);
    
    if strcmp(options.two_axis_style,'line')
        for ii=1:series_nmbr
            set(pl_hist(ii),'LineWidth',gstyle.LineWidth);
            set(pl(ii),'LineWidth',gstyle.LineWidth);
        end;
    else
        Xlimsize_1 = get(ax(1),'Xlim');
        Xlimsize_2 = get(ax(2),'Xlim');
        offset = (Xlimsize_1(2)-Xlimsize_1(1))/(2*length(ts_range));
        set(ax(1),'Xlim', Xlimsize_1 - [offset+gstyle.barprop.offset -offset-gstyle.barprop.offset]);
        set(ax(2),'Xlim', Xlimsize_1 - [offset+gstyle.barprop.offset -offset-gstyle.barprop.offset]);
        set(ax(2),'Position',get(ax(1),'Position'));
        for ii=1:(series_nmbr-series_2nAxis_nmbr)
            set(pl_hist(ii),'LineWidth',gstyle.LineWidth);
            set(pl(ii),'LineWidth',gstyle.LineWidth);
        end;
    end;
       
    set(ax(1),'YGrid','on');
    set(ax(1),'XTick',ticks);
    set(ax(2),'XTick',[]);
    
    Yposun=0;
    set(ax(1),'Ylim',[y_axis_bottom_limit y_axis_top_limit]);
    if max_data>0 && min_data<0
        set(ax(2),'Ylim',[y_axis_bottom_limit2 y_axis_top_limit2]);
        Yposun=0;
    else
        Yticks=get(ax(1),'YTick');
        if data_range2~=0
            if max_data2>0 && min_data2<0
                if mod(length(Yticks),2)==1
                    prostr=ceil(length(Yticks)/2);
                else
                    prostr=ceil(length(Yticks)/2)+1;
                end
                if abs(max_data2)>abs(min_data2)
                    y_axis_top_limit2=max_data2 + options.margin_top * data_range2;
                    y_axis_bottom_limit2=y_axis_top_limit2*(y_axis_bottom_limit-Yticks(prostr))/(y_axis_top_limit-Yticks(prostr));
                    Yposun=Yticks(prostr);
                else
                    y_axis_bottom_limit2 = min_data2 - options.margin_bottom * data_range2;
                    y_axis_top_limit2    = y_axis_bottom_limit2*(y_axis_top_limit-Yticks(prostr))/(y_axis_bottom_limit-Yticks(prostr));
                    Yposun=Yticks(prostr);
                end
            else
                if max_data2>0
                    y_axis_top_limit2=max_data2 + options.margin_top * data_range2;
                    y_axis_bottom_limit2=y_axis_top_limit2*(y_axis_bottom_limit-Yticks(1))/(y_axis_top_limit-Yticks(1));
                    Yposun=Yticks(1);
                end
                if min_data2<0
                    y_axis_bottom_limit2 = min_data2 - options.margin_bottom * data_range2;
                    y_axis_top_limit2    = y_axis_bottom_limit2*(y_axis_top_limit-Yticks(end))/(y_axis_bottom_limit-Yticks(end));
                    Yposun=Yticks(end);
                end
            end
        else
            y_axis_top_limit2    = y_axis_top_limit;
            y_axis_bottom_limit2 = y_axis_bottom_limit;
        end
        set(ax(2),'Ylim',[y_axis_bottom_limit2 y_axis_top_limit2]);
    end
    
    % uprava ticks aby na sebe sedeli...
    Yticks=get(ax(1),'YTick');
    Yticks_2=get(ax(2),'YTick');
    Ylim=get(ax(1),'Ylim');
    Ylim_2=get(ax(2),'Ylim');
    
    pom_a=Yticks(2)-Yticks(1);
    pom_c=Ylim(2)-Ylim(1);
    pom_x=Yticks_2(2)-Yticks_2(1);
    pom_z=Ylim_2(2)-Ylim_2(1);
    	
    if pom_x*pom_c-pom_a*pom_z<0
        set(ax(2),'Ylim',[Ylim_2(1)-abs(Ylim_2(1))/(abs(Ylim_2(1))+Ylim_2(2))*((ceil(pom_a*pom_z/pom_c/pom_x)*pom_x*pom_c-pom_a*pom_z)/pom_a) Ylim_2(2)+Ylim_2(2)/(abs(Ylim_2(1))+Ylim_2(2))*((ceil(pom_a*pom_z/pom_c/pom_x)*pom_x*pom_c-pom_a*pom_z)/pom_a)]);
        set(ax(2),'Ytick',(Yticks-Yposun)/pom_a*pom_x*ceil(pom_a*pom_z/pom_c/pom_x));
    else
        set(ax(2),'Ylim',[Ylim_2(1)-abs(Ylim_2(1))/(abs(Ylim_2(1))+Ylim_2(2))*((pom_x*pom_c-pom_a*pom_z)/pom_a) Ylim_2(2)+Ylim_2(2)/(abs(Ylim_2(1))+Ylim_2(2))*((pom_x*pom_c-pom_a*pom_z)/pom_a)]);
        set(ax(2),'Ytick',(Yticks-Yposun)/pom_a*pom_x);
    end

    if ~isempty(options.two_axis_limits) % uprava axis natvrdo
        y_axis_top_limit     = options.two_axis_limits(2);
        y_axis_bottom_limit  = options.two_axis_limits(1);
        y_axis_top_limit2    = options.two_axis_limits(4);
        y_axis_bottom_limit2 = options.two_axis_limits(3);
    	set(ax(1),'Ylim',[y_axis_bottom_limit y_axis_top_limit]);
        set(ax(2),'Ylim',[y_axis_bottom_limit2 y_axis_top_limit2]);
        yticks1 = get(ax(1),'YTick');
        nticks  = length(yticks1);
        yticks2 = linspace(y_axis_bottom_limit2,y_axis_top_limit2,nticks);
        set(ax(2),'YTick',yticks2);        
	end
    
%     % >>> manualni prenastaveni skaly na prave ose (option.two_axis)
%     keyboard;
%     % 1] nastavit limit by-oko
%     set(ax(2),'Ylim',[-0.8 0.8]);
%     % 2] pomoci ff si vyladit ticky podle leve osy a Ygrid, 
%     %       ff muze byt ruzne pro jednotlive ticky
%     ff=1.18;set(ax(2),'Ytick',[-0.6*1.16 -0.3*ff 0 0.3*1.1 0.6*1.12]);
%     % 3] prelepeni zobrazovanych hodnot na ose, aby +/- sedelo, ale
%     %       zaokrouhleno
%     set(ax(2),'YtickLabel',[-0.6 -0.3 0 0.3 0.6]);
%     % <<<
    
    Ylim_2=get(ax(2),'Ylim');
    Yticks_2=get(ax(2),'Ytick');
    while data_range2~=0 && (min_data2<Ylim_2(1) || max_data2>Ylim_2(2))
        set(ax(2),'Ylim',2*Ylim_2);
        set(ax(2),'Ytick',2*Yticks_2);
        Ylim_2=get(ax(2),'Ylim');
        Yticks_2=get(ax(2),'Ytick');
    end    
    
    % modify xticklabel
    xtick = x_labels_temp;
    %     xtick = cellstr(get(ax(1),'XTickLabel'));
    if length(xtick{1}) == 6
        for i = 1:length(xtick),
            year=xtick{i}(3:4); season = xtick{i}(6);
            switch iscell(xtick)
                case strcmp(season,'1');
                    if frequency == 12
                        xtick{i} = strcat('1/',year);
                    else
                        xtick{i} = strcat('I/',year);
                    end;
                case strcmp(season,'2');
                    if frequency == 12
                        xtick{i} = ' 4';%strcat('4/',year);
                    else
                        xtick{i} = strcat('II');
                    end;
                case strcmp(season,'3');
                    if frequency == 12
                        xtick{i} = ' 7';%strcat('7/',year);
                    else
                        xtick{i} = strcat('III');
                    end;
                case strcmp(season,'4');
                    if frequency == 12
                        xtick{i} = ' 10';%strcat('10/',year);
                    else
                        xtick{i} = strcat('IV');
                    end;
            end
        end
        set(ax(1),'XTickLabel',char(xtick));
        set(ax(2),'XTickLabel',char(xtick));
    end
    
    if frequency==1
       for i = 1:length(xtick) 
        year=xtick{i}(3:4);
        xtick{i} = year;       
       end 
        set(ax(1),'XTickLabel',char(xtick));
        set(ax(2),'XTickLabel',char(xtick));
    end
    
    if ~strcmp(options.legend,'')
        %lg = legend((pl_hist(1:length(options.legend))),options.legend,'Location','Best'); %the legend
        lg = legend((pl_hist(1:length(options.legend))),options.legend); %the legend
        set(lg, 'Location', 'North');
        set(lg, 'FontName', gstyle.LegendFontName);
        set(lg, 'FontWeight',gstyle.LegendFontWeight);
        set(lg, 'FontSize', gstyle.LegendFontSize);
        set(lg, 'Color',gstyle.LegendColor);
    end
    
    set(ax(2),'ActivePositionProperty','OuterPosition');
	pause(0.001);
    set(ax(2),'Position',get(ax(1),'Position'));
     
    hold off;
end;

%% UI tools

% Always on top Java feature
% + .pdf printer
% + .pptx exporter

% if args.visible
    pushbuttons(gcf);
% end

%% post-processing
for ii=1:size(options.post_process,1)
    eval(char(options.post_process(ii,:)));
end;
%% send handle out
if nargout > 0
    handle = gcf;
    %     rng = ts_range;
end





