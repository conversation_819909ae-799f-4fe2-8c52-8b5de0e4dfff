function [output] = set_decomp_datatip(decomp,legend_names,varargin)
% main function of the package
%
% adds additional info to the decomposition (bar contribution) graphs
% as defined by the custom_datatip_decomp update function
% it also enables the use of arrow keys to move between individual bar
% contributions to view the data in detail

% input arguments:
%  decomp - IRIS time series data matrix with decomposition
%  legend_names - structure contraining the string names of all the individual contributions

% optional arguments:
%  precision - large number, used to avoid indeterminacy in graph when identifying the contribution by clicking 

% ! Note that the datatip is located always to side of the
% individual bar that is further from the zero line

% set-up the modified datatip update function

if nargin==3
    precision = varargin{1};
else
    precision = 10^9;
end

% update_callbacks_decomp(gcf,decomp,legend_names,precision);
UserData.decomp       = decomp;
UserData.legend_names = legend_names;
UserData.precision    = precision;
setappdata(gcf,'UserData',UserData);

add_toolbar_button_decomp(gcf,decomp,legend_names,precision);

end