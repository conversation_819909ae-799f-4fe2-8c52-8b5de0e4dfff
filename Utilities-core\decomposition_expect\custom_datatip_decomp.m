function output_txt = custom_datatip_decomp(obj,event_obj,decomp,legend_names,precision)
% update function for datatips in decomposition (bar contribution) graphs
% allows for additional information about the data (such as legend and
% individual values) to be displayed in datatips

% input arguments:
%  decomp - IRIS time series data matrix with decomposition
%  legend_names - structure contraining the string names of all the individual contributions
%  precision - large number, used to avoid indeterminacy in graph when identifying the contribution by clicking 

pos = get(event_obj, 'Position');
set(0,'userdata',pos); % shared variable, KeyDownListener will need to use this

% round the position in the graph to given precision

pres = precision; 

if pos(2) > 0
    pos(2) = round(abs(pos(2))*pres)/pres;
else
    pos(2) = -round(abs(pos(2))*pres)/pres;
end

% identify the selected period (in IRIS terms) from clicking in the graph

year=floor(pos(1));
quarter=pos(1)-year;
if quarter < 0.75
    if quarter < 0.5
        if quarter < 0.25
            quarter = 1;
        else
            quarter = 2;
        end
    else
        quarter = 3;
    end
else
    quarter = 4;
end
            
period = qq(year,quarter);

% split the decomposition in current period to positive and negative
% contributions

dec_in_per = decomp(period); 

dec_in_per_pos = dec_in_per;
dec_in_per_neg = dec_in_per;

for i = 1:length(dec_in_per)
    if dec_in_per(i)>0;
        dec_in_per_neg(i) = 0;
    elseif dec_in_per(i)<0;
        dec_in_per_pos(i) = 0;
    end
end

dec_in_per_pos = round(dec_in_per_pos*pres)/pres;
dec_in_per_neg = -round(abs(dec_in_per_neg)*pres)/pres;

% take cumulative sums that are in fact plotted in the bar plot

cmsm_pos = cumsum(dec_in_per_pos);
cmsm_neg = cumsum(dec_in_per_neg);

% find out which contribution was the closest to the click in the graph 

% note that the datatip is located always to side of the
% individual bar that is further from the zero line

ind_found = 0;
curInd = 0;
leg_str = 'not found';
if pos(2) == 0
    leg_str = 'zeroline';
elseif pos(2) > 0
    srch = abs(cmsm_pos-pos(2));
    [x1 x2] = find(srch==min(srch));
    crit = cmsm_pos(x2(1));
    for i = 1:length(dec_in_per)        
        if ind_found == 0
            if round(dec_in_per_pos(i)*pres) ~= 0 % ignore contributions smaller than given precision
                if crit <= cmsm_pos(i)
                    ind_found = 1;
                    curInd = i;
                    leg_str = char(legend_names{i});
                end
            end
        end
    end
elseif pos(2) < 0
	srch = abs(cmsm_neg-pos(2));
    [x1 x2] = find(srch==min(srch));
    crit = cmsm_neg(x2(1));
	for i = 1:length(dec_in_per)  
        if ind_found == 0
            if round(dec_in_per_neg(i)*pres) ~= 0 % ignore contributions smaller than given precision
                if crit >= cmsm_neg(i)
                    ind_found = 1;
                    curInd = i;
                    leg_str = char(legend_names{i});
                end
            end
        end
    end
end


if ind_found == 0

    output_txt = {
    ['Period: ',char(dat2str(period))]...
    ['legend: ', leg_str]...
	['contrib:', '0']...
    ['X: ', num2str(pos(1),4)]...
    ['Y: ', num2str(pos(2),4)] ...
    };

else
    
	output_txt = {
    ['Period: ',char(dat2str(period))]...
    ['legend: ', leg_str]...
	['contrib:', num2str(decomp(period,curInd))]...
    ['X: ', num2str(pos(1),4)]...
    ['Y: ', num2str(pos(2),4)] ...
    };
end

end

