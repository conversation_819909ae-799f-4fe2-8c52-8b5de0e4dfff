%**************************************************************************
function [dtunes] = get_tunes_FB(SS)
%**************************************************************************

% Input:	SS		: structure of steady state values and parameters of the model
% Output:	dtunes	: database of tuned tseries 

%**************************************************************************

% Initialize database
dtunes = dbempty();

%% Perma Tunes
% tune_dot_y_star_trend_shift, SS = 1
dtunes.tune_dot_y_star_trend_shift = tseries();

%original
dtunes.tune_dot_y_star_trend_shift(qq(2020,1)) = 0.98;%0.975; %0.98
dtunes.tune_dot_y_star_trend_shift(qq(2020,2)) = 0.915;%0.91; %0.91
dtunes.tune_dot_y_star_trend_shift(qq(2020,3)) = 1.087;%1.076; % 1.08
dtunes.tune_dot_y_star_trend_shift(qq(2020,4)) = 1.01;
dtunes.tune_dot_y_star_trend_shift(qq(2021,1)) = 0.995; %0.985
dtunes.tune_dot_y_star_trend_shift(qq(2021,2)) = 1.02;

% y_star_gap, SS = 1
dtunes.tune_y_star_gap = tseries();

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Temporary
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% commented

dtunes.tune_y_star_gap(qq(2005,1)) = 0.99;

dtunes.tune_y_star_gap(qq(2009,1)) = 0.962;
  dtunes.tune_y_star_gap(qq(2011,1)) = 1.00809;
   dtunes.tune_y_star_gap(qq(2011,2)) = 1.0070905;
   dtunes.tune_y_star_gap(qq(2011,3)) = 1.0074905;
 dtunes.tune_y_star_gap(qq(2011,4)) = 1.0050905;
dtunes.tune_y_star_gap(qq(2012,1)) = 1.0040905;

%dtunes.tune_y_star_gap(qq(2019,1)) = 1.0080905;

%%%%%%
%dtunes.tune_y_star_gap(qq(2013,1)) = 0.982;
%dtunes.tune_y_star_gap(qq(2014,1)) = 0.9905; %-1.6 %% - 1.3 %
%dtunes.tune_y_star_gap(qq(2016,1)) = 0.9902546; %-1.82 %% -1.4 %
%dtunes.tune_y_star_gap(qq(2017,4)) = 1.004; %-1.463 %% -0.9 %
%dtunes.tune_y_star_gap(qq(2018,4)) = 1.003; % -1.18 %% -0.57 %
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% tune_dot_y_star_trend_fund, SS = 1.0039515
dtunes.tune_dot_y_star_trend_fund = tseries();

dtunes.tune_dot_y_star_trend_fund(qq(2019,4)) = 1.003541;
dtunes.tune_dot_y_star_trend_fund(qq(2020,2)) = 1.0001;
dtunes.tune_dot_y_star_trend_fund(qq(2020,3)) = 0.999;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,1)) = 0.9995;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,2)) = 1.0008;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,3)) = 1.0013;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,4)) = 1.0017;
dtunes.tune_dot_y_star_trend_fund(qq(2022,1)) = 1.0032;
dtunes.tune_dot_y_star_trend_fund(qq(2022,2)) = 1.0046; %
dtunes.tune_dot_y_star_trend_fund(qq(2022,3)) = 1.0049; %
dtunes.tune_dot_y_star_trend_fund(qq(2022,4)) = 1.0043; %
dtunes.tune_dot_y_star_trend_fund(qq(2023,1)) = 1.00388 ;%1.0037
dtunes.tune_dot_y_star_trend_fund(qq(2023,2)) = 1.00355 ;%1.0034
dtunes.tune_dot_y_star_trend_fund(qq(2023,3)) = 1.00348 ;% 1.0032
%% Temp Tunes

 dtunes.tune_y_star_gap(qq(2021,4)) = 1.01690517;

 dtunes.tune_dot_y_star_trend_fund(qq(2023,4)) =1.003199;
dtunes.tune_dot_y_star_trend_fund(qq(2024,1)) = 1.003122;
dtunes.tune_dot_y_star_trend_fund(qq(2024,2)) = 1.00257792876739;
dtunes.tune_dot_y_star_trend_fund(qq(2024,3)) = 1.00229899962372;
dtunes.tune_dot_y_star_trend_fund(qq(2024,4)) = 1.00200775290219;
dtunes.tune_dot_y_star_trend_fund(qq(2025,1)) = 1.00188452;
dtunes.tune_dot_y_star_trend_fund(qq(2025,2)) = 1.00188768838908;
dtunes.tune_dot_y_star_trend_fund(qq(2025,3)) = 1.001919;
dtunes.tune_dot_y_star_trend_fund(qq(2025,4)) = 1.00208500447485;
dtunes.tune_dot_y_star_trend_fund(qq(2026,1)) = 1.00222684785534;
dtunes.tune_dot_y_star_trend_fund(qq(2026,2)) = 1.00238052;
dtunes.tune_dot_y_star_trend_fund(qq(2026,3)) = 1.00254635040608;
dtunes.tune_dot_y_star_trend_fund(qq(2026,4)) = 1.002713;
dtunes.tune_dot_y_star_trend_fund(qq(2027,1)) = 1.0029090677474;
dtunes.tune_dot_y_star_trend_fund(qq(2027,2)) = 1.00304318319677;
dtunes.tune_dot_y_star_trend_fund(qq(2027,3)) = 1.00316556817266;
dtunes.tune_dot_y_star_trend_fund(qq(2027,4)) = 1.00328324336997;
dtunes.tune_dot_y_star_trend_fund(qq(2028,1)) = 1.00337940375025;
dtunes.tune_dot_y_star_trend_fund(qq(2028,2)) = 1.00346666532315;
dtunes.tune_dot_y_star_trend_fund(qq(2028,3)) = 1.00356389485306;
dtunes.tune_dot_y_star_trend_fund(qq(2028,4)) = 1.00363561820171;
dtunes.tune_dot_y_star_trend_fund(qq(2029,1)) = 1.00370084938771;
dtunes.tune_dot_y_star_trend_fund(qq(2029,2)) = 1.00376035876005;
dtunes.tune_dot_y_star_trend_fund(qq(2029,3)) = 1.00381451148987;
dtunes.tune_dot_y_star_trend_fund(qq(2029,4)) = 1.00386299958909;
dtunes.tune_dot_y_star_trend_fund(qq(2030,1)) = 1.00390467615571;
dtunes.tune_dot_y_star_trend_fund(qq(2030,2)) = 1.00394001885459;
dtunes.tune_dot_y_star_trend_fund(qq(2030,3)) = 1.00396222127952;
dtunes.tune_dot_y_star_trend_fund(qq(2030,4)) = 1.00397759186929;
dtunes.tune_dot_y_star_trend_fund(qq(2031,1)) = 1.00398518605798;
dtunes.tune_dot_y_star_trend_fund(qq(2031,2)) = 1.00398631856148;
dtunes.tune_dot_y_star_trend_fund(qq(2031,3)) = 1.00398408415411;
dtunes.tune_dot_y_star_trend_fund(qq(2031,4)) = 1.0039782338296;







% 
% 
%   dtunes.tune_dot_y_star_trend_fund(qq(2024,1)) = 1.003132 ;
%   
%  dtunes.tune_dot_y_star_trend_fund(qq(2024,3)) = 1.002329 ;
%   
%  dtunes.tune_dot_y_star_trend_fund(qq(2025,1)) = 1.00190452 ; %1.001994 ;
%  
%  dtunes.tune_dot_y_star_trend_fund(qq(2025,3)) = 1.001959 ; %002099 ;
%  
%  
%  dtunes.tune_dot_y_star_trend_fund(qq(2026,2)) = 1.00240452 ; 
%  
%  dtunes.tune_dot_y_star_trend_fund(qq(2026,4)) = 1.002753 ; 
%  
 


end