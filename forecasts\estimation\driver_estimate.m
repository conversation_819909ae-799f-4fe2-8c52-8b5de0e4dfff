% DRIVER_Estimate model
% Odhad podla dynare zoznamu
%%


% TODO: estimation range, do 19q4 ! a porovnat

close all;
clear all;
disp([sprintf('\n'), 'ZB Estimate']);
%% Set options
% load GLOBALSETTINGS (= variable ID with report prefix) or set ID directly
load('GLOBALSETTINGS.mat');
disp(['ID: ', ZB_ID]);
ZB_ID_tmp = ZB_ID;

% load settings
load('../baseline/GLOBALSETTINGS.mat');
settings = load_settings(ZB_ID);
ZB_ID = ZB_ID_tmp; clear ZB_ID_tmp;
settings.report_prefix	= 'develop_estim_ppi_new-ZBestim_new';
settings.oldfrc_prefix	= 'develop_estim_ppi_new';

% override hist range
% settings.hrng = settings.hrng(1):qq(2019,4);

%% Read model and parameters
% all parameters are positive, modify to estimate
SS = setparam_ZB_new_estim([],settings);
SS.filtering = true;

SS = ssmodel_ZB_new(SS);
m_filt = readmodel('../../Utilities-core/model_september/inc_g3p_ZB_new_estim.model',SS);

% remove nonstationarity
SS.filtering = false;
m_fcs = readmodel('../../Utilities-core/model_september/inc_g3p_ZB_new_estim.model',SS);

%% Load data

% original database
dbo = dbload(settings.origdata_fb_name);

% pre-filter database
d = dbload(settings.prefilter_fb_name);

%% Estimation setup
E = struct;

% parameters to estimate begin with p_ in model code
list_ = get(m_filt,'pList');

E.a_ystar_AR = { m_filt.a_ystar_AR, 0.0, 1.0, logprior.lognormal(0.5,0.1)};
E.a_ystar_IR = { m_filt.a_ystar_IR, 0.0, 2.0, logprior.lognormal(0.1,0.05)};
E.a_ystar_RER = { m_filt.a_ystar_RER, 0.0, 1.0, logprior.lognormal(0.001,0.0005)};
E.a_pstar_ystar = { m_filt.a_pstar_ystar, 0.0, 2.0, logprior.lognormal(0.1,0.05)};
E.a_pstar_AR = { m_filt.a_pstar_AR, 0.0, 1.0, logprior.beta(0.2,0.02)};
E.a_pstar_RER = { m_filt.a_pstar_RER, 0.0, 1.0, logprior.lognormal(0.001,0.0005)};
E.a_istar_ystar = { m_filt.a_istar_ystar, 0.0, 2.0, logprior.lognormal(0.5,0.2)};
E.a_istar_AR = { m_filt.a_istar_AR, 0.0, 1.0, logprior.beta(0.5,0.1)};
E.rho_prem_usdeur = { m_filt.rho_prem_usdeur, 0.0, 1.0, logprior.beta(0.5,0.1)};

E.rho_usdeur            = { m_filt.rho_usdeur, 0.0, 1.0, logprior.beta(0.7,0.02)};
%E.rho_p_BrentUSD_tilde = { m_filt.rho_p_BrentUSD_tilde, 0.0, 1.0, logprior.beta(0.7,0.02)};
E.rho_Pstar_energy_tilde    = { m_filt.rho_Pstar_energy_tilde, 0.0, 1.0, logprior.beta(0.7,0.02)};
E.rho_y_star_trend      = { m_filt.rho_y_star_trend, 0.0, 1.0, logprior.beta(0.5,0.1)};
E.rho_Pstar_RP_tilde     = { m_filt.rho_Pstar_RP_tilde, 0.0, 1.0, logprior.beta(0.7,0.1)};

E.rho_shadow_rate       = { m_filt.rho_shadow_rate, 0.0, 1.0, logprior.beta(0.5,0.1)};
E.rho_dot_z_eq          = { m_filt.rho_dot_z_eq, 0.0, 1.0, logprior.beta(0.5,0.1)};

E.std_eps_Istar            = { m_filt.std_eps_Istar, 0.001, 10, logprior.invgamma(0.05,0.05)};
E.std_eps_y_star_gap            = { m_filt.std_eps_y_star_gap, 0.001, 10, logprior.invgamma(0.03,0.004)};
E.std_eps_pstar_other_tilde          = { m_filt.std_eps_pstar_other_tilde, 0.001, 10, logprior.invgamma(0.05,0.05)};
% E.std_eps_dot_y_star_trend = { m_filt.std_eps_dot_y_star_trend, 0.001, 10, logprior.invgamma(0.05,0.05)};
E.std_eps_USDEUR           = { m_filt.std_eps_USDEUR, 0.001, 10, logprior.invgamma(0.05,0.05)};
%E.std_eps_pstar_tilde      = { m_filt.std_eps_pstar_tilde, 0.0001, 10, logprior.invgamma(0.05,0.05)};
E.std_eps_pstar_energy_tilde   = { m_filt.std_eps_pstar_energy_tilde, 0.001, 10, logprior.invgamma(0.070, 0.05)};
%E.std_eps_p_BrentUSD       = { m_filt.std_eps_p_BrentUSD, 0.001, 10, logprior.invgamma(0.07,0.005)};
E.std_eps_pstar_RP_tilde    = { m_filt.std_eps_pstar_RP_tilde, 0.001, 10, logprior.invgamma(0.05,0.05)};
E.std_eps_prem_usdeur          = { m_filt.std_eps_prem_usdeur, 0.001, 10, logprior.invgamma(0.05,0.05)};
E.std_eps_dot_z_eq         = { m_filt.std_eps_dot_z_eq, 0.001, 10, logprior.invgamma(0.05, 0.02)};

%% Pre Estimation analysis
% List of parameters for which we want to compute the Fisher matrix,
% further exclude the nonstationary variables
% plist = fieldnames(E);
plist = setdiff(fieldnames(get(m_fcs,'params')),fieldnames(get(m_fcs,'std')));

% #of resamples of model
nsim = 50;
% Simulate a total of nsim artificial data, histlength.
d_rsmp = resample(m_filt,[],settings.hrng,nsim,'deviation',false);
[obj,grad,hess,v] = diffloglik(m_filt,d_rsmp,settings.hrng,plist,'deviation',true,'relative',false,'progress',true,'exclude',get(m_filt,'Ynames'));
hess = mean(hess,3);
[F,Fi,delta,freq] = fisher(m_filt,length(settings.hrng),plist,'deviation',true,'progress',true,'exclude',get(m_filt,'Ynames'));

% disp('Compare diagonal elements');
% [diag(hess),diag(F)]
% format();

% SVD analysis
[u1,s1] = svd(hess);
[u2,s2] = svd(F);

s1 = diag(s1);
s2 = diag(s2);
s1 = s1 / s1(1);
s2 = s2 / s2(1);

format('shortEng');
disp('Singular values (normalised and ordered)');
disp('Time domain');
disp(s1.');
disp('Frequency domain');
disp(s2.');

disp('Combinations of parameters ordered by degree of identification.');
disp('Best identified columns ordered first');

disp('Time domain');
[char(plist),num2str(u1,'| %-4.3f')]
disp('Frequency domain');
[char(plist),num2str(u2,'| %-4.3f')]
format();

%% PRIORS
[pr,po,fig,ax,prlin,polin,blin,tit] = grfun.plotpp(E,[],'subplot',[3,3]);
set(blin,'marker','.','markerSize',11);
set([ax,tit],'fontSize',8);
%% Estimate
[E_est,POS,COV,HESS,M_est,V,~,~,DELTA,PDELTA] = estimate(m_filt,d,settings.hrng,E,'maxFunEvals',20000,'objective','-loglik','noSolution','penalty','relative',false);

disp('Parameter          | Original | Estimate| Difference in %');
[ char(fieldnames(E)), num2str(structfun(@(x) ( x{1} ), E, 'UniformOutput',true),'| %-g'),...
    num2str(structfun(@(x) ( x(1) ), E_est, 'UniformOutput',true),'| %-g') ...
    num2str(100*(structfun(@(x) (x(1)), E_est, 'UniformOutput',true)./structfun(@(x,y) ( x{1} ),E, 'UniformOutput',true)-1),'| %-6.2f')...
]

%% Paremeters Variance
plist = fieldnames(E_est);
std1 = sqrt(diag(COV));
std2 = sqrt(diag(inv(HESS{1})));
disp('Std devs of parameter estimates: asymptotic hessian | Optim Tbx hessian');
[ char(plist), num2str(std1,'| %-g'), num2str(std2,'| %-g')  ]

%% Estimation stability
n = neighbourhood(M_est,POS,0.95:0.01:1.05,'linkaxes',true,'subplot',[3,3]);
%% Posterior
N = 10000;
[THETA,LOGPOST,AR,SCALE,FINALCOV] = arwm(POS,N,'progress',true,'adaptScale',2,'adaptProposalCov',1,'burnin',0.20);
AR(end)
est_stat = stats(POS,THETA,LOGPOST);
[pr,po,fig,ax,prlin,polin,blin,tit] = grfun.plotpp(E,THETA,'subplot',[3,3]);
set(prlin,'lineStyle','--');
set(polin,'color','black','linewidth',1);
set(blin,'marker','.','markerSize',11);
set([ax,tit],'fontSize',8);

%% Save
m = M_est;
save([settings.outdata_dir '\' settings.report_prefix '-kalman-fb.mat'],'m');

SS_fields= fieldnames(SS);
for k=1:numel(SS_fields)
    if isfield(E_est,SS_fields{k})
        SS.(SS_fields{k}) = E_est.(SS_fields{k});
        disp(['Updated from estimation: ' SS_fields{k}]);
    end;
end
SS.filtering = false;
m = readmodel('../../Utilities-core/model_new/inc_g3p_ZB_new_estim.model',SS);
save([settings.outdata_dir '\' settings.report_prefix '-model-fb.mat'],'m');
