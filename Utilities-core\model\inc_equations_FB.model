!transition_equations

% IS Curve
log(y_star_gap) = a_ystar_AR*log(y_star_gap{-1})...
    - a_ystar_IR*log(r_star_gap{-1})...
    + a_ystar_RER*log(z_gap)...
    + eps_y_star_gap + eps_exp_y_star_gap;

% Phillips Curve
log(dot_pstar_other_tilde) = a_pstar_AR*log(dot_pstar_other_tilde{-1}) ...
    + (1 - a_pstar_AR)*log(dot_pstar_other_tilde{+1})...
    + a_pstar_ystar*log(y_star_gap) ...
    + a_pstar_RER*log(z_gap)...
    + eps_pstar_other_tilde + eps_exp_pstar_other_tilde;

% MP Rule
log(i_star_eu) = a_istar_AR*(log(i_star_eu{-1}))...
    +(1-a_istar_AR) * (log(i_star_eq) ...
    + a_istar_ystar*log(y_star_gap)...
    + a_istar_pi*(log(dot_cpi_star_tilde4{+4})-log(dot_cpi_star_tilde_^4)))...
    + eps_Istar + eps_exp_Istar;

% UIP Condition
rho_usdeur*log(dot_usdeur{+1}) - (1-rho_usdeur)*log(dot_usdeur) + ...
    log(i_star) - log(i_star_us) + log(prem_usdeur) + eps_USDEUR + eps_exp_USDEUR = 0;

log(prem_usdeur_gap) = (log(prem_usdeur)+log(prem_usdeur{-1}))/2 - (log(i_star_us_)-log(i_star_eq));

% Linking CPI to PPI
log(dot_cpi_star_tilde)-log(dot_cpi_star_tilde_) ...
    = rho_cpistar*(log(dot_cpi_star_tilde{-1})-log(dot_cpi_star_tilde_)) + ...
    (1-rho_cpistar)*a_cpistar_pstar*(log(dot_pstar_tilde)-log(dot_pstar_tilde_)) + eps_dot_cpi_star_tilde + eps_exp_dot_cpi_star_tilde;


%**************************************************************************
% Needed to resolve non-stationarity
usdeur                  = usdeur{-1}*dot_usdeur - a_usdeur*(usdeur-usdeur_); % Level of USDEUR

% Definitions
log(dot_pstar_tilde)	= energy_share_ppi_star*energy_share_ppi_star_gap*log(dot_pstar_energy_tilde/dot_pstar_RP_tilde) ...
                            + (1-energy_share_ppi_star*energy_share_ppi_star_gap)*log(dot_pstar_other_tilde) ...
                            + eps_pstar_tilde + eps_exp_pstar_tilde;

log(z_gap/z_gap{-1}) + log(dot_z_eq)    = - log(dot_usdeur) - log(dot_pstar_tilde);
dot_y_star                              = (y_star_gap / y_star_gap{-1}) * dot_y_star_trend;
dot_y_star_trend                        = dot_y_star_trend_fund * dot_y_star_trend_shift;
log(r_star_gap)                         = log(i_star_eu) - log(dot_cpi_star_tilde{+1}) - (log(i_star_eq) - log(dot_cpi_star_tilde_));
dot_cpi_star_tilde4                     = dot_cpi_star_tilde * dot_cpi_star_tilde{-1} * dot_cpi_star_tilde{-2} * dot_cpi_star_tilde{-3};
log(i_star) - log(i_star_eu)            = log(shadow_rate_gap);
dot_pstar_tilde4                        = dot_pstar_tilde * dot_pstar_tilde{-1} * dot_pstar_tilde{-2} * dot_pstar_tilde{-3};
dot_pstar_other_tilde4                  = dot_pstar_other_tilde * dot_pstar_other_tilde{-1} * dot_pstar_other_tilde{-2} * dot_pstar_other_tilde{-3};
dot_usdeur4                             = dot_usdeur * dot_usdeur{-1} * dot_usdeur{-2} * dot_usdeur{-3};
dot_pstar_energy_tilde4                 = dot_pstar_energy_tilde * dot_pstar_energy_tilde{-1} * dot_pstar_energy_tilde{-2} * dot_pstar_energy_tilde{-3};

% AR processes
log(prem_usdeur)               = rho_prem_usdeur*log(prem_usdeur{-1}) + (1-rho_prem_usdeur)*log(prem_usdeur_) + eps_prem_usdeur + eps_exp_prem_usdeur;
log(dot_z_eq)                  = rho_dot_z_eq*log(dot_z_eq{-1}) + (1-rho_dot_z_eq)*log(dot_z_eq_) + eps_dot_z_eq + eps_exp_dot_z_eq;
log(i_star_eq{0})              = rho_i_star_eq*log(i_star_eq{-1})+(1-rho_i_star_eq)*log(i_star_)+eps_i_star_eq+eps_exp_i_star_eq;
log(i_star_us{0})              = rho_i_star_us*log(i_star_us{-1})+(1-rho_i_star_us)*log(i_star_us_)+eps_i_star_us+eps_exp_i_star_us;
log(dot_pstar_energy_tilde)    = rho_pstar_energy_tilde* log(dot_pstar_energy_tilde{-1}) ...
                                + (1-rho_pstar_energy_tilde)* log(dot_pstar_energy_tilde_) + eps_pstar_energy_tilde  + eps_exp_pstar_energy_tilde;
log(energy_share_ppi_star_gap) = log(dot_pstar_energy_tilde/dot_pstar_other_tilde) + eps_energy_share_ppi_star_gap + eps_exp_energy_share_ppi_star_gap;
log(dot_pstar_RP_tilde)	       = rho_pstar_RP_tilde*log(dot_pstar_RP_tilde{-1}) + eps_pstar_RP_tilde + eps_exp_pstar_RP_tilde;
log(shadow_rate_gap)           = rho_shadow_rate*log(shadow_rate_gap{-1}) + eps_shadow_rate + eps_exp_shadow_rate;
log(dot_y_star_trend_fund)     = rho_y_star_trend_fund*log(dot_y_star_trend_fund{-1}) ...
                                + (1-rho_y_star_trend_fund)*log(dot_y_star_trend_) + eps_dot_y_star_trend_fund  + eps_exp_dot_y_star_trend_fund;

% One-off shifter
dot_y_star_trend_shift	= 1 + eps_dot_y_star_trend_shift + eps_exp_dot_y_star_trend_shift;

% Expectations
log(e_dot_pstar_other_tilde)	= log(dot_pstar_other_tilde{+1});
log(e_dot_usdeur)               = log(dot_usdeur{+1});
log(e4_dot_pstar_tilde4)        = log(dot_pstar_tilde4{+4});
log(e4_dot_cpi_star_tilde4)     = log(dot_cpi_star_tilde4{4});

% <end>
