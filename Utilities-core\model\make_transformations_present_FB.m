function [dout,varargout] = make_transformations_present_FB(d, h, sr_opt, p, varargin)

% FUNCTION NAME:
%   make_transformations_present_FB
%
% DESCRIPTION:
%   Enrich the given database d with useful variable transformations prefixed
%   with ne_zz_. Filtering and forecasting part.
%
% INPUT:
%   d - database
%   h - histcore
%   sr_opt - report options 
%   p - parameters
%   varargin - selecting connection to data
%
% OUTPUT:
%   dout - (struct) database with extra vars
%   vargout - export islog structure
%
% ASSUMPTIONS AND LIMITATIONS:
%   None
%
% REVISION HISTORY:
%   11/04/2023 - FB,TP
%       * revision


%% --------------------------------------------------------------------- %%

%% BODY OF THE FUNCTION

% initializing
default = {                            ...
    'fcast_plan',       NaN            ...
    'fcast_link',       'hp'          ...                        
    'adhoc_data',       dbempty()      ...
    };
options = passopt(default,varargin{1:end});

islog = dbempty();

% % % % if ~p.filtering
% % % %     fix_sfore.all = sr_opt.start_pred;
% % % % end
% % % % 
% % % % % analyze fcast_plan and adhoc_data
% % % % if isa(options.fcast_plan, 'plan')
% % % %     fix = get_exogenized(options.fcast_plan);
% % % %     list_ = fieldnames(fix);
% % % %     
% % % %     adhoc_list_pom = fieldnames(options.adhoc_data);
% % % %     adhoc_list = {};
% % % %     for i = 1 : length(adhoc_list_pom)
% % % %         if any(~isnan(options.adhoc_data.(adhoc_list_pom{i})))
% % % %            adhoc_list = {adhoc_list{1:end},adhoc_list_pom{i}};
% % % %         end       
% % % %     end    
% % % %    
% % % %     for i = 1 : length(list_)
% % % %         if isequal(get(fix.(list_{i}), 'first'), fix_sfore.all)
% % % %             if sum(strcmp(adhoc_list,list_{i}))
% % % % 				aux_ = find(~isnan(options.adhoc_data.(list_{i})));
% % % %                 fix_sfore.(list_{i}) = aux_(1);  
% % % %             else    
% % % %                 fix_sfore.(list_{i}) = get(fix.(list_{i}), 'end')+1;
% % % %             end  
% % % %         end
% % % %     end
% % % %     
% % % %     
% % % % end

% transformations on historical range
[d, islog_] = mk_trans_present_history(d, h, sr_opt, p);
islog = dbmerge(islog, islog_);

% transformations on forecast range
% % % % if ~ p.filtering
% % % %     switch options.fcast_link
% % % %         case {'dot', 'QQ', 'qq', 'QoQ', 'qoq'}
% % % %             disp('make_transformations_present: linking forecast to QoQ growth');
% % % %             [d,islog_] = mk_trans_present_forecast_dot(d, h, fix_sfore, sr_opt.end_comp);
% % % %         case {'level', 'L', 'g3', 'model'}
% % % %             disp('make_transformations_present: linking forecast to model level');
% % % %             [d,islog_] = mk_trans_present_forecast_level(d, h, fix_sfore, sr_opt.end_comp);
% % % %         case {'HP', 'hp'}
% % % %             disp('make_transformations_present: linking forecast to HP level of data');
% % % %             [d,islog_] = mk_trans_present_forecast_hp(d, h, fix_sfore, sr_opt.end_comp);
% % % %         case {'dot4', 'YY', 'yy', 'YoY', 'yoy'}
% % % %             disp('make_transformations_present: linking forecast to YoY growth');
% % % %             [d,islog_] = mk_trans_present_forecast_dot4(d, h, fix_sfore, sr_opt.end_comp);
% % % %         otherwise
% % % %             disp('make_transformations_present: incorrect option for fcast_link; running default option');
% % % %             disp('make_transformations_present: linking forecast to QoQ growth');
% % % %             [d,islog_] = mk_trans_present_forecast_dot(d, h, fix_sfore, sr_opt.end_comp);
% % % %     end
% % % % end
islog = dbmerge(islog, islog_);

% output
dout = d;
if nargout > 1
    varargout{1} = islog;
end

end
% --------------------------------------------------------------------- %%

%% VARIABLES FOR REPORTING - HISTORY

function [dout,varargout] = mk_trans_present_history(d, h, sr_opt, p)

islog = dbempty();
dout  = d;

h.i_star                    = (1+h.net4_euribor3m/100)^(1/4);
h.i_star_eu                 = (1+h.net4_shadoweuribor3m/100)^(1/4);
h.i_star_us                 = (1+h.net4_ussofr3m/100)^(1/4);
h.i_star_eq                 = (1+h.equilibrium_i_star/100)^(1/4);
h.i_star_shadow             = (1+h.net4_shadoweuribor3m/100)^(1/4);
h.i_star_eonia              = (1+h.net4_eonia3m/100)^(1/4);

h.y_star                    = h.gdp_emu;
if ~sr_opt.FB_gdp_decomp
    h.y_star_gap                = exp(h.gdp_emu_gap);
    h.y_star_trend              = h.gdp_emu_trend;                          % in order to have observed y_star_trend
    h.y_star_trend_fund         = h.gdp_emu_trend_fund;                     % in order to have observed y_star_trend_fund
    h.dot_y_star_trend_shift    = (1 + h.gdp_emu_trend_shift / 100)^(1/4);
end

 if ~sr_opt.FB_prem_decomp
     h.prem_usdeur =  h.prem_usdeur;
 end


h.pstar_energy_tilde        = h.ppi_emu_energy;
h.pstar_other_tilde         = h.ppi_emu_other;
h.pstar_tilde               = h.ppi_emu;

h.cpi_star_tilde            = h.cpi_emu;

% Create ad-hoc comments for non-model variables
d.i_star_eonia = h.i_star_eonia;
d.i_star_eonia = comment(d.i_star_eonia,'3M Eonia Interest Rate');

%% Data from histcore only
% 
% trans_names = {'p_oil'};
% 
% for i = 1:length(trans_names)
%     dout.(['ne_zz_' trans_names{i}])             = h.(trans_names{i});
%     
%     dout.(['ne_dot_' trans_names{i}])            = (h.(trans_names{i}) / h.(trans_names{i}){-1} );
%     dout.(['ne_zz_dot_' trans_names{i}])         = 100*((h.(trans_names{i}) / h.(trans_names{i}){-1})^4 )-100;
%     dout.(['ne_zz_dot_' trans_names{i}])         = comment(dout.(['ne_zz_dot_' trans_names{i}]),[char(comment(h.(trans_names{i}))) ' (QoQ)']);
%     islog.(['ne_zz_dot_' trans_names{i}])        = true;
%     
% 	tmpdot = h.(trans_names{i}) / h.(trans_names{i}){-4};
%     dout.(['ne_zz_dot_' trans_names{i} '4'])         = 100*tmpdot - 100;
%     dout.(['ne_zz_dot_' trans_names{i} '4'])         = comment(dout.(['ne_zz_dot_' trans_names{i} '4']),strrep(comment(dout.(['ne_zz_dot_' trans_names{i}])),'QoQ','YoY'));
%     islog.(['ne_zz_dot_' trans_names{i} '4'])        = true;
% end

%% Transformations

trans_names	= {'pstar_other_tilde', 'pstar_tilde', 'cpi_star_tilde','pstar_energy_tilde', ...
               'y_star', 'usdeur'};
trans_names_components = {'pstar_energy_tilde','pstar_other_tilde'}; % exp^4 makes these components non-additive  

% PPI components shares
p.pstar_energy_tilde_share	= h.weight_dot_pstar_energy_tilde;
p.pstar_other_tilde_share	= 1-h.weight_dot_pstar_energy_tilde;
           
for i = 1:length(trans_names)
    dout.(['ne_zz_' trans_names{i}])          	= h.(trans_names{i});
    dout.(['ne_dot_' trans_names{i}])        	= (h.(trans_names{i}) / h.(trans_names{i}){-1} );
    
    if find(strmatch(trans_names{i},trans_names_components))
        dout.(['ne_zz_dot_' trans_names{i}]) 	= 100*((h.(trans_names{i}) / h.(trans_names{i}){-1})^4)-100;
        dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i}]) = p.([trans_names{i} '_share']){-1}*dout.(['ne_zz_dot_' trans_names{i}]);
        dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i}]) = comment(dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i}]),[char(comment(d.dot_pstar_tilde)) ', ' char(trans_names{i}) ' contribution']);
        islog.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i}]) = false;
    else
        dout.(['ne_zz_dot_' trans_names{i}])	= 100*((h.(trans_names{i}) / h.(trans_names{i}){-1})^4 )-100;
    end

	dout.(['ne_zz_dot_' trans_names{i}])        = comment(dout.(['ne_zz_dot_' trans_names{i}]),comment(d.(['dot_' trans_names{i}])));
    islog.(['ne_zz_dot_' trans_names{i}])       = true;
        
    if find(strmatch(trans_names{i},trans_names_components))
        tmpdot = (h.(trans_names{i}) / h.(trans_names{i}){-4} - 1);
        dout.(['ne_zz_dot_' trans_names{i} '4'])	= 100*tmpdot;
        dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i} '4']) = p.([trans_names{i} '_share']){-4}*dout.(['ne_zz_dot_' trans_names{i} '4']);
        dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i} '4']) = comment(dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i} '4']),[char(strrep(comment(d.dot_pstar_tilde),'QoQ','YoY')) ', ' char(trans_names{i}) ' contribution']);
        islog.(['ne_zz_dot_pstar_tilde_contrib_' trans_names{i} '4']) = false;
    else
        tmpdot = h.(trans_names{i}) / h.(trans_names{i}){-4};
        dout.(['ne_zz_dot_' trans_names{i} '4'])	= 100*tmpdot - 100;
    end
    
    dout.(['ne_zz_dot_' trans_names{i} '4'])       	= comment(dout.(['ne_zz_dot_' trans_names{i} '4']),strrep(comment(d.(['dot_' trans_names{i}])),'QoQ','YoY'));
    islog.(['ne_zz_dot_' trans_names{i} '4'])      	= true;
end

% Remedy for PPI components discrepancies
%  - YoY, identified discrepancy goes to energy component (414 approach)
%  - QoQ, annualisation of components is problematic; QoQ components are rescaled to match the overall QoQ dynamics
ppi_dif_YoY = (dout.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde4 + dout.ne_zz_dot_pstar_tilde_contrib_pstar_other_tilde4) - dout.ne_zz_dot_pstar_tilde4;
dout.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde4 = dout.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde4 - ppi_dif_YoY;

ppi_scale_QoQ = dout.ne_zz_dot_pstar_tilde / (dout.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde + dout.ne_zz_dot_pstar_tilde_contrib_pstar_other_tilde);

for i = 1:length(trans_names_components)
    dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names_components{i}]) = dout.(['ne_zz_dot_pstar_tilde_contrib_' trans_names_components{i}])*ppi_scale_QoQ;
end

%%  Interest rates

trans_names = {'i_star','i_star_eu','i_star_shadow','i_star_eq', 'i_star_eonia','i_star_us'};

for i = 1:length(trans_names)
    dout.(['ne_zz_' trans_names{i}]) = 100*(h.(trans_names{i})^4 -1); % d.(['zz_' trans_names{i}]);
    
    if  strcmp(trans_names{i},'i_star_shadow') == 1
        dout.(['ne_zz_' trans_names{i}]) = comment(dout.(['ne_zz_' trans_names{i}]),{[cell2mat(comment(d.(trans_names{1}))),', Shadow Rate']});
    else
        dout.(['ne_zz_' trans_names{i}]).Comment = get(d.(trans_names{i}),'Comment');
    end
    
    islog.(['ne_zz_' trans_names{i}]) = true;
end

%% Output and its components

% y_star_trend_total
dout.(['zz_', 'y_star_trend'])  = dout.ne_zz_y_star / dout.y_star_gap;
dout.(['zz_', 'y_star_trend'])  = comment(dout.zz_y_star_trend, 'Foreign Output Trend');
islog.(['zz_', 'y_star_trend']) = true;

% y_star_trend_fund
aux_date        = get(dout.zz_y_star_trend, 'first');
aux_date_end    = get(dout.zz_y_star_trend, 'last');

tmp_ = cumprod(dout.dot_y_star_trend_fund(aux_date+1:aux_date_end));
dout.zz_y_star_trend_fund                           = dout.zz_y_star_trend;
dout.zz_y_star_trend_fund(aux_date+1:aux_date_end)  = dout.zz_y_star_trend(aux_date) .* tmp_;
dout.zz_y_star_trend_fund                           = comment(dout.zz_y_star_trend_fund, 'Foreign Output Trend Fund');
islog.zz_y_star_trend_fund                          = true;

if  sr_opt.FB_gdp_decomp == 0
    % Trends
    trans_names = {'y_star_trend_fund','y_star_trend'};
  
    for i = 1:length(trans_names)
        %         if max(cell2mat(strfind(trans_names_level,trans_names{i}))) == 1
        dout.(['ne_zz_' trans_names{i}])             = h.(trans_names{i});
        %         end
        dout.(['ne_dot_' trans_names{i}])            = (h.(trans_names{i}) / h.(trans_names{i}){-1} );
        dout.(['ne_zz_dot_' trans_names{i}])         = 100*((h.(trans_names{i}) / h.(trans_names{i}){-1})^4 )-100;
        dout.(['ne_zz_dot_' trans_names{i}]).Comment = get(d.(['dot_' trans_names{i}]),'Comment');
        islog.(['ne_zz_dot_' trans_names{i}])        = true;
        
        tmpdot = h.(trans_names{i}) / h.(trans_names{i}){-4};
        dout.(['ne_zz_dot_' trans_names{i} '4'])         = 100*tmpdot - 100;
        dout.(['ne_zz_dot_' trans_names{i} '4'])         = comment(dout.(['ne_zz_dot_' trans_names{i} '4']),strrep(comment(d.(['dot_' trans_names{i}])),'QoQ','YoY'));
        islog.(['ne_zz_dot_' trans_names{i} '4'])        = true;
    end
    
    % Gap
    trans_names = {'y_star_gap'};
    
    for i = 1:length(trans_names)
        dout.(['ne_zz_' trans_names{i}])            = 100*(h.(trans_names{i}) -1);
        dout.(['ne_zz_' trans_names{i}]).Comment    = get(d.([trans_names{i}]),'Comment');
        islog.(['ne_zz_' trans_names{i}])           = true;
    end
        
    % One-off shift
    trans_names = {'dot_y_star_trend_shift'};
    
    for i = 1:length(trans_names)
        dout.(['ne_zz_' trans_names{i}])            = 100*(h.(trans_names{i})^4 -1);
        dout.(['ne_zz_' trans_names{i}]).Comment    = get(d.(['zz_' trans_names{i}]),'Comment');
        islog.(['ne_zz_' trans_names{i}])           = true;
    end
    
    
end


if  sr_opt.FB_prem_decomp == 0
   
    % Foreign premie
    trans_names = {'prem_usdeur'};
    
    for i = 1:length(trans_names)
        dout.(['ne_zz_' trans_names{i}])            = h.(trans_names{i});
        dout.(['ne_zz_' trans_names{i}]).Comment    = get(d.(['zz_' trans_names{i}]),'Comment');
        islog.(['ne_zz_' trans_names{i}])           = false;
    end
end


%% Out-of-model variables for reporting
% Brent USD oil price 
trans_names = {'p_brentUSD_tilde', 'p_brent_tilde'};

for i = 1:length(trans_names)
    dout.(['ne_zz_' trans_names{i}])          	= h.(trans_names{i});
    dout.(['ne_dot_' trans_names{i}])        	= (h.(trans_names{i}) / h.(trans_names{i}){-1} );
    dout.(['ne_zz_dot_' trans_names{i}])        = 100*((h.(trans_names{i}) / h.(trans_names{i}){-1})^4 )-100;
	dout.(['ne_zz_dot_' trans_names{i}])        = comment(dout.(['ne_zz_dot_' trans_names{i}]),[char(comment(h.(trans_names{i}))) ' (QoQ)']);
    tmpdot                                      = h.(trans_names{i}) / h.(trans_names{i}){-4};
    dout.(['ne_zz_dot_' trans_names{i} '4'])	= 100*tmpdot - 100;
   	dout.(['ne_zz_dot_' trans_names{i} '4'])	= comment(dout.(['ne_zz_dot_' trans_names{i} '4']),strrep(comment(dout.(['ne_zz_dot_' trans_names{i}])),'QoQ','YoY'));
end

% Time-varying dot PPI weight
dout.weight_dot_pstar_energy_tilde = h.weight_dot_pstar_energy_tilde;

%%
if nargout > 1
    varargout{1} = islog;
end

end

%<end>



%% --------------------------------------------------------------------- %%

%% VARIABLES FOR REPORTING - FORECAST - HP LEVEL

% % % % function [dout,varargout] = mk_trans_present_forecast_hp(d, h, fix_sfore, efore)
% % % % 
% % % % islog = dbempty();
% % % % dout  = d;
% % % % 
% % % % h.i_star            = (1+h.net4_euribor3m/100)^(1/4);
% % % % h.i_star_eq         = (1+h.equilibrium_i_star/100)^(1/4);
% % % % h.i_star_eu         = (1+h.net4_shadoweuribor3m/100)^(1/4);
% % % % h.i_star_shadow     = (1+h.net4_shadoweuribor3m/100)^(1/4);
% % % % 
% % % % 
% % % % %% Data from histcore only
% % % % % 
% % % % % trans_names = {'p_oil'};
% % % % % 
% % % % % for i = 1:length(trans_names)
% % % % %     dout.(['ne_zz_' trans_names{i}])             = h.(trans_names{i});
% % % % %     
% % % % %     dout.(['ne_dot_' trans_names{i}])            = (h.(trans_names{i}) / h.(trans_names{i}){-1} );
% % % % %     dout.(['ne_zz_dot_' trans_names{i}])         = 100*((h.(trans_names{i}) / h.(trans_names{i}){-1})^4 )-100;
% % % % %     dout.(['ne_zz_dot_' trans_names{i}])         = comment(dout.(['ne_zz_dot_' trans_names{i}]),[char(comment(h.(trans_names{i}))) ' (QoQ)']);
% % % % %     islog.(['ne_zz_dot_' trans_names{i}])        = true;
% % % % %     
% % % % % 	tmpdot = h.(trans_names{i}) / h.(trans_names{i}){-4};
% % % % %     dout.(['ne_zz_dot_' trans_names{i} '4'])         = 100*tmpdot - 100;
% % % % %     dout.(['ne_zz_dot_' trans_names{i} '4'])         = comment(dout.(['ne_zz_dot_' trans_names{i} '4']),strrep(comment(dout.(['ne_zz_dot_' trans_names{i}])),'QoQ','YoY'));
% % % % %     islog.(['ne_zz_dot_' trans_names{i} '4'])        = true;
% % % % % end
% % % % 
% % % % 
% % % % %% Interest rates
% % % % 
% % % % trans_names = {'i_star','i_star_eq','i_star_eu'}; %,'y_star_gap'
% % % % 
% % % % for i = 1:length(trans_names)
% % % %     if isfield(fix_sfore,trans_names{i})
% % % %         if ~isequal(trans_names{i},'i')
% % % %             sfore_aux = fix_sfore.(trans_names{i});
% % % %         else
% % % %             sfore_aux = fix_sfore.all;
% % % %             disp('make_transformations_present: interest rate fix reported from model, not from database');
% % % %         end
% % % %     else 
% % % %         sfore_aux = fix_sfore.all;
% % % %     end
% % % %     dout.(['ne_zz_' trans_names{i}]) = d.(['ne_zz_' trans_names{i}]);
% % % %     dout.(['ne_zz_' trans_names{i}])(sfore_aux:efore) = d.(['zz_' trans_names{i}])(sfore_aux:efore);
% % % %     dout.(['ne_zz_' trans_names{i}]).Comment = get(d.(trans_names{i}), 'Comment');
% % % %     islog.(['ne_zz_' trans_names{i}]) = true;
% % % % end
% % % % 
% % % % %% Other variables
% % % % % keyboard; 
% % % % trans_names = {'pstar_tilde','cpi_star_tilde', 'pstar_other_tilde', 'y_star', 'pstar_energy_tilde','usdeur'...
% % % %     
% % % %     };    
% % % % 
% % % % 
% % % % 
% % % % % link variables
% % % % for i = 1:length(trans_names)
% % % % 	
% % % %     if isfield(fix_sfore,['dot_' trans_names{i}])
% % % %         if ~isequal(['dot_' trans_names{i}],'dot_usdeur')
% % % %             sfore_aux = fix_sfore.(['dot_' trans_names{i}]); %for usdeur
% % % %         else
% % % %             sfore_aux = fix_sfore.all; %for the rest of vars, empty in this case because no "dot"
% % % %         end
% % % %     else 
% % % %         sfore_aux = fix_sfore.all; %rest of the vars
% % % %     end
% % % %     
% % % %     % link level in sfore_aux period
% % % %     tmp_ = hpf(h.(trans_names{i}), inf, 'lambda', 0.5); %sfore_aux is the last point of historical data
% % % %     
% % % % 
% % % % %%%%%%%%%%%%%	%% Preberanie QoQ na prvy kvartal prognozy (ignoruje HP level) %%%%%%%%%%%%%%%%%%%%%
% % % % 	% 1.SZ 2017 - investicie
% % % % 	switch trans_names{i}
% % % % 		case ''
% % % % 			disp('make_transformations_present: Preberanie QoQ na prvy kvartal prognozy (ignoruje HP level)!!!');
% % % % 			dout.ne_zz_j(sfore_aux) = d.ne_zz_j(sfore_aux-1) * d.dot_j(sfore_aux);
% % % % 		otherwise % tato cast je standardna
% % % % 			%dout.(['ne_zz_' trans_names{i}])(sfore_aux) = 100*log(tmp_(sfore_aux-1) * d.(['dot_' trans_names{i}])(sfore_aux)); % error due to 100*log
% % % %             dout.(['ne_zz_' trans_names{i}])(sfore_aux) = (tmp_(sfore_aux-1) * d.(['dot_' trans_names{i}])(sfore_aux));
% % % %   end
% % % % %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% % % % % keyboard
% % % %     aux_ = dout.(['ne_zz_' trans_names{i}])(sfore_aux) / (dout.(['ne_zz_' trans_names{i}])(sfore_aux-1)); %QoQ change
% % % %     
% % % %     dout.(['ne_dot_' trans_names{i}])(sfore_aux) = aux_; % rewrite first FC quarter QoQ
% % % %     
% % % %     aux_ = 100*(dout.(['ne_dot_' trans_names{i}]))^4 - 100; 
% % % %     dout.(['ne_zz_dot_' trans_names{i}])(sfore_aux) = aux_(sfore_aux); %rewrite first FC quarter QoQ annualized
% % % %     
% % % %     
% % % %     
% % % %    % link QoQ growth for the rest of the forecast
% % % %    if isequal(trans_names{i},'pC')
% % % %         dout.(['ne_zz_dot_' trans_names{i}])(sfore_aux+1:efore) = d.(['zz_dot_' trans_names{i}])(sfore_aux+1:efore)+h_inf.tax_pie_pC(sfore_aux+1:efore);
% % % %    else    
% % % %         dout.(['ne_zz_dot_' trans_names{i}])(sfore_aux+1:efore) = d.(['zz_dot_' trans_names{i}])(sfore_aux+1:efore); %QoQ annualized
% % % %    end    
% % % %     
% % % % 	tmp_ = ((dout.(['ne_zz_dot_' trans_names{i}])+100)/100).^(1/4);                                        
% % % %     dout.(['ne_dot_' trans_names{i}])(sfore_aux:efore) = tmp_(sfore_aux:efore); %QoQ
% % % %     
% % % %     tmp_ = cumprod(dout.(['ne_dot_' trans_names{i}])(sfore_aux:efore));
% % % %     dout.(['ne_zz_' trans_names{i}])(sfore_aux:efore) = dout.(['ne_zz_' trans_names{i}])(sfore_aux-1)*tmp_;
% % % % 
% % % % end
% % % % 
% % % % %% YoY changes
% % % % 
% % % % trans_names = {'pstar_tilde', 'cpi_star_tilde','pstar_other_tilde', 'y_star', 'pstar_energy_tilde','usdeur'
% % % %     };        
% % % % 
% % % % for i = 1:length(trans_names)
% % % %     tmp_ =  dout.(['ne_dot_' trans_names{i}]);
% % % %     tmpser_ = 100*( tmp_ * tmp_{-1} * tmp_{-2} * tmp_{-3} ) - 100;
% % % %     tmpComment_ = strrep(dout.(['ne_zz_dot_' trans_names{i}]).Comment, 'QoQ','YoY');
% % % % 	dout.(['ne_zz_dot_' trans_names{i} '4'])(fix_sfore.all:efore) = tmpser_(fix_sfore.all:efore);
% % % %     dout.(['ne_zz_dot_' trans_names{i} '4']).Comment = tmpComment_;
% % % %     islog.(['ne_zz_dot_' trans_names{i} '4']) = true;
% % % %     
% % % % end
% % % % 
% % % % 
% % % % %%
% % % % if nargout > 1
% % % %     varargout{1} = islog;
% % % % end
% % % % 
% % % % end

