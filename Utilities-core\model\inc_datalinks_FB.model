!transition_variables

%IS
'Measured Real Foreign Demand (100*log)'                mes_Y_STAR

!if ~FB_gdp_decomp
    'Measured Foreign Output Gap (100*log)'           	mes_Y_STAR_GAP
    'Measured Real Foreign GDP Trend (100*log)'        	mes_Y_STAR_TREND
    'Measured Real Foreign GDP Shift Trend (QoQ)'     	mes_DOT_Y_STAR_TREND_SHIFT
!end

!if ~FB_prem_decomp
    'Measured Real Foreign Premium (QoQ)'               mes_PREM_USDEUR
!end

% MP
'Measured 3-M Foreign Rates (percent p.a.)'             mes_I_STAR
'Measured neutral 3-M Foreign Rates (percent p.a.)'     mes_I_STAR_EQ
'Measured Effective 3-M Foreign Rates (percent p.a.)'   mes_I_STAR_EU
'Measured 3-M Foreign (US) Rates (percent p.a.)'        mes_I_STAR_US

% UIP
'Measured Exchange Rate (EUR/USD) (100*log)'            mes_USDEUR

% PC
'Measured Energy Price Level (EUR) (100*log)'           mes_PSTAR_ENERGY_TILDE
'Measured Non-Energy Price Level (EUR) (100*log)'       mes_PSTAR_OTHER_TILDE
'Measured Foreign Price Level (EUR) (100*log)'          mes_PSTAR_TILDE
'Measured Consurmer Price Index (100*log)'              mes_CPI_STAR_TILDE


!transition_equations
% vlevo modelove promenne, ktere jsou nalinkovane na data; modelove
% promenne jsou upraveny tak, aby odpovidaly transformacim "datum"

% IS
100*log(dot_y_star)                 = mes_Y_STAR                    - mes_Y_STAR{-1};

!if ~FB_gdp_decomp
    100*log(y_star_gap)             = mes_Y_STAR_GAP;
    100*log(dot_y_star_trend)       = mes_Y_STAR_TREND              - mes_Y_STAR_TREND{-1};
    400*(dot_y_star_trend_shift-1)  = mes_DOT_Y_STAR_TREND_SHIFT;
!end

!if ~FB_prem_decomp
    400*(prem_usdeur-1)             = mes_PREM_USDEUR;
!end

% MP
400*(i_star-1)                      = mes_I_STAR;
400*(i_star_eq-1)                   = mes_I_STAR_EQ;
400*(i_star_eu-1)                   = mes_I_STAR_EU;
400*(i_star_us-1)                   = mes_I_STAR_US;

% UIP
100*log(usdeur)                     = mes_USDEUR;

% PC
100*log(dot_pstar_energy_tilde)     = mes_PSTAR_ENERGY_TILDE        - mes_PSTAR_ENERGY_TILDE{-1};
100*log(dot_pstar_other_tilde)      = mes_PSTAR_OTHER_TILDE         - mes_PSTAR_OTHER_TILDE{-1};
100*log(dot_pstar_tilde)            = mes_PSTAR_TILDE               - mes_PSTAR_TILDE{-1};
100*log(dot_cpi_star_tilde)         = mes_CPI_STAR_TILDE            - mes_CPI_STAR_TILDE{-1};

% <end>