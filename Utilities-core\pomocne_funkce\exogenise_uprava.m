function this = exogenise_uprava(this,list,dates)
% EXOGENISE  Exogenise some of the model variables on the specified range.
%
% -Syntax
%
%    p = exogenise(m,list,dates)
%
% -Input arguments
%
% * |p| [ plan ] - Simulation plan.
%
% * |list| [ cellstr | char ] - List of the model variables that will be
% exogenised.
%
% * |dates| [ numeric ] - Dates at which the variables will be exogenised.
%
% -Output arguments
%
% * |p| [ plan ] - Simulation plan with new information on exogenised
% variables included.
%
% -Description
%
% -Example

% -The IRIS Toolbox.
% -Copyright (c) 2007-2011 Jaromir Benes.

% Parse required input arguments.
P = inputParser();
P.addRequired('p',@isplan);
P.addRequired('list',@(x) ischar(x) || iscellstr(x));
P.addRequired('dates',@isnumeric);
P.parse(this,list,dates);

% Convert char list to cell of str.
if ischar(list)
   list = regexp(list,'[A-Za-z]\w*','match');
end

if isempty(list)
   return
end

%**************************************************************************

[dates,outofrange] = dateindex(this,dates);
if ~isempty(outofrange)
    % Report invalid dates.
   irisoverhead.error('plan', ...
      'Dates out of simulation plan range: %s.', ...
      dat2charlist(outofrange));
end

nlist = numel(list);
valid = true([1,nlist]);

for i = 1 : nlist
   index = strcmp(this.xlist,list{i});
   if any(index)
      this.xanchors(index,dates) = true;
   else
      index = strcmp(this.nlist,list{i}); 
      if any(index)
         % if flag == 1
            this.nanchorsreal(index,dates) = false;
         % else
         %   this.nanchorsimag(index,dates) = false;
         % end
      else 
         valid(i) = false;
      end   
   end
end

% Report invalid names.
if any(~valid)
   irisoverhead.error('plan', ...
      'Cannot exogenise this name: ''%s''.', ...
      list{~valid});
end

end