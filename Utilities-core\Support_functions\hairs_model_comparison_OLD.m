function  hairs_model_comparison(changedata1,histdata1,fcastdata1,fmodel1,model1,SS1,model1_plan,...
                               filterhistory1,esimulate1,mk_transformations1,...
                               changedata2,histdata2,fcastdata2,fmodel2,model2,SS2,model2_plan,...
                               filterhistory2,esimulate2,mk_transformations2,...
                               db, ...
                               cur_db_name,...
                               endhist_start,endhist_end,hair_length,recompute,...
                               model_names,plotlim,spaghetti_label, ...
                               decomp_plot_vars, sr_opt_base)

%% Error checking 
if (hair_length>8) || (hair_length<1)
    error('hair_length must not exceed 8...');
end

n_per = endhist_end-endhist_start+1;
if n_per<1
   n_per
   error('"<endhist_end-endhist_start>" difference must be strictly positive...'); 
end

%% Computation
hair_max = 8;

% Standard computation hair_length = 8;
hair_length_user = hair_length;
if recompute>0
    hair_length = hair_max;
end

% sr_opt_base.shist        = qq(1996,1); 
sr_opt_base.ehist        = endhist_start;
% sr_opt_base.end_comp     = qq(2025,4);

sr_opt.shist             = sr_opt_base.shist;
sr_opt.end_comp          = sr_opt_base.end_comp;

s1=[];s2=[];

cur_db = dbload(cur_db_name);

if recompute>0
	for i=1:n_per
		disp([sprintf('\n'), ['||| Simulating [' num2str(i) '/' num2str(n_per) ']...']]);
        disp(dat2str(endhist_start+i-1));
            
			sr_opt.ehist        = sr_opt_base.ehist+i-1;
			sr_opt.start_pred   = sr_opt.ehist+1;
			sr_opt.hrng         = sr_opt.shist:sr_opt.ehist;
			sr_opt.comprng      = sr_opt.start_pred:sr_opt.end_comp;

			%% Model #1
			if recompute==2
				h1                  = changedata1(db, sr_opt);
				d1                  = histdata1(h1, sr_opt);   

				dtunes              = dbempty();
				dbfilter            = filterhistory1(fmodel1, d1, dtunes, SS1, sr_opt.hrng);
				f1                  = dbfilter.mean;

				t1                  = fcastdata1(h1, sr_opt);
				dbp                 = dbextend(t1, f1);

				list = get(fmodel1,'enames');
				for j = 1:length(list);
					dbp.(list{j})(sr_opt.comprng) = zeros(size(sr_opt.comprng));
				end

				dbp.dot_s(sr_opt.start_pred) = exp((100*log(h1.czkeur(sr_opt.start_pred))-f1.obs_S(sr_opt.ehist))/100);

				dbp_realimag = dbfun(@(x,y) x+y*1i,dbp,dbp);
				dbp_realimag = dbfun(@(x,y) comment(x,comment(y)),dbp_realimag,dbp);

				% upravit shocky na filtru - jen neocekavane....
				eps_names = dbnames(f1,'nameFilter','^eps_\w*');
				dbf_eps = f1*eps_names;
				dbf_eps = dbfun(@(x) 0+x*1i,dbf_eps);
				dbp_realimag = dbextend(dbp_realimag,dbf_eps);

				[fcast_plan1,fcast_plan_surprise1] = model1_plan(model1,sr_opt);

				dbfcast1 = esimulate1(model1, dbp_realimag, sr_opt.comprng,'plan', fcast_plan1,'surprise_plan', fcast_plan_surprise1);

				dd1 = mk_transformations1(h1,dbfcast1,dbp_realimag,dbp,fcast_plan1,fcast_plan_surprise1,model1,sr_opt,SS1);
            else
                if i==1
                    % Check hair length in advance
                    load(['computed_spaghetti_' spaghetti_label '.mat']);
                    s2=[];

                    names1 = fieldnames(s1);
                    range1 = get(s1.(names1{1}),'range');

                    min_pos = range1(1);
                    max_pos = range1(end)-hair_length;

                    to_print_min = dat2str(min_pos);
                    to_print_max = dat2str(max_pos);
                    flag_ = 0;
                    if (min_pos < endhist_start) || (max_pos > endhist_end)
                        flag_ = 1;
                    end
                    if flag_==1
                        error([sprintf('\n') '||| Maximum possible <endhist_start:endhist_end> range - ' to_print_min{:} ':' to_print_max{:} sprintf('\n') 'Reset the options!...'])
                    end
                end
            end
            
			%% Model #2
			h2                  = changedata2(db, sr_opt);
			d2                  = histdata2(h2, sr_opt);

			dtunes              = dbempty();
			dbfilter            = filterhistory2(fmodel2, d2, dtunes, SS2, sr_opt.hrng);
			f2                  = dbfilter.mean;

			t2                  = fcastdata2(h2, sr_opt);
			dbp                 = dbextend(t2, f2);

			list = get(fmodel2,'enames');
			for j = 1:length(list);
				dbp.(list{j})(sr_opt.comprng) = zeros(size(sr_opt.comprng));
			end

			dbp.dot_s(sr_opt.start_pred) = exp((100*log(h2.czkeur(sr_opt.start_pred))-f2.obs_S(sr_opt.ehist))/100);

			dbp_realimag = dbfun(@(x,y) x+y*1i,dbp,dbp);
			dbp_realimag = dbfun(@(x,y) comment(x,comment(y)),dbp_realimag,dbp);

			% upravit shocky na filtru - jen neocekavane....
			eps_names = dbnames(f2,'nameFilter','^eps_\w*');
			dbf_eps = f2*eps_names;
			dbf_eps = dbfun(@(x) 0+x*1i,dbf_eps);
			dbp_realimag = dbextend(dbp_realimag,dbf_eps);

			[fcast_plan2,fcast_plan_surprise2] = model2_plan(model2,sr_opt);

			dbfcast2 = esimulate2(model2, dbp_realimag, sr_opt.comprng,'plan', fcast_plan2,'surprise_plan', fcast_plan_surprise2);

			dd2 = mk_transformations2(h2,dbfcast2,dbp_realimag,dbp,fcast_plan2,fcast_plan_surprise2,model2,sr_opt,SS2);

			%% Expand the results with current simulation
			if recompute==2
				s_pom = dbclip(dd1,sr_opt.ehist:sr_opt.ehist+hair_length);
				if ~isempty(s1)
					s1 = dbfun (@(x,y) [x y], s1, s_pom);
				else
					s1 = s_pom;
				end    
			end
% 			keyboard;
			s_pom = dbclip(dd2,sr_opt.ehist:sr_opt.ehist+hair_length);
			if ~isempty(s2)
				s2 = dbfun (@(x,y) [x y], s2, s_pom);        
			else
				s2 = s_pom;
            end    
 
    end
    
    %% Post-processing
% 	if recompute==1
% 		s2_pom = s2;
% 		load(['computed_spaghetti_' spaghetti_label '.mat']);
% 		disp([sprintf('\n'), '||| Model#1 previous results loaded...']);
% 		s2 = s2_pom;
%     end 
	
	% Saving .mat
	disp([sprintf('\n'), '||| Saving spaghetti...']);
	save(['computed_spaghetti_' spaghetti_label '.mat'],'s1','s2');
    disp([sprintf('\n'), '||| Saving DONE...']);
    
    names1 = fieldnames(s1);
    names2 = fieldnames(s2);
    
    range1 = get(s1.(names1{1}),'range');
    range2 = get(s2.(names2{1}),'range');
   
else
	%% Load previous results
	load(['computed_spaghetti_' spaghetti_label '.mat']);
    disp([sprintf('\n'), '||| Previous results loaded...']);
%     keyboard;
    names1 = fieldnames(s1);
    names2 = fieldnames(s2); 
    
    range1 = get(s1.(names1{1}),'range');
    range2 = get(s2.(names2{1}),'range');
    
    min_pos = max([range1(1);range2(1)]);
    max_pos = min([range1(end)-hair_length;range2(end)-hair_length]);
    
    to_print_min = dat2str(min_pos);
    to_print_max = dat2str(max_pos);
    
    disp([sprintf('\n') 'Maximum possible <endhist_start:endhist_end> range - ' to_print_min{:} ':' to_print_max{:} sprintf('\n') 'hit F5 to continue...'])
    keyboard;
end

%% Reporting the differences between predictions
	
disp([sprintf('\n'), '||| Computing ex post transformations...']);

% Range intersection
start_now = max([range1(1); ...
                 range2(1); ...
                 endhist_start]);
end_now   = min([range1(end)-hair_length; ...
                 range2(end)-hair_length; ...
                 endhist_end]);

% Debugging - DO NOT ERASE!
% dat2str(range1(1))
% dat2str(range2(1))
% dat2str(range1(end)-hair_length)
% dat2str(range2(end)-hair_length)
% dat2str(start_now)
% dat2str(end_now)

%% Determine original hair_lengths
s1_temp = s1.(names1{1})(:);
s2_temp = s2.(names2{1})(:);

s1_temp = s1_temp(:,1);
s2_temp = s2_temp(:,1);

hair_length_s1 = length(s1_temp) - sum(isnan(s1_temp)) - 1; 
hair_length_s2 = length(s2_temp) - sum(isnan(s2_temp)) - 1;

%% Model vs. data differences 
s1_diffs_pom = dbfun(@(x,y) y-x,cur_db,s1);
s2_diffs_pom = dbfun(@(x,y) y-x,cur_db,s2);
if size(s1.(names1{1}),2)>1
    s1_diffs = dbfun(@(x) tseries(range1(1):range1(end)-hair_length_s1,spdiags(x(:),0:-1:-hair_length_s1)),s1_diffs_pom);
else
    s1_diffs = dbfun(@(x) tseries(range1(1):range1(end)-hair_length_s1,x(:)'),s1_diffs_pom);
end
if size(s2.(names2{1}),2)>1
    s2_diffs = dbfun(@(x) tseries(range2(1):range2(end)-hair_length_s2,spdiags(x(:),0:-1:-hair_length_s2)),s2_diffs_pom);
else
    s2_diffs = dbfun(@(x) tseries(range2(1):range2(end)-hair_length_s2,x(:)'),s2_diffs_pom);
end

s1_diffs = dbclip(s1_diffs,start_now:end_now);
s2_diffs = dbclip(s2_diffs,start_now:end_now);

%% Clip data to plot according to user supplied range/hair_length
% Model #1 
clip_min =  strfind(range1,start_now);
clip_max =  strfind(range1,end_now);
s1_pom = dbfun(@(x) x(:),s1);
s1_pom = dbfun(@(x) x(clip_min:clip_max+hair_length_s1,clip_min:clip_max),s1_pom);
colnum = size(s1_pom.(names1{1}),2);
s1_pom = dbfun(@(x) x(1:colnum+hair_length_user,:),s1_pom);
to_be_truncated = rot90(isnan(s1_pom.(names1{1})),2);
for ii = 1:length(names1)
    s1_pom.(names1{ii})(to_be_truncated)=NaN;    
end
s1 = dbfun(@(x) tseries(start_now:end_now+hair_length_user,x),s1_pom);

% Model #2
clip_min =  strfind(range2,start_now); 
clip_max =  strfind(range2,end_now);
s2_pom = dbfun(@(x) x(:),s2);
s2_pom = dbfun(@(x) x(clip_min:clip_max+hair_length_s2,clip_min:clip_max),s2_pom);
colnum = size(s1_pom.(names2{1}),2);
s2_pom = dbfun(@(x) x(1:colnum+hair_length_user,:),s2_pom);
to_be_truncated = rot90(isnan(s2_pom.(names2{1})),2);
for ii = 1:length(names2)
    s2_pom.(names2{ii})(to_be_truncated)=NaN;    
end
s2 = dbfun(@(x) tseries(start_now:end_now+hair_length_user,x),s2_pom);

%% Acknowledge the user supplied hair_length
s1_diffs = dbfun(@(x) tseries(start_now:end_now,x(:,1:hair_length_user+1)),s1_diffs);
s2_diffs = dbfun(@(x) tseries(start_now:end_now,x(:,1:hair_length_user+1)),s2_diffs);

%% Differences between models
s1s2_periods_compare     = dbfun(@(x,y) ...
							tseries(start_now:end_now, ...
							(abs(y(:))-abs(x(:))) ), ...
							s1_diffs,s2_diffs);

hair_length = hair_length_user;

% Linearni prevazeni vsech odchylek vychazejicich z daneho roku 
weights = [1 1:-1/hair_length:1/hair_length];

% Procentni hodnota rozdilu mezi s2 a s1 (baseline)
s1_weight_diffs = dbfun(@(x) tseries(start_now:end_now,(abs(x(:))*weights')./sum(weights)),s1_diffs);
s2_weight_diffs = dbfun(@(x) tseries(start_now:end_now,(abs(x(:))*weights')./sum(weights)),s2_diffs);

r1 = dbfun(@(x,y) tseries(start_now:end_now, ...
		max(abs(y(:)),1e-12)./max(abs(x(:)),1e-12)), ...
		s1_weight_diffs,s2_weight_diffs);

% -> absolutne...
s1s2_compare = dbfun(@(x) tseries(start_now:end_now,(x(:)*weights')./sum(weights)),s1s2_periods_compare);
% -> v procentech...
s1s2_compare_pct = dbfun(@(x) tseries(start_now:end_now,(x(:)-1)*100),r1);

% keyboard;

%% Plot the comparison

disp([sprintf('\n'), '||| Plotting...']);


n_per = end_now-start_now+1;
cur_db = dbclip(cur_db,plotlim{1}:plotlim{2});

for k = 1:length(decomp_plot_vars)


    var_exists1 = 0;
    var_exists2 = 0;

    figname = figure;
    p1 = subplot(5,1,[1:3]);
    hold on;
    
    try
        h1 = plot(s1.(decomp_plot_vars{k}),'color','red','LineWidth',2);
        var_exists1 = 1;
    end
    try
        h2 = plot(s2.(decomp_plot_vars{k}),'color',[0 0.5 0],'LineWidth',2);
        var_exists2 = 1;
    end
    plot(cur_db.(decomp_plot_vars{k}),'LineWidth',4);
    hold off;

    % Y limits
    axis('tight');
    ylims = get(gca,'Ylim');
    ylim_range = max(ylims)-min(ylims);
    if ylim_range==0,ylim_range = 0.1;end
    set(gca,'Ylim',[min(ylims)-0.1*ylim_range max(ylims)+0.01*ylim_range]);

    % Legend
    lines_handles=get(p1(end),'Children');
    if var_exists1 && var_exists2         %2*(n_per+1)+1    +2
        leg_handle = legend(lines_handles([1 2*(n_per)+1 n_per+1]),'Data',model_names{1},model_names{2});
        set(leg_handle,'Location','South','Orientation','horizontal');
    end
    legend('boxoff');

    % Rest
    title( get(cur_db.(decomp_plot_vars{k}),'comment'));
    box on;
    xlims = get(gca,'Xlim');
    xticks = get(gca,'Xtick');

    if var_exists1 && var_exists2
        subplot(5,1,4);
            temp_ = s1s2_compare.(decomp_plot_vars{k});
            range_ = get(temp_,'range');

            temp_1 = max(temp_(:),0);
            temp_2 = min(temp_(:),0);

            temp_1 = tseries(range_,temp_1);
            temp_2 = tseries(range_,temp_2);

            temp_1(plotlim{1}-1)=0;
            temp_2(plotlim{1}-1)=0;

            temp_1(plotlim{2}+1)=0;
            temp_2(plotlim{2}+1)=0;

        hold on;
        conbar(plotlim{1}:plotlim{2},[temp_1 0],'colorMap',[0 0.5 0]);
        conbar(plotlim{1}:plotlim{2},[temp_2 0],'colorMap',[1 0 0]);
        hold off;
        set(gca,'xticklabel',[]);
        Ylims = get(gca,'Ylim');
        set(gca,'Ylim',[-max(abs(Ylims)) max(abs(Ylims))]);
        title(['Absolute error difference - ' model_names{2} ' vs. ' model_names{1}]);
        set(gca,'Xlim',xlims);
        set(gca,'Xtick',xticks);
        box on;

        subplot(5,1,5);
            temp_ = s1s2_compare_pct.(decomp_plot_vars{k});
            range_ = get(temp_,'range');

            temp_1 = max(temp_(:),0);
            temp_2 = min(temp_(:),0);

            temp_1 = tseries(range_,temp_1);
            temp_2 = tseries(range_,temp_2);

            temp_1(plotlim{1}-1)=0;
            temp_2(plotlim{1}-1)=0;

            temp_1(plotlim{2}+1)=0;
            temp_2(plotlim{2}+1)=0;

        hold on;
        conbar(plotlim{1}:plotlim{2},[temp_1 0],'colorMap',[0 0.5 0]);
        conbar(plotlim{1}:plotlim{2},[temp_2 0],'colorMap',[1 0 0]);
        hold off;
        set(gca,'xticklabel',[]);
        Ylims = get(gca,'Ylim');
        if max(abs(Ylims))<1,Ylims=1;end
        set(gca,'Ylim',[-50 50]);
        title(['Relative error difference [50=50%] - ' model_names{2} ' vs. ' model_names{1}]);
        set(gca,'Xlim',xlims);
        set(gca,'Xtick',xticks);
        box on;
    end

    % report figures as .ps
    set(figname,'PaperOrientation','portrait');    
    set(figname,'PaperUnits','normalized');
    set(figname,'PaperPosition', [0 0 1 1]);

    if k == 1
         print(figname,            '-dpsc2', 'Spaghetti_Report.ps');
    else
         print(figname, '-append', '-dpsc2', 'Spaghetti_Report.ps');
    end

end

% create PDF
model_names = strrep(model_names,'\_','_');
disp([sprintf('\n'), '||| Generating PDF...']);
filename = ['Spaghetti_Report_[' spaghetti_label ']_' model_names{2} '-vs-' model_names{1} '.pdf'];

success_ = 0;
while success_==0
    try
        ps2pdf('psfile', 'Spaghetti_Report.ps', 'pdffile', ...
          filename, ...
           'gspapersize', 'a4', 'deletepsfile', 1);
        disp([sprintf('\n') 'Report ' filename ' generated...' sprintf('\n')]);
        success_ = 1;
    catch
        disp([sprintf('\n') '<strong>Cannot open report file' sprintf('\n') filename sprintf('\n') 'for writing, close & F5...</strong>' sprintf('\n')]);
        keyboard;
    end
end
keyboard;
end

% 
% hs = getappdata(gcf,'PrintHeaderHeaderSpec');
% if isempty(hs)
% hs = struct('dateformat','none',...
% 'string','',...
% 'fontname','Times',...
% 'fontsize',9,... % in points 12
% 'fontweight','normal',...
% 'fontangle','normal',...
% 'margin',20); % in points 72
% end
% 
% currentPage = 1;
% totalPages = 10;
% hs.string = ['Header: Page ' num2str(currentPage) ' of ' num2str(totalPages)];
% setappdata(gcf,'PrintHeaderHeaderSpec',hs);

