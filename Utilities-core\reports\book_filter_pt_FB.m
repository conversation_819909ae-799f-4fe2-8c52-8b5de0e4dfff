function book_filter_detail(d, m, settings, heading, basename)

disp(['Creating ' heading]);
rng = settings.ehist-12:settings.ehist;
rngg = settings.shist+8:settings.ehist; % Change range
sdate = settings.start_pred;

p = get(m,'parameters');
eList = dbnames(d,'nameFilter','^eps_\w*');
omegaList = dbnames(d,'nameFilter','^omega_\w*');

%% list from tables - Please keep it consistent with tables
list = {'zz_dot_p_star_tilde', 'zz_n_star', 'zz_i_star', 'zz_r_star', ...
        'ne_zz_dot:p_star_tilde', 'ne_zz_n_star', 'ne_zz_i_star', 'ne_zz_r_star',...
        };

dbout = dbbatch(d, '$0', 'resize(d.$0, rng);', 'namelist', list);
dbout = dbout*list;


%--open the report--%

x = report.new(heading);

%**************************************************************************

% %--Graph--%
sty = struct(); 
sty.line.lineWidth = 2;
sty.line.lineStyle = '-';
sty.line.Color = {[0 0 1]};
sty.highlight.faceColor = [0.8,0.8,0.8];
sty.legend.location = 'SouthOutside';
sty.legend.Orientation = 'Horizontal';
sty.axes.ylim=[ ...
                    '!! ylim = get(H,''ylim'');', ...
                    'k = 0.05*(ylim(2)-ylim(1));', ...
                    'SET = [ylim(1)-k,ylim(2)+k];'];


% post = [ ...
%     'pos = get(H,''position'');', ...
%     'k = 0.0962;', ...
%     'set(H,''position'',[pos(1),pos(2)-k,pos(3),pos(4)+k]);'];

post = [ ...
    'leg = findobj(gcf,''Location'',''SouthOutside'');'...
    'set(leg,''location'',''none'');',...
    'posleg = get(leg,''position'');', ...
    'pos = get(H,''position'');', ...
    'k = 0.0073;', ...
    'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
    'k = 0.062;', ...
    'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];

sty.line.Color = {[1 0 0],[0 0.5 0],[0 0 1],[0 1 1],[1 1 0],[1 0 1]};

%**************************************************************************
%% 1

x.table('Forecast Summary','range',rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);

x.subheading('');
x.subheading('Inflation');
x.series(char(get(d.ne_zz_dot_pstar_tilde4,'comment')), d.ne_zz_dot_pstar_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.ne_zz_dot_pstar_tilde,'comment')), d.ne_zz_dot_pstar_tilde,'format','%.1f','units','%pa');
x.series('Inflation Target', tseries(settings.shist:settings.ehist,(m.dot_pstar_tilde^4-1)*100),'format','%.1f','units','%pa');

% x.subheading('');
% x.series(char(get(d.zz_dot_p_ener_ex_oil_tilde4,'comment')), d.zz_dot_p_ener_ex_oil_tilde4,'format','%.1f','units','%pa');
% x.series(char(get(d.zz_dot_p_ener_ex_oil_tilde,'comment')), d.zz_dot_p_ener_ex_oil_tilde,'format','%.1f','units','%pa');
% x.series('Steady State', tseries(settings.shist:settings.ehist,(m.dot_p_ener_ex_oil_tilde^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_dot_pstar_RP_tilde4,'comment')), d.zz_dot_pstar_RP_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.zz_dot_pstar_RP_tilde,'comment')), d.zz_dot_pstar_RP_tilde,'format','%.1f','units','%pa');
x.series('Steady State', tseries(settings.shist:settings.ehist,(m.dot_pstar_RP_tilde^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.ne_zz_dot_pstar_other_tilde4,'comment')), d.ne_zz_dot_pstar_other_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.ne_zz_dot_pstar_other_tilde,'comment')), d.ne_zz_dot_pstar_other_tilde,'format','%.1f','units','%pa');
x.series('Steady State', tseries(settings.shist:settings.ehist,(m.dot_pstar_other_tilde^4-1)*100),'format','%.1f','units','%pa');

% x.subheading('');
% x.series(char(get(d.ne_zz_dot_p_BrentUSD_tilde4,'comment')), d.ne_zz_dot_p_BrentUSD_tilde4,'format','%.1f','units','%pa');
% x.series(char(get(d.ne_zz_dot_p_BrentUSD_tilde,'comment')), d.ne_zz_dot_p_BrentUSD_tilde,'format','%.1f','units','%pa');
% x.series('Steady State', tseries(settings.shist:settings.ehist,(m.dot_p_BrentUSD_tilde^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.subheading('Nominal Interest Rate');
x.series(char(get(d.ne_zz_i_star,'comment')), d.ne_zz_i_star,'format','%.1f','units','%pa');

x.subheading('');
x.subheading('Nominal Exchange Rate');
x.series(char(get(d.ne_zz_usdeur,'comment')), d.ne_zz_usdeur,'format','%.2f');
x.series(char(get(d.ne_zz_dot_usdeur4,'comment')), d.ne_zz_dot_usdeur4,'format','%.1f','units','%pa');
x.series(char(get(d.ne_zz_dot_usdeur,'comment')), d.ne_zz_dot_usdeur,'format','%.1f','units','%pa');

x.pagebreak();

x.table('Forecast Summary','range',rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);

x.subheading('');
x.subheading('Real Sector Variables');
x.series(char(get(d.zz_dot_y_star_trend4,'comment')), d.zz_dot_y_star_trend4,'format','%.1f','units','%pa');
x.series(char(get(d.zz_dot_y_star_trend,'comment')), d.zz_dot_y_star_trend,'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_y_star_gap,'comment')), d.zz_y_star_gap,'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.ne_zz_dot_y_star,'comment')), d.ne_zz_dot_y_star,'format','%.1f','units','%pa');
x.series(char(get(d.ne_zz_dot_y_star,'comment')), d.ne_zz_dot_y_star,'format','%.1f','units','%pa');

x.pagebreak();


%% Contributions

stybar=struct();
stybar.figure.colormap=[0 0 0.95; 1 0 0; 1 1 0; 0 1 0; 1 0 1; 0 0.5 0.5; 0.9 0.7 0.1; 0 1 1];
stybar.line.lineWidth = {3,2};
stybar.line.Color = {[0.8 0.8 0.8],[0 0 0]};
stybar.legend.location = 'SouthOutside';
stybar.legend.Orientation = 'Horizontal';
stybar.highlight.faceColor = [0.8,0.8,0.8];
stybar.axes.xgrid = 'off';

%  stybar.axes.YLim=[-5 5];
%  stybar.axes.YTick=[-5;-4;-3;-2;-1;0;1;2;3;4;5];

post = [ ...
    'leg = findobj(gcf,''Location'',''SouthOutside'');'...
    'set(leg,''location'',''none'');',...
    'posleg = get(leg,''position'');', ...
    'pos = get(H,''position'');', ...
    'k = 0.014;', ...
    'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
    'k = 0.057;', ...
    'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];

x.pagebreak();   


% dot_p_star_tilde decomposition - model view
x.figure('Contributions - Model','range', rngg, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rngg(1):4:rngg(end),'ordering','descent');   
 x.graph(get(d.zz_dot_pstar_tilde, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_tilde_contrib_nonener d.zz_dot_pstar_tilde_contrib_energy d.zz_dot_pstar_tilde_contrib_resid], ...
	   'plotfunc',@conbar,'legend',{'non-energy','oil','energy other','residuals'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model p star',d.zz_dot_pstar_tilde);
   x.series('Sum',d.zz_dot_pstar_tilde_contrib_energy+d.zz_dot_pstar_tilde_contrib_nonener+d.zz_dot_pstar_tilde_contrib_resid);   
 x.graph(get(d.zz_dot_pstar_tilde4, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_tilde_contrib_nonener4 d.zz_dot_pstar_tilde_contrib_energy4 d.zz_dot_pstar_tilde_contrib_resid4], ...
	   'plotfunc',@conbar,'legend',{'non-energy','oil','energy other','residuals'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model p star', d.zz_dot_pstar_tilde4); %log((d.dot_p_star_tilde-d.eps_Pstar_tilde)*(d.dot_p_star_tilde{-1}-d.eps_Pstar_tilde{-1})* ...
             %(d.dot_p_star_tilde{-2}-d.eps_Pstar_tilde{-2})*(d.dot_p_star_tilde{-3}-d.eps_Pstar_tilde{-3}))*100);
   x.series('Sum',d.zz_dot_pstar_tilde_contrib_energy4+d.zz_dot_pstar_tilde_contrib_nonener4+d.zz_dot_pstar_tilde_contrib_resid4);
        
x.pagebreak();

% dot_p_other_tilde decomposition
x.figure('Contributions - Model','range', rngg, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rngg(1):4:rngg(end),'ordering','descent');   
 x.graph(get(d.zz_dot_pstar_other_tilde, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_other_tilde_contrib_ogap d.zz_dot_pstar_other_tilde_contrib_lagdev d.zz_dot_pstar_other_tilde_contrib_expdev d.zz_dot_pstar_other_tilde_contrib_usdeur d.zz_dot_pstar_other_tilde_contrib_resid], ...                         
	   'plotfunc',@conbar,'legend',{'O. gap','lag dev','exp dev','usdeur dev','residuals'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model p other',d.zz_dot_pstar_other_tilde);
   x.series('Sum',d.zz_dot_pstar_other_tilde_contrib_ogap+d.zz_dot_pstar_other_tilde_contrib_lagdev+d.zz_dot_pstar_other_tilde_contrib_expdev+d.zz_dot_pstar_other_tilde_contrib_usdeur+d.zz_dot_pstar_other_tilde_contrib_resid);   
 x.graph(get(d.zz_dot_pstar_other_tilde4, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_other_tilde_contrib_ogap4 d.zz_dot_pstar_other_tilde_contrib_lagdev4 d.zz_dot_pstar_other_tilde_contrib_expdev4 d.zz_dot_pstar_other_tilde_contrib_usdeur4 d.zz_dot_pstar_other_tilde_contrib_resid4], ...
	   'plotfunc',@conbar,'legend',{'O. gap','lag dev','exp dev','usdeur dev','residuals'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model p other',d.zz_dot_pstar_other_tilde4);
   x.series('Sum',d.zz_dot_pstar_other_tilde_contrib_ogap4+d.zz_dot_pstar_other_tilde_contrib_lagdev4+d.zz_dot_pstar_other_tilde_contrib_expdev4+d.zz_dot_pstar_other_tilde_contrib_usdeur4+d.zz_dot_pstar_other_tilde_contrib_resid4);

x.pagebreak();

% y_star_gap decomposition
x.figure('Contributions - Model','range', rngg, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rngg(1):4:rngg(end),'ordering','descent');   
 x.graph(get(d.zz_y_star_gap, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_y_star_gap_contrib_lag d.zz_y_star_gap_contrib_rmci d.zz_y_star_gap_contrib_usdeur d.zz_y_star_gap_contrib_resid], ...                                   
	   'plotfunc',@conbar,'legend',{'lag','RMCI','Oil cost','usdeur dev','residuals'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model y star gap',d.zz_y_star_gap);
   x.series('Sum',d.zz_y_star_gap_contrib_lag+d.zz_y_star_gap_contrib_rmci+d.zz_y_star_gap_contrib_usdeur+d.zz_y_star_gap_contrib_resid);         
% i_star decomposition 
 x.graph(get(d.zz_i_star, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_i_star_contrib_ss d.zz_i_star_contrib_smoothing  d.zz_i_star_contrib_ogap d.zz_i_star_contrib_inflexpdev  d.zz_i_star_contrib_resid], ...               
	   'plotfunc',@conbar,'legend',{'SS','smoothing','O. gap','infl exp dev','residuals'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model i star shadow rate',d.zz_i_star_eu); % change bcs of shadow rate
   x.series('Sum',d.zz_i_star_eu);%d.zz_i_star_contrib_ogap+d.zz_i_star_contrib_inflexpdev+d.zz_i_star_contrib_smoothing+d.zz_i_star_contrib_ss+d.zz_i_star_contrib_resid);   

%**************************************************************************

x.publish([basename '.pdf'],'paperSize','a4paper','display',false);

