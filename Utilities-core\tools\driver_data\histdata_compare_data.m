function [dbm db] = histdata_compare_data(db, sr_opt)

%*************************************************************************
% HISTDATA
%    - creates 'dbm'= DataBaseModel
%    - includes OBS variables into dbm
%    - do NOT change data here if you want them officially reported!!!
%*************************************************************************

%% Expert judgements - won't appear in reports

% disp('histdata: tuning data in model before transformations');
% db.wpft = db.wpft_model;

%% Standard data transformations

%--Convert Interest Rates--%
dbm.euribor3m   = (1+db.net4_euribor3m/100)^(1/4);
% dbm.eonia3m     = (1+db.net4_eonia3m/100)^(1/4);
% dbm.euribor1y   = (1+db.net4_euribor1y/100)^(1/4);
dbm.pribor3m    = (1+db.net4_pribor3m/100)^(1/4);
% dbm.pribor1y    = (1+db.net4_pribor1y/100)^(1/4);
% dbm.czeonia     = (1+db.net4_czeonia/100)^(1/4);
% dbm.repo2w      = (1+db.net4_repo2w/100)^(1/4);

% dbm.brent = db.brent;

% keyboard;
%--Inflation--%
db = inflation(db, sr_opt.ehist);

dbm.target  = exp(db.cpi_target4/100);
dbm.p       = db.p;
dbm.p = comment(dbm.p,'Net Price Index');
dbm.pREG    = db.pREG;
dbm.pREG = comment(dbm.pREG,'Regulated Price Index');
dbm.cpi     = db.cpi;
dbm.cpi = comment(dbm.cpi,'Consumer Price Index');

dbm.wdot_reg = db.wdot_reg;
dbm.wdot_net = db.wdot_net;

%--dirty series HACK--%!!!!!!!!
dbm.p       = hpf(dbm.p, get(dbm.p ,'range'),'lambda',1);
dbm.pREG    = hpf(dbm.pREG, get(dbm.pREG ,'range'),'lambda',1);
dbm.cpi_aux = hpf(dbm.cpi, get(dbm.cpi ,'range'),'lambda',1);

aux.dot_p    = dbm.p/dbm.p{-1};
aux.dot_pREG = dbm.pREG/dbm.pREG{-1};
aux.dot_cpi  = (dbm.wdot_reg*aux.dot_pREG + dbm.wdot_net*aux.dot_p) ...
                    / (dbm.wdot_reg + dbm.wdot_net);

dbm.cpi = tseries;
dbm.cpi(startdate(aux.dot_cpi)-1)=100;
tmp_    = cumprod(aux.dot_cpi(get(aux.dot_cpi, 'range')));
dbm.cpi(get(aux.dot_cpi, 'range')) = dbm.cpi(startdate(aux.dot_cpi)-1) * tmp_;
dbm.cpi = dbm.cpi * dbm.cpi_aux(sr_opt.ehist) / dbm.cpi(sr_opt.ehist);

% display('histdata: net inflation hpf hack!');
% display('histdata: reg inflation hpf hack!');


%--National Accounts--%

db.nom_j     = db.nom_gcf;
db.j         = db.gcf;
dbm.jfix      = db.gfcf;
dbm.jinv = db.j - dbm.jfix;

% keyboard;

gdp_list       = {'gdp','c','j','g','x','n'};

dbm.nom_gdp = db.nom_gdp;
dbm.gdp = db.gdp;

dbm.nom_c = db.nom_c;
dbm.c = db.c;

dbm.nom_j = db.nom_j;
dbm.j = db.j;

dbm.nom_g = db.nom_g;
dbm.g = db.g;

% dbm.nom_g = db.nom_g_tun;
% dbm.g = db.g_tun;

dbm.nom_x = db.nom_x;
dbm.x = db.x;

dbm.nom_n = db.nom_n;
dbm.n = db.n;

%--Gdp Deflators--%
% TODO: link to TRAMO-SEATS Matlab Interface by Juan Bogalo!
dbm            = dbbatch(dbm,'pupper($0)','dbm.nom_$0/dbm.$0','nameList',gdp_list);

%--Taxes in pC--%
dbm.dot_pC = dbm.pC/dbm.pC{-1};

dbm.dot_pC(sr_opt.shist:sr_opt.ehist) = 1+((100*dbm.dot_pC(sr_opt.shist:sr_opt.ehist)-100) - db.tax_pie_pC(sr_opt.shist:sr_opt.ehist))/100;
tmp_            = dbm.dot_pC(get(dbm.dot_pC, 'first'):sr_opt.ehist);
tmp_            = tmp_(end:-1:1);
tmp_            = cumprod(tmp_); 
tmp_            = tmp_(end:-1:1);
dbm.pC(startdate(dbm.dot_pC)-1:sr_opt.ehist-1) = dbm.pC(sr_opt.ehist)./tmp_;

% dbm.c = dbm.nom_c/dbm.pC;
disp('||| Realna spotreba neni ocistena o dane, jen samotny deflator !!!');


%% seasf
% disp('SEASF!')
% db.j_u         = db.gcf_u;
% db.nom_j_u         = db.nom_gcf_u;
% gdp_list       = {'gdp','c','j','g','x','n','gfcf'};
% gdp_list       = {'c'};
% dbm            = dbbatch(dbm,'$0','seasf(db.$0_u, sr_opt.hrng,''log'', true)','namefilter',gdp_list);
% dbm            = dbbatch(dbm,'nom_$0','seasf(db.nom_$0_u, sr_opt.hrng,''log'',true)','namefilter',gdp_list);
% dbm            = dbbatch(dbm,'p@upper($0)','dbm.nom_$0/dbm.$0','namefilter',gdp_list);

% -- hpf crack na OBS--%
list_ = {'pC', 'pJ', 'pX', 'pN', 'pG',...
         'c','j','jfix','x', 'n', 'nom_g','g'};
for i = 1:length(list_)
    dbm.([list_{i} '_noHP']) = dbm.(list_{i});
end
for i = 1:length(list_)
    tmp = dbm.(list_{i});
    tmp = 100*log(tmp);
    tmp = hpf(tmp, inf, 'lambda', 0.5);
    tmp = exp(tmp/100);
    dbm.(list_{i}) = tmp;
end


list_ = {'jinv'};
for i = 1:length(list_)
    dbm.([list_{i} '_noHP']) = dbm.(list_{i});
end
for i = 1:length(list_)
    tmp = dbm.(list_{i})+1000;
    tmp = 100*log(tmp);
    tmp = hpf(tmp, inf, 'lambda', 0.5);
    tmp = exp(tmp/100);
    dbm.(list_{i}) = tmp-1000;
end
% display('histdata: GDP hpf hack!');

 
%--Labour Market--%
dbm.lab        = x12(db.lab_u, startdate(db.lab_u):sr_opt.ehist,'log',true); %seasf(db.lab_u, sr_opt.hrng,'log',true);
% dbm.wpft       = seasf(resize(db.wpft_u, sr_opt.hrng), sr_opt.hrng, 'log',true);
dbm.wpft       = db.wpft;

%--Exchange rates-%
dbm.czkeur     = db.czkeur;
%dbm.czkusd     = db.czkusd;
dbm.usdeur     = db.usdeur;


%--Rest of the World--%

dbm.n_star       = exp(4*log(db.gdp_emu));
dbm.p_star_tilde = db.ppi_emu;
dbm.p_star       = db.czkeur * db.ppi_emu;

dbm.cpi_star_tilde = db.cpi_emu;
dbm.cpi_star       = db.czkeur * db.cpi_emu;


%% --OBSERVED MODEL VARIABLES FOR FILTERING-- %

% list_ = {'pC', 'pJ', 'pX', 'pN', 'pG',...
%          'c','j','jfix','jinv','x', 'n', 'nom_g','g'};
dbm.obs_PC_noHP           = 100*log(dbm.pC_noHP);
dbm.obs_PJ_noHP            = 100*log(dbm.pJ_noHP);
dbm.obs_PX_noHP            = 100*log(dbm.pX_noHP);
dbm.obs_PN_noHP            = 100*log(dbm.pN_noHP);
dbm.obs_PG_noHP            = 100*log(dbm.pG_noHP);
dbm.obs_C_noHP             = 100*log(dbm.c_noHP);
dbm.obs_J_noHP             = 100*log(dbm.j_noHP);
dbm.obs_Jfix_noHP          = 100*log(dbm.jfix_noHP);
dbm.obs_Jinv_noHP          =  dbm.jinv_noHP;
dbm.obs_X_noHP             = 100*log(dbm.x_noHP);
dbm.obs_N_noHP            = 100*log(dbm.n_noHP);
dbm.obs_G_noHP             = 100*log(dbm.g_noHP);
     
dbm.obs_PC            = 100*log(dbm.pC);
dbm.obs_PJ            = 100*log(dbm.pJ);
dbm.obs_PX            = 100*log(dbm.pX);
dbm.obs_PN            = 100*log(dbm.pN);
dbm.obs_PG            = 100*log(dbm.pG);
dbm.obs_C             = 100*log(dbm.c);
dbm.obs_J             = 100*log(dbm.j);
dbm.obs_Jfix          = 100*log(dbm.jfix);
dbm.obs_Jinv          =  dbm.jinv;
dbm.obs_X             = 100*log(dbm.x);
dbm.obs_N             = 100*log(dbm.n);
dbm.obs_G             = 100*log(dbm.g);

dbm.obs_P             = 100*log(dbm.p);
dbm.obs_W             = 100*log(dbm.wpft);
dbm.obs_L             = 100*log(dbm.lab);
dbm.obs_S             = 100*log(dbm.czkeur);
dbm.obs_N_STAR        = 100*log(dbm.n_star);
dbm.obs_P_STAR_TILDE  = 100*log(dbm.p_star_tilde);
dbm.obs_I_STAR        = 400*(dbm.euribor3m-1);% dbm.euribor3m je QoQ scalovane na jednicku
dbm.obs_I             = 400*(dbm.pribor3m -1);% -"-
dbm.obs_PREG          = 100*log(dbm.pREG);
dbm.obs_CPI           = 100*log(dbm.cpi);
dbm.obs_GP            = 100*log(dbm.nom_g);



%% Expert judgements - won't appear in reports

% disp('histdata: tuning data in model after transformations');





