%**************************************************************************
%% DRIVER_FCAST
%**************************************************************************

close all
clear all
disp([sprintf('\n'), 'DRIVER_FCAST']);

%% Set options

do_forecast_book     = 0;      % do forecast book options
                               % 0 - do not create forecast book
                               % 1 - do simple forecast book 
                               % 2 - do forecast book with filter comparison 
do_compare_book      = 0;

% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('GLOBALSETTINGS.mat');

disp(['FB_ID: ', FB_ID]);

% load settings
settings = load_settings(FB_ID);

%**************************************************************************
%% Read model and parameters

SS.filtering = false;

SS  = setparam_FB(settings, 'filtering', SS.filtering);
SS.FB_only      = false;
m = readmodel('../../Utilities-core/model/inc_g3_FB.model',SS);

% load expectations scheme
try
    expect_scheme = expDesign(m,settings.expectations_scheme);
catch
    msgbox('Expectation scheme not defined!!!','Expectations scheme error','error');
    disp('Expectation scheme not defined.');
end

%% Load database
h = dbload(settings.histcore_name_adj);
h = changedata_FB(h,SS);

f = load(settings.filterdata_fb_name);		% filter results
dbf = dbclip(f, settings.shist:settings.ehist);
dbm = fcastdata_FB(h, f, settings, SS);

if strcmp(settings.adhoc_data_fb,'')
    db_ejud = dbempty;
else
    % expert judgement
    db_ejud = dbload(settings.adhoc_data_fb);
    db_ejud = dbclip(db_ejud, settings.comprng);
	d_pom      = dbfun(@(x,y) comment(x,comment(y)),db_ejud,f);
	db_ejud = dbextend(db_ejud,d_pom);
end

% merge databases
d = dbextend(dbm, dbf);

% remove structural shocks over forecasting horizon
list = get(m,'eList');
for i = 1:length(list);
    d.(list{i})(settings.comprng) = zeros(size(settings.comprng));
end

d = dbextend(d, db_ejud);

replic = 0;


%% MAKE THE PLAN
% getting ready for foreign block inclusion
 
fcast_plan   = plan(m, settings.comprng);
    % fixing expected outlooks
    % fixes are primarily taken from histcore(through changedata)
    % if you want to change some fix, do it in adhocdata, otherwise IT WOULD NOT BE REPORTED!!!!!!
    
     fcast_plan  = exogenize( fcast_plan, 'dot_usdeur',                 settings.usdeurrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_USDEUR',              	settings.usdeurrng);       

     fcast_plan  = exogenize( fcast_plan, 'dot_pstar_other_tilde',      settings.pstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_pstar_other_tilde',      settings.pstarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'dot_pstar_energy_tilde',     settings.pstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_pstar_energy_tilde',    	settings.pstarrng);     
  
     fcast_plan  = exogenize( fcast_plan, 'dot_cpi_star_tilde',         settings.pstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_dot_cpi_star_tilde',    	settings.pstarrng);    
   
     fcast_plan  = exogenize( fcast_plan, 'y_star_gap',                 settings.nstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_y_star_gap',             settings.nstarrng);
    
     fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend_fund',     	settings.nstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_dot_y_star_trend_fund', 	settings.nstarrng);  
   
     fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend_shift',   	settings.nstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_dot_y_star_trend_shift',	settings.nstarrng);  
    
 	 fcast_plan  = exogenize( fcast_plan, 'i_star_eq',              settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_i_star_eq',              settings.istarrng);       
   
     fcast_plan  = exogenize( fcast_plan, 'i_star_eu',                  settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_Istar',                  settings.istarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'i_star',                     settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_shadow_rate',            settings.istarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'i_star_us',                  settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_i_star_us',                  settings.istarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'prem_usdeur',                 settings.usdeurrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_prem_usdeur',              	settings.usdeurrng);   
    
tic
[dbfcast,~,~,~,expectations] = simulate(m, d, settings.comprng, 'plan', fcast_plan, ...
	'expectations',expect_scheme);

disp('simulate time:');
toc
if isequal(exist('expectations','var'),1)
	disp(expectations);
end

%% Create databases
d = dbextend(d,dbfcast);
% add out of model core variables
d = make_outofcore_FB(d, SS, settings);
% make transformations
d_model = make_transformations_FB(d, SS, false);
% add reporting layer of data
dd     = make_transformations_present_FB(d_model, h, settings, SS);

%% Create reports

if do_forecast_book ==1
    db_fcast_book = book_fcast_FB(dd, m, settings, ...
        ['Forecast Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-forecast-book-fb']);
end

if do_forecast_book ==2
    ff  = dbload([settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.csv']);
    db_fcast_book = book_fcast_filter_comp_FB(ff, dd, m, settings, ...
        ['Forecast Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-fcast_filter_comp-fb']);
end

if do_compare_book
	dd_old  = dbload([settings.outdata_dir '\' settings.oldfrc_prefix '-forecast-fb.csv']);
    settings_old  = load([settings.outdata_dir '\' settings.oldfrc_prefix '-settings.mat']);
    db_compare_book = book_fcast_compare_FB(dd, m, SS, dd_old, fcast_plan, settings, settings_old, ...
    ['Analytical Compare Report ' settings.report_prefix 'vs' settings.oldfrc_prefix], ...
    [settings.outreport_dir '\' settings.report_prefix '-' settings.oldfrc_prefix '-anal-comp']);
end

%% Data saving
% complementary databases
save([settings.outdata_dir '\' settings.report_prefix '-model-fb.mat'], 'm');
save([settings.outdata_dir '\' settings.report_prefix '-SS-fb.mat'],'-struct', 'SS');
save([settings.outdata_dir '\' settings.report_prefix '-plan-fb'], 'fcast_plan');
save([settings.outdata_dir '\' settings.report_prefix '-expectations-fb'], 'expect_scheme');
% database with pre-forecast data
dbsave(dbf, [settings.outdata_dir, '\', settings.report_prefix, '-pre-forecast-fb.csv'], Inf, 'format', '%.16e');
% forecast database
dbsave(dd, [settings.outdata_dir '\' settings.report_prefix '-forecast-fb.csv'],Inf,'format','%.16e');

if do_forecast_book
    dbsave(db_fcast_book, [settings.outdata_dir '\' settings.report_prefix ...
        '-forecast-book-fb.csv'],Inf,'format','%.16e');
end