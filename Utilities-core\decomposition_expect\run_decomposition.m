function [decomp_tuneold, ...
    decomp_tunenew, ...
    decomp_tunerev, ...
    decomp_rev, ...
    decomp_rls, ...
    decomp_fcastold, ...
    decomp_fcastnew, ...
    decomp_ini, ...
    decomp_type, ...
	d_new, ...
	d_old, ...
	options, settings] = run_decomposition(new_ID, old_ID, settings, varargin)

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% PURPOSE:	Runs the particular decomposition based on user's input settings
%
% INPUTS:	new_ID - new forecast prefix
% 			old_ID - either 'SS' or old forecast prefix
%
% OUTPUT:	PARTIAL DECOMPOSITIONS
%			decomp_type - string for decomposition settings
%
%-----------------------------
% @ last revision: FK,ZA CNB, Feb 2013
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% presunout o patro vys ve fcich
% if ~recompute && isequal(exist(settings.cmp.decomp_filename, 'file'), 2)
%     load(settings.cmp.decomp_filename,'decomp');
%     if	~isequal(decomp.new_ID, new_ID) || ...
%             ~isequal(decomp.old_ID, old_ID) || ...
%             (decomp.decomp_range(1) > decomp_range(1)) || ...
%             (decomp.decomp_range(end) < decomp_range(end)) || ...
%             ~isequal(decomp.plan_type', plan_type) || ...
%             ~isequal(decomp.doIniToObsDecomp, doIniToObsDecomp) || ...
%             ~isequal(decomp.justFilter, justFilter) || ...
%             ~isequal(decomp.do_standard_rls, do_standard_rls) || ...
%             ~isequal(decomp.rls2shocks, rls2shocks) || ...
%             ~isequal(decomp.limit, limit)
%         clear decomp;
%     else
%         disp('Final decomposition loaded!');
%         disp(settings.cmp.decomp_filename);
%     end
% end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Setting the options
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

default = {...
    'plan_type',            1, ...			%- plan type used for simulations see STANDARD_FCAST_PLANS.M
    'decomp_range',         [],...			%- range of matrix
    'ini2obs',				0,...           % 1 - decompose to obs; 0 - decompose just to ini_vars
    'just_filter',			0,...           % 1 - skipFcast; 0 - decompose with forecast
    'obs_free',             0,...           % 1 - no forecast plan on obs and tunes decomposition, 0 - standard plan
    'rls2shocks',           0,...           % 1 - release decomposed with respect to shocks, 0 - standard decomposition with respect to observations
    'obs2shocks',           0,...           % 1 - filter decomposed with respect to shocks, 0 - standard decomposition with respect to observations
    'detail_level',         1,...			%- detail of decomposition 0 - just vars, 1 - NTF to end pred, 2 - all quarters
    'recompute',            1,...           % 0 - try to find already computed items, 1 - recompute all
    'limit',				1e-8, ...		%- limit for difference to be evaluated
	'show_modelchange'		1, ...			% 1 show changes in model 0 do not show
    };

%--parse options using IRIS --%
options = passopt(default, varargin{1:end});

options.new_ID = new_ID;
options.old_ID = old_ID;

%% setting auxiliary options
if isempty(options.decomp_range)
    options.decomp_range = settings.cmp.hist_rng(1):settings.cmp.fut_rng(end); % za: hist_rng(5+)??? fur_rng(end-4)???
else % za: adjust decomp_range
    if options.ini2obs
        options.decomp_range = max(settings.cmp.hist_rng(1),options.decomp_range(1)):options.decomp_range(end);
    else
        options.decomp_range = max(options.decomp_range(1),settings.cmp.fut_rng(1)):options.decomp_range(end);
    end
    options.decomp_range = options.decomp_range(1):min(settings.cmp.fut_rng(end),options.decomp_range(end));
end

options.is_trans = ~isempty(settings.cmp.trans_rng); % za

if strcmpi(old_ID,'SS')
	options.show_modelchange = 0;
end


%% create decomp_type string from options

decomp_type = [num2str(options.is_trans) num2str(options.ini2obs) num2str(options.just_filter) num2str(options.obs_free) num2str(options.rls2shocks) num2str(options.obs2shocks)];

switch options.detail_level
    case 0
        is_detailed_obs     = [];
        is_detailed_fix     = [];
        is_detailed_eps     = [];
    case 1
        is_detailed_obs     = settings.old.ehist-2:settings.new.ehist;
        is_detailed_fix     = settings.old.start_pred:settings.new.end_pred;
        is_detailed_eps     = settings.old.start_pred:settings.new.end_pred;
    case 2
        is_detailed_obs     = settings.old.shist:settings.new.end_comp;
        is_detailed_fix     = settings.old.shist:settings.new.end_comp;
        is_detailed_eps     = settings.old.shist:settings.new.end_comp;
    otherwise
        disp('Wrong detail level.')
            return
end

%% define names for decompositions
% za: optimalizovany kod, snad funkcny :-)

part_decomp_str = {...
    'tuneold'
    'tunenew'
    'tunerev'
    'rev'
    'rls'
    'fcastold'
    'fcastnew'
    'ini'
    };

for ix = 1:length(part_decomp_str)
    settings.cmp.([part_decomp_str{ix} '_filename']) = [];
end

[settings.cmp.tuneold_filename ...
    settings.cmp.tunenew_filename ...
    settings.cmp.tunerev_filename ...
    settings.cmp.rev_filename ...
    settings.cmp.rls_filename ...
    settings.cmp.fcastold_filename ...
    settings.cmp.fcastnew_filename ...
    settings.cmp.ini_filename ] = define_names_for_decomposition(settings, options, decomp_type);

[~,~,~,~,~,~,isWarning] = restore_decomp_settings(decomp_type);
if isWarning
    error('Wrong parameters settings for decomposition! Check combinations!');
end


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% LOAD
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

disp('Loading data...');

for ix = 1:length(part_decomp_str)
    eval(['decomp_' part_decomp_str{ix} ' = [];']);
    if ~isempty(settings.cmp.([part_decomp_str{ix} '_filename'])) && ~options.recompute
        try
            load(settings.cmp.([part_decomp_str{ix} '_filename']),['decomp_' part_decomp_str{ix}]);
        catch ME
        end
    end
end

%% load kalman & params if needed

% proc loaduju a kdy? nutno jeste zkontrolovat
SS = load([settings.new.filterss_fb_name]);

if (isempty(decomp_tuneold) && ~isempty(settings.cmp.tuneold_filename)) || ...
        (isempty(decomp_tunenew) && ~isempty(settings.cmp.tunenew_filename)) || ...
        (isempty(decomp_tunerev) && ~isempty(settings.cmp.tunerev_filename)) || ...
        (isempty(decomp_rev) && ~isempty(settings.cmp.rev_filename)) || ...
        (isempty(decomp_rls) && ~isempty(settings.cmp.rls_filename))
    
    load([settings.new.kalman_fb_name]);
    kf_new = m; clear m;
    if ~strcmp(upper(old_ID),'SS')
        load([settings.old.kalman_fb_name]);
        kf_old = m; clear m;
    else
        kf_old = kf_new;
    end
    SS.filtering = true;
end

%% load models & expectations if needed

if (isempty(decomp_fcastold) && ~isempty(settings.cmp.fcastold_filename)) || ...
        (isempty(decomp_fcastnew) && ~isempty(settings.cmp.fcastnew_filename)) || ...
        (isempty(decomp_ini) && ~isempty(settings.cmp.ini_filename)) || ...
        (~options.just_filter && isequal(exist('kf_new','var'),1))
    
    load([settings.new.model_fb_name]);
    m_new = m; clear m;
	if isfield(settings.new,'expectations_fb_name')
		load(settings.new.expectations_fb_name);
		expectations_new = expect_scheme; clear expect_scheme;
		if ~isa(expectations_new,'expDesign')
			error('New limited rational expectations design is not a proper class object!');
		end
	else
		expectations_new = false;
	end
    if ~strcmpi(old_ID,'SS')
        load([settings.old.model_fb_name]);
        m_old = m; clear m;
		if isfield(settings.old,'expectations_fb_name')
			load(settings.old.expectations_fb_name);
            expectations = expect_scheme; clear expect_scheme;
			if ~isa(expectations,'expDesign')
				error('Old limited rational expectations design is not a proper class object!');
			end
			if isa(expectations_new,'expDesign')
				if isequal(expectations_new,expectations)
					clear expectations_new; % only 'expectations' remains
				else
					error('Limited rational expectations designs are not equal! Treat changes as a new calibration of parameters!');
				end
			else
				error('Limited rational expectations must be applied in both forecasts!');
			end
		elseif isa(expectations_new,'expDesign')
			error('Limited rational expectations must be applied in both forecasts!');
		end
    else
        m_old = m_new;
		expectations = expectations_new; clear expectations_new;
    end
end

%% read datasets

if (isequal(exist('kf_new','var'),1) && ~isequal(exist('m_new','var'),1)) || options.just_filter
	% new kalman data
    d_new = dbload(settings.new.filter_name);
	if isequal(exist('kf_new','var'),1)
		yList_new = get(kf_new,'yList');
		eList_new = get(kf_new,'eList');
		xList_new = get(kf_new,'xList');
		d_new = d_new*[yList_new,eList_new,xList_new];
		d_check_new = d_new;
	end
	% old kalman data
    if ~strcmpi(old_ID,'SS')
        d_old = dbload(settings.old.filter_name);
		if isequal(exist('kf_old','var'),1)
			yList_old = get(kf_old,'yList');
			eList_old = get(kf_old,'eList');
			xList_old = get(kf_old,'xList');
			d_old = d_old*[yList_old,eList_old,xList_old];
			d_check_old = d_old;
		end
    else
		if isequal(exist('kf_new','var'),1)
			d_old = sstatedb(kf_new, settings.new.shist:settings.new.end_comp);
	        d_check_old = d_old;
		else 
			d_old = dbempty;
		end
    end
else
	% new simulate data
    d_new = dbload(settings.new.data_fb_name);
	if isequal(exist('m_new','var'),1)
        tuneList_new = dbnames(d_new,'nameFilter','^tune_\w*');
		eList_new = get(m_new,'eList');
		xList_new = get(m_new,'xList');
		if isequal(exist('kf_new','var'),1)
			yList_new = get(kf_new,'yList');
		else yList_new = {};
		end
		d_new = d_new*[yList_new,eList_new,xList_new,tuneList_new];
		d_check_new = d_new;
	end
	% old simulate data
	if ~strcmpi(old_ID,'SS')
		d_old = dbload(settings.old.data_fb_name);
		if isequal(exist('m_old','var'),1)
%             tuneList_old = dbnames(d_old,'nameFilter','^tune_\w*');
			eList_old = get(m_old,'eList');
			xList_old = get(m_old,'xList');
			if isequal(exist('kf_old','var'),1)
				yList_old = get(kf_old,'yList');
			else yList_old = {};
			end
			d_old = d_old*[yList_old,eList_old,xList_old];%,tuneList_old
			d_check_old = d_old;
		end
	else
		if isequal(exist('kf_new','var'),1)
			d_old = sstatedb(kf_new, settings.new.shist:settings.new.end_comp);
%             tuneList_old = dbnames(d_old,'nameFilter','^tune_\w*');
			yList_old = get(kf_old,'yList');
			eList_old = get(kf_old,'eList');
			xList_old = get(kf_old,'xList');
			d_old = d_old*[xList_old,eList_old];%,tuneList_old
			for ix = 1 : length(yList_old)
				d_old.(yList_old{ix}) = tseries;
			end
		elseif isequal(exist('m_new','var'),1)
			d_old = sstatedb(m_new, settings.new.shist:settings.new.end_comp);
		else
			d_old = dbempty;
		end
		d_check_old = d_old;
	end
end

%% set or load plans

if (isequal(exist('kf_new','var'),1) && ~options.just_filter) || ...
        isequal(exist('m_new','var'),1)
    fcast_plan_decomp  = standard_fcast_plans(options.plan_type, m_new, settings.new);
    if options.ini2obs
        fcast_plan_decomp_old = amend_fcast_plans_to_old(fcast_plan_decomp, ...
            m_new, settings.new, settings.old);
    end
    
    if options.obs_free
        fcast_plan_obs_decomp = plan(m_new,settings.new.comprng);
    else
        fcast_plan_obs_decomp = fcast_plan_decomp;
    end
    
end

%% extract tunes and observations

if isequal(exist('kf_new','var'),1)
    % select only kalman observables and adjust appropriate range
    yList_new = get(kf_new,'yList');
    d_check_new = d_check_new*yList_new;
    d_check_new = dbclip(d_check_new,settings.new.hrng);
    d_check_old = d_check_old*yList_new;
    d_check_old = dbclip(d_check_old,settings.old.hrng);
    % select tunes
    dtunes_new = d_check_new*dbnames(d_check_new,'nameFilter','^tune_\w*');
    dtunes_old = d_check_old*dbnames(d_check_old,'nameFilter','^tune_\w*');
    if options.is_trans
        dtunes_rev = dbclip(dtunes_new,settings.cmp.hist_rng);
    end
    
    % select observations
    d_check_new = d_check_new*dbnames(d_check_new,'nameFilter','^obs_\w*');
    d_check_old = d_check_old*dbnames(d_check_old,'nameFilter','^obs_\w*');
end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Recomputing filter and forecast
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% f_rep_new
if (isempty(decomp_fcastnew) && ~isempty(settings.cmp.fcastnew_filename)) || ...
        (options.is_trans && isempty(decomp_tunenew) && ~isempty(settings.cmp.tunenew_filename)) || ...
		(~options.is_trans && isempty(decomp_tunerev) && ~isempty(settings.cmp.tunerev_filename))
    disp('Checking new data filtration on new model.');
    [f_rep_new] = datamodel_replicate(d_new, SS, ...
        'kalman', kf_new, ...
        'range_hist', settings.new.hrng, ...
        'tunes', dtunes_new, ...
        'limit', options.limit, ...
        'check', true);
    disp('Generating new simulation without fixes and residuals.');
    if options.just_filter
        % final simulation is also the simulation without fixes and residuals
        [f_rep_new] = datamodel_replicate(f_rep_new, SS, ...
            'kalman', kf_new, ...
            'range_hist', settings.new.hrng(1):options.decomp_range(end), ...
            'tunes', dtunes_new, ...
            'limit', options.limit, ...
            'check', true);
    else
        [f_rep_new] = datamodel_replicate(d_check_new, SS, ...
            'kalman', kf_new, ...
            'model', m_new, ...
            'range_hist', settings.new.hrng, ...
            'range_fcast', settings.new.comprng, ...
            'tunes', dtunes_new, ...
			'expectations', expectations, ...
            'limit', options.limit);
    end
end

% f_rep_old
if (isempty(decomp_fcastold) && ~isempty(settings.cmp.fcastold_filename)) || ...
        (isempty(decomp_tuneold) && ~isempty(settings.cmp.tuneold_filename))
    disp('Checking old data filtration on old model.');
    [~] = datamodel_replicate(d_old, SS, ...
        'kalman', kf_old, ...
        'range_hist', settings.old.hrng, ...
        'tunes', dtunes_old, ...
        'limit', options.limit, ...
        'check', true);
    disp('Resimulating old data filtration on new model.');
    [f_rep_old] = datamodel_replicate(d_old, SS, ...
        'kalman', kf_new, ...
        'range_hist', settings.old.hrng, ...
        'tunes', dtunes_old, ...
        'limit', options.limit, ...
        'check', true);
    disp('Generating old simulation without fixes and residuals.');
    if options.just_filter
        % final simulation is also the simulation without fixes and residuals
        [f_rep_old] = datamodel_replicate(f_rep_old, SS, ...
            'kalman', kf_new, ...
            'range_hist', settings.old.hrng(1):options.decomp_range(end), ...
            'tunes', dtunes_old, ...
            'limit', options.limit, ...
            'check', true);
    else
        [f_rep_old] = datamodel_replicate(d_check_old, SS, ...
            'kalman', kf_new, ...
            'model', m_new, ...
            'range_hist', settings.old.hrng, ...
            'range_fcast', settings.old.comprng, ...
            'tunes', dtunes_old, ...
			'expectations', expectations, ...
            'limit', options.limit);
    end
end

% d_rep_new
if (isempty(decomp_fcastnew) && ~isempty(settings.cmp.fcastnew_filename))
    disp('Checking new data simulation on new model.');
    [d_rep_new] = datamodel_replicate(d_new, SS, ...
        'kalman', kf_new, ...
        'model', m_new, ...
        'range_hist', settings.new.hrng, ...
        'range_fcast', settings.new.comprng, ...
        'tunes', dtunes_new, ...
		'expectations', expectations, ...
        'limit', options.limit, ...
        'check', true);
elseif (isempty(decomp_ini) && ~isempty(settings.cmp.ini_filename))
    disp('Checking new data simulation on new model.');
    [d_rep_new] = datamodel_replicate(d_new, SS, ...
        'model', m_new, ...
        'range_fcast', settings.new.comprng, ...
		'expectations', expectations, ...
        'limit', options.limit, ...
        'check', true);
end

% d_rep_old
if (isempty(decomp_fcastold) && ~isempty(settings.cmp.fcastold_filename))
    disp('Checking old data simulation on old model.');
    [~] = datamodel_replicate(d_old, SS, ...
        'kalman', kf_old, ...
        'model', m_old, ...
        'range_hist', settings.old.hrng, ...
        'range_fcast', settings.old.comprng, ...
        'tunes', dtunes_old, ...
		'expectations', expectations, ...
        'limit', options.limit, ...
        'check', true);
    disp('Resimulating old data simulation on new model.');
    [d_rep_old] = datamodel_replicate(d_old, SS, ...
        'kalman', kf_new, ...
        'model', m_new, ...
        'range_hist', settings.old.hrng, ...
        'range_fcast', settings.old.comprng, ...
        'tunes', dtunes_old, ...
		'expectations', expectations, ...
        'limit', options.limit, ...
        'check', true);
elseif (isempty(decomp_ini) && ~isempty(settings.cmp.ini_filename))
    disp('Checking old data simulation on old model.');
    [~] = datamodel_replicate(d_old, SS, ...
        'model', m_old, ...
        'range_fcast', settings.old.comprng, ...
		'expectations', expectations, ...
        'limit', options.limit, ...
        'check', true);
    disp('Resimulating old data simulation on new model.');
    [d_rep_old] = datamodel_replicate(d_old, SS, ...
        'model', m_new, ...
        'range_fcast', settings.old.comprng, ...
		'expectations', expectations, ...
        'limit', options.limit, ...
        'check', true);
end

% f_free_new
if (options.is_trans && isempty(decomp_rls) && ~isempty(settings.cmp.rls_filename)) || ...
		(~options.is_trans && isempty(decomp_rev) && ~isempty(settings.cmp.rev_filename)) || ...
		(isempty(decomp_tunerev) && ~isempty(settings.cmp.tunerev_filename))
	if options.just_filter
		[f_free_new] = datamodel_replicate(d_check_new, SS, ...
			'kalman', kf_new, ...
			'range_hist', settings.new.hrng, ...
			'limit', options.limit);
		[f_free_new] = datamodel_replicate(f_free_new, SS, ...
			'kalman', kf_new, ...
			'range_hist', settings.old.hrng(1):options.decomp_range(end), ...
			'limit', options.limit, ...
			'check', true);
	else
		[f_free_new] = datamodel_replicate(d_check_new, SS, ...
			'kalman', kf_new, ...
			'model', m_new, ...
			'range_hist', settings.new.hrng, ...
			'range_fcast', settings.new.comprng, ...
			'expectations', expectations, ...
			'limit', options.limit);
	end
end

% f_free_old
if (isempty(decomp_tuneold) && ~isempty(settings.cmp.tuneold_filename)) || ...
        (isempty(decomp_rev) && ~isempty(settings.cmp.rev_filename))
    disp('Generating old free simulation without tunes.');
    if options.just_filter
        [f_free_old] = datamodel_replicate(d_check_old, SS, ...
            'kalman', kf_new, ...
            'range_hist', settings.old.hrng, ...
            'tunes', dbempty);
        [f_free_old] = datamodel_replicate(f_free_old, SS, ...
            'kalman', kf_new, ...
            'range_hist', settings.old.hrng(1):options.decomp_range(end), ...
            'tunes', dbempty, ...
            'limit', options.limit, ...
            'check', true);
    else
        [f_free_old] = datamodel_replicate(d_check_old, SS, ...
            'kalman', kf_new, ...
            'model', m_new, ...
            'range_hist', settings.old.hrng, ...
            'range_fcast', settings.old.comprng, ...
			'expectations', expectations, ...
            'limit', options.limit);
    end
end

% f_free_mix
if options.is_trans
	if (isempty(decomp_rev) && ~isempty(settings.cmp.rev_filename)) || ...
			(isempty(decomp_rls) && ~isempty(settings.cmp.rls_filename))
		disp('Generating free simulation without tunes with revisions on history range.');
		if options.just_filter
			[f_free_mix] = datamodel_replicate(dbclip(d_check_new,settings.old.hrng), SS, ...
				'kalman', kf_new, ...
				'range_hist', settings.old.hrng, ...
				'tunes', dbempty);
			[f_free_mix] = datamodel_replicate(f_free_mix, SS, ...
				'kalman', kf_new, ...
				'range_hist', settings.old.hrng(1):options.decomp_range(end), ...
				'tunes', dbempty, ...
				'limit', options.limit, ...
				'check', true);
		else
			[f_free_mix] = datamodel_replicate(dbclip(d_check_new,settings.old.hrng), SS, ...
				'kalman', kf_new, ...
				'model', m_new, ...
				'range_hist', settings.old.hrng, ...
				'range_fcast', settings.old.comprng, ...
				'expectations', expectations, ...
				'limit', options.limit);
			[f_free_mix] = datamodel_replicate(f_free_mix + dbclip(d_check_new,settings.old.hrng), SS, ...
				'kalman', kf_new, ...
				'model', m_new, ...
				'range_hist', settings.new.hrng, ...
				'range_fcast', settings.new.comprng, ...
				'expectations', expectations, ...
				'check', true, ...
				'limit', options.limit);
		end
	end
elseif (isempty(decomp_rev) && ~isempty(settings.cmp.rev_filename))
	f_free_mix = f_free_new;
end

% f_comb_new
if options.is_trans
	if (isempty(decomp_tunenew) && ~isempty(settings.cmp.tunenew_filename)) || ...
			(isempty(decomp_tunerev) && ~isempty(settings.cmp.tunerev_filename)) || ...
            ((isempty(decomp_rls) && ~isempty(settings.cmp.rls_filename)) && options.obs2shocks)
		disp('Generating free simulation with new observations and tunes updated on history range.');
		if options.just_filter
			[f_comb_new] = datamodel_replicate(d_check_new, SS, ...
				'kalman', kf_new, ...
				'range_hist', settings.new.hrng, ...
				'tunes', dtunes_rev);
			[f_comb_new] = datamodel_replicate(f_comb_new, SS, ...
				'kalman', kf_new, ...
				'range_hist', settings.old.hrng(1):options.decomp_range(end), ...
				'tunes', dtunes_rev, ...
				'limit', options.limit, ...
				'check', true);
		else
			[f_comb_new] = datamodel_replicate(d_check_new, SS, ...
				'kalman', kf_new, ...
				'tunes', dtunes_rev, ...
				'range_hist', settings.new.hrng);
			[f_comb_new] = datamodel_replicate(f_comb_new, SS, ...
				'kalman', kf_new, ...
				'model', m_new, ...
				'tunes', dtunes_rev, ...
				'range_hist', settings.new.hrng, ...
				'range_fcast', settings.new.comprng, ...
				'expectations', expectations, ...
				'check', true, ...
				'limit', options.limit);
		end
	end
elseif (isempty(decomp_tunerev) && ~isempty(settings.cmp.tunerev_filename))
	f_comb_new = f_rep_new;
end

if ((isempty(decomp_rev) && ~isempty(settings.cmp.rev_filename)) || ...
        (isempty(decomp_rls) && ~isempty(settings.cmp.rls_filename))) && options.obs2shocks
    shocks.hrng = settings.new.hrng(1):settings.new.hrng(1)+3;
    shocks.fcastrng = settings.new.hrng(1)+4:settings.new.comprng(end);
    
    eps_names   = dbnames(f_rep_old,'nameFilter','^eps_\w*');
    db_pom = dbclip(f_rep_old,shocks.hrng);
    db_pom = dbextend(db_pom,f_rep_old*eps_names);

	if isa(expectations,'expDesign')
		db_pom =  simulate(m_new, db_pom, shocks.fcastrng, ...
			'expectations',expectations,'anticipate',false);
	else
		db_pom =  simulate(m_new, db_pom, shocks.fcastrng);
	end

    db_pom =  dbextend(db_pom,f_rep_old*eps_names);
    f_rep_old_shocks  = dbextend(f_rep_old,db_pom);
    
    eps_names   = dbnames(f_rep_new,'nameFilter','^eps_\w*');
    db_pom = dbclip(f_rep_new,shocks.hrng);
    db_pom = dbextend(db_pom,f_rep_new*eps_names);
    
    if isa(expectations,'expDesign')
		db_pom =  simulate(m_new, db_pom, shocks.fcastrng, ...
			'expectations',expectations,'anticipate',false);
	else
		db_pom =  simulate(m_new, db_pom, shocks.fcastrng);
	end

    db_pom =  dbextend(db_pom,f_rep_new*eps_names);
    f_rep_new_shocks  = dbextend(f_rep_new,db_pom);
    
    if ~isempty(settings.cmp.trans_rng)
        eps_names   = dbnames(f_comb_new,'nameFilter','^eps_\w*');
        db_pom = dbclip(f_comb_new,shocks.hrng);
        db_pom = dbextend(db_pom,f_comb_new*eps_names);
     
		if isa(expectations,'expDesign')
	        db_pom =  simulate(m_new, db_pom, shocks.fcastrng, ...
			'expectations',expectations,'anticipate',false);
		else
			db_pom =  simulate(m_new, db_pom, shocks.fcastrng);
		end

        db_pom =  dbextend(db_pom,f_comb_new*eps_names);
        f_comb_new_shocks  = dbextend(f_comb_new,db_pom);
    end
end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% COMPUTING DECOMPOSITIONs
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% decomp_ini
if (isempty(decomp_ini) && ~isempty(settings.cmp.ini_filename))
    disp('Forecast decomposition of initial state, fixes and residuals.');
    [ decomp_ini ] = decomp_all(m_new, d_rep_old, d_rep_new, ...
        options.decomp_range, ...
        'plan', fcast_plan_decomp, ...
        'simrng', settings.new.comprng,...
        'is_detailed_fix_', is_detailed_fix, ...
        'is_detailed_eps_', is_detailed_eps, ...
		'expectations', expectations);
    decomp_ini = reset_input_datenames(decomp_ini, 'decomp_ini');
    decomp_ini.new_ID			= new_ID;
    decomp_ini.old_ID			= old_ID;
    decomp_ini.plan_type		= options.plan_type;
    decomp_ini.decomp_type      = decomp_type;
    decomp_ini.detail_level     = options.detail_level;
    decomp_ini.limit			= options.limit;
    save(settings.cmp.ini_filename,'decomp_ini');
end

% decomp_tuneold
if (isempty(decomp_tuneold) && ~isempty(settings.cmp.tuneold_filename))
    disp('Filter decomposition of old tunes.');
    [ decomp_tuneold ] = decomp_all(m_new, f_rep_old, f_free_old, ...
        options.decomp_range, ...
        'plan', fcast_plan_obs_decomp, ...
        'filter_model', kf_new, ...
        'filter_range', settings.old.hrng, ...
        'simrng', settings.new.comprng, ...
        'trans_range', settings.old.ehist+1:settings.new.ehist, ...
        'ini2obs', true,...
        'is_detailed_obs_', is_detailed_obs, ...
        'is_detailed_fix_', is_detailed_fix, ...
        'is_detailed_eps_', is_detailed_eps, ...
		'expectations', expectations);
    decomp_tuneold = reset_input_datenames(decomp_tuneold, 'tuneold');
    decomp_tuneold.new_ID			= new_ID;
    decomp_tuneold.old_ID			= old_ID;
    decomp_tuneold.plan_type		= options.plan_type;
    decomp_tuneold.decomp_type		= decomp_type;
    decomp_tuneold.detail_level     = options.detail_level;
    decomp_tuneold.limit			= options.limit;
    save(settings.cmp.tuneold_filename,'decomp_tuneold');
end

% decomp_tunerev
if (isempty(decomp_tunerev) && ~isempty(settings.cmp.tunerev_filename))
    disp('Filter decomposition of new tunes on history.');
    [ decomp_tunerev ] = decomp_all(m_new, f_free_new, f_comb_new, ...
        options.decomp_range,...
        'plan', fcast_plan_obs_decomp, ...
        'filter_model', kf_new, ...
        'filter_range', settings.new.hrng, ...
        'simrng', settings.new.comprng, ...
        'ini2obs', true,...
        'is_detailed_obs_', is_detailed_obs, ...
        'is_detailed_fix_', is_detailed_fix, ...
        'is_detailed_eps_', is_detailed_eps, ...
		'expectations', expectations);
    decomp_tunerev = reset_input_datenames(decomp_tunerev, 'tunenew','settings',settings);
    decomp_tunerev.new_ID			= new_ID;
    decomp_tunerev.old_ID			= old_ID;
    decomp_tunerev.plan_type		= options.plan_type;
    decomp_tunerev.decomp_type      = decomp_type;
    decomp_tunerev.detail_level     = options.detail_level;
    decomp_tunerev.limit			= options.limit;
    save(settings.cmp.tunerev_filename,'decomp_tunerev');
end

% decomp_tunenew
if options.is_trans && (isempty(decomp_tunenew) && ~isempty(settings.cmp.tunenew_filename))
	disp('Filter decomposition of new tunes on release.');
	[ decomp_tunenew ] = decomp_all(m_new, f_comb_new, f_rep_new, ...
		options.decomp_range,...
		'plan', fcast_plan_obs_decomp, ...
		'filter_model', kf_new, ...
		'filter_range', settings.new.hrng, ...
		'simrng', settings.new.comprng, ...
		'ini2obs', true,...
		'is_detailed_obs_', is_detailed_obs, ...
		'is_detailed_fix_', is_detailed_fix, ...
		'is_detailed_eps_', is_detailed_eps, ...
		'expectations', expectations);
	decomp_tunenew = reset_input_datenames(decomp_tunenew, 'tunerls','settings',settings);
	decomp_tunenew.new_ID			= new_ID;
	decomp_tunenew.old_ID			= old_ID;
	decomp_tunenew.plan_type		= options.plan_type;
	decomp_tunenew.decomp_type      = decomp_type;
	decomp_tunenew.detail_level     = options.detail_level;
	decomp_tunenew.limit			= options.limit;
	save(settings.cmp.tunenew_filename,'decomp_tunenew');
end

% decomp_rev
if (isempty(decomp_rev) && ~isempty(settings.cmp.rev_filename))
    if ~options.obs2shocks
        disp('Standard filter decomposition of revisions.');
        [ decomp_rev ] = decomp_all(m_new, f_free_old, f_free_mix, ...
            options.decomp_range, ...
            'plan', fcast_plan_obs_decomp, ...
            'filter_model', kf_new, ...
            'filter_range', settings.old.hrng, ...
            'simrng', settings.new.comprng, ...
            'trans_range', settings.old.ehist+1:settings.new.ehist, ...
            'ini2obs', true,...
            'is_detailed_obs_', is_detailed_obs, ...
            'is_detailed_fix_', is_detailed_fix, ...
            'is_detailed_eps_', is_detailed_eps, ...
			'expectations', expectations);   
    else
        disp('Filter decomposition of revisions with respect to shocks.');        
        if options.is_trans
            [ decomp_rev ] = decomp_all(m_new, f_rep_old_shocks, f_comb_new_shocks, ...
                options.decomp_range, ...
                'plan', fcast_plan_obs_decomp, ...
                'filter_model', kf_new, ...
                'filter_range', shocks.hrng, ...
                'simrng', shocks.fcastrng, ...
                'expect_range', settings.new.comprng,...                
                'ini2obs', false,...
                'is_detailed_obs_', is_detailed_obs, ...
                'is_detailed_fix_', is_detailed_fix, ...
                'is_detailed_eps_', is_detailed_eps, ...
				'expectations', expectations);
               decomp_rev.hist_rng = settings.old.hrng;
               decomp_rev.trans_rng = settings.old.ehist+1:settings.new.ehist;
               decomp_rev.fut_rng = settings.new.comprng;
               decomp_rev.expect_rng = settings.new.comprng;
        else
            [ decomp_rev ] = decomp_all(m_new, f_rep_old_shocks, f_rep_new_shocks, ...
                options.decomp_range, ...
                'plan', fcast_plan_obs_decomp, ...
                'filter_model', kf_new, ...
                'filter_range', shocks.hrng, ...
                'simrng', shocks.fcastrng, ...
                'expect_range', settings.new.comprng,...
                'ini2obs', false,...
                'is_detailed_obs_', is_detailed_obs, ...
                'is_detailed_fix_', is_detailed_fix, ...
                'is_detailed_eps_', is_detailed_eps, ...
				'expectations', expectations);
               decomp_rev.hist_rng = settings.old.hrng;
               decomp_rev.trans_rng = [];
               decomp_rev.fut_rng = settings.new.comprng;  
               decomp_rev.expect_rng = settings.new.comprng;
        end
    end
    decomp_rev = reset_input_datenames(decomp_rev, 'rev');
    decomp_rev.new_ID			= new_ID;
    decomp_rev.old_ID			= old_ID;
    decomp_rev.plan_type		= options.plan_type;
    decomp_rev.decomp_type      = decomp_type;
    decomp_rev.detail_level     = options.detail_level;
    decomp_rev.limit			= options.limit;
    save(settings.cmp.rev_filename,'decomp_rev');
end

% decomp_rls
if options.is_trans && (isempty(decomp_rls) && ~isempty(settings.cmp.rls_filename))
    if ~options.obs2shocks
        disp('Standard filter decomposition of release.');
        [ decomp_rls ] = decomp_all(m_new, f_free_mix , f_free_new, ...
            options.decomp_range, ...
            'plan', fcast_plan_obs_decomp, ...
            'filter_model', kf_new, ...
            'filter_range', settings.new.hrng, ...
            'simrng', settings.new.comprng, ...
            'ini2obs', true,...
            'is_detailed_obs_', is_detailed_obs, ...
            'is_detailed_fix_', is_detailed_fix, ...
            'is_detailed_eps_', is_detailed_eps, ...
			'expectations', expectations);
        if options.rls2shocks
            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
            disp('Filter decomposition of release with respect to shocks.');
            decomp_rls = divide_rls_into_shocks_and_ini(decomp_rls,...
                m_new, kf_new, f_free_mix, f_free_new, settings,...
                fcast_plan_obs_decomp,...
                is_detailed_obs, ...
                is_detailed_fix, ...
                is_detailed_eps, ...
                options.decomp_range, ...
                options.limit, ...
				expectations);
            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        end
    else
        disp('Filter decomposition of release with respect to shocks.');
        [ decomp_rls ] = decomp_all(m_new, f_comb_new_shocks, f_rep_new_shocks, ...
            options.decomp_range, ...
            'plan', fcast_plan_obs_decomp, ...
            'filter_model', kf_new, ...
            'filter_range', shocks.hrng, ...
            'simrng', shocks.fcastrng, ...
            'expect_range', settings.new.comprng, ...
            'ini2obs', false,...
            'is_detailed_obs_', is_detailed_obs, ...
            'is_detailed_fix_', is_detailed_fix, ...
            'is_detailed_eps_', is_detailed_eps, ...
			'expectations', expectations);
    end
    decomp_rls = reset_input_datenames(decomp_rls, 'rls');
    decomp_rls.new_ID			= new_ID;
    decomp_rls.old_ID			= old_ID;
    decomp_rls.plan_type		= options.plan_type;
    decomp_rls.decomp_type      = decomp_type;
    decomp_rls.detail_level     = options.detail_level;
    decomp_rls.limit			= options.limit;
    decomp_rls.expect_rng       = settings.new.comprng;
    save(settings.cmp.rls_filename,'decomp_rls');
end

% decomp_fcastold
if (isempty(decomp_fcastold) && ~isempty(settings.cmp.fcastold_filename))
    disp('Forecast decomposition of old fixes and residuals.');
    [ decomp_fcastold ] = decomp_all(m_new,d_rep_old ,f_rep_old , ...
        options.decomp_range, ...
        'plan', fcast_plan_decomp_old, ...
        'simrng', settings.old.comprng,...
        'is_detailed_obs_', is_detailed_obs, ...
        'is_detailed_fix_', is_detailed_fix, ...
        'is_detailed_eps_', is_detailed_eps, ...
		'expectations', expectations);
    
    if options.is_trans
        disp('Forecast decomposition of old vs old free into transition and sim range.');
        decomp_fcastold = divide_old_into_trans_and_future(decomp_fcastold,...
            m_new,d_rep_old,f_rep_old,settings,...
            fcast_plan_decomp,...
            is_detailed_obs, ...
            is_detailed_fix, ...
            is_detailed_eps, ...
            options.decomp_range, ...
            expectations);
    else
        decomp_fcastold = reset_input_datenames(decomp_fcastold, 'fcastold');
    end
    decomp_fcastold.new_ID			= new_ID;
    decomp_fcastold.old_ID			= old_ID;
    decomp_fcastold.plan_type		= options.plan_type;
    decomp_fcastold.decomp_type      = decomp_type;
    decomp_fcastold.detail_level     = options.detail_level;
    decomp_fcastold.limit			= options.limit;
    save(settings.cmp.fcastold_filename,'decomp_fcastold');
end

% decomp_fcastnew
if (isempty(decomp_fcastnew) && ~isempty(settings.cmp.fcastnew_filename))
    disp('Forecast decomposition of new fixes and residuals.');
    [ decomp_fcastnew ] = decomp_all(m_new, f_rep_new, d_rep_new, ...
        options.decomp_range, ...
        'plan', fcast_plan_decomp, ...
        'simrng', settings.new.comprng,...
        'is_detailed_obs_', is_detailed_obs, ...
        'is_detailed_fix_', is_detailed_fix, ...
        'is_detailed_eps_', is_detailed_eps, ...
		'expectations', expectations);
    decomp_fcastnew = reset_input_datenames(decomp_fcastnew, 'fcastnew');
    decomp_fcastnew.new_ID			= new_ID;
    decomp_fcastnew.old_ID			= old_ID;
    decomp_fcastnew.plan_type		= options.plan_type;
    decomp_fcastnew.decomp_type      = decomp_type;
    decomp_fcastnew.detail_level     = options.detail_level;
    decomp_fcastnew.limit			= options.limit;
    save(settings.cmp.fcastnew_filename,'decomp_fcastnew');
end

% % define names for decompositions
% switch decomp_type
%     case {'100000','000000'} % standard forecast decomposition, history just into initial conditions
%     case {'110000','010000'} % whole forecast, filter into observables (standard GRIP decomposition)
%     case {'110011','010001','010011'} % whole forecast, filter into shocks
%     case '110010' % whole forecast, filter history into into observables, rls into shocks (implicitly must be trans range)
%     case {'110111','010101','010111'} % whole forecast, obs free, filter into shocks
%     case {'110100','010100'} % whole forecast, obs free, filter into observables
%     case '110110' % whole forecast, obs free, filter history into into observables, rls into shocks (implicitly must be trans range)
%     case {'111011','011011'} % filter shock decomposition
%     case {'111000','011000'} % filter into observables decomposition
%     case '111010' % filter history into observables rls into shocks (implicitly must be trans range)
%     otherwise
%         disp('Nektera nastaveni se vzajemne vylucuji!');
%         disp('Rozseknuti predcasne zastaveno.');
%         return;
% end

end % COMPUTING DECOMPOSITION
