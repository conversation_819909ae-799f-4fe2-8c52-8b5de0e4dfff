% Driver provides insight to the forecast performance of given model. Driver
% is able to compare the forecast performance of two models as well.

clear all; close all;


% load GLOBALSETTINGS (= variable sr_ID with last report prefix) or set sr_ID directly
load('GLOBALSETTINGS.mat');
settings = load_settings(FB_ID);

driver_filter;

ID_hair_old = 'model_final';

new_compute    = 1; % to compute hairs for given model
compare_two_fb = 0; % to compare two models

if new_compute==1

    save([settings.outdata_dir '\' settings.report_prefix '-model-hair-fb.mat'],'m','SS')

    for iii=1:(settings.ehist-qq(2000,2)) %62
        iii  
        settings.shist        = qq(1996,1); 
        % end of history in the histcore database for filtering
        settings.ehist        = qq(2000,2) + iii;
        % end of ouf outlooks data for filtering
        settings.hrng         = settings.shist:settings.ehist;
        settings.end_exo      = settings.ehist;
        % start of prediction range 
        settings.start_pred   = settings.ehist+1;
        % end of prediction range 
        settings.end_pred     = settings.ehist+8;
        settings.end_comp     = settings.end_pred;
        settings.comprng      = settings.start_pred:settings.end_pred;
        settings.fcastrng  = settings.start_pred:settings.end_pred;  % forecast

        % settings.pstarrng = settings.start_pred:settings.end_pred;  % foreign inflation rate
        % settings.targetrng = settings.start_pred:settings.end_pred;  % inflation target
        % settings.regrng    = settings.start_pred:settings.end_pred;  % regulated prices
        % settings.labforcerng    = settings.start_pred:settings.end_pred;  % labour forec
        % settings.nstarrng  = settings.start_pred:settings.end_pred;  % foreign demand
        % settings.istarrng  = settings.start_pred:settings.end_pred;  % foreign interest rate
        % settings.grng   = settings.start_pred:settings.end_pred;  % real government
        % settings.gprng  = settings.start_pred:settings.end_pred;  % nom. government
        % 
        % % settings.cmp_rng   = settings.ehist-4:settings.end_pred;     % comparison of actual vs. previous forecast
        % % settings.plot_rng  = settings.start_plot:settings.end_pred;  % default graph range
        % % settings.extd_rng  = settings.starte_plot:settings.end_pred; % extended graph range
        % % settings.shrt_rng  = settings.start_plot:settings.ehist;     % for PP
        % % settings.shex_rng  = settings.starte_plot:settings.ehist;    % for extended PP
         settings.whole_rng = settings.shist:settings.end_pred;       % overall data
        % % settings.rng_db = qq(1998,1):settings.end_pred;         % database range for book forecast xls file, default is +4
        % % settings.rng_g   = dec2dat(floor(dat2dec(settings.start_pred-18)),4):settings.end_pred; % graphs in analytical report
        % % settings.rng_t   = dec2dat(floor(dat2dec(settings.start_pred-2)),4):settings.end_pred;  % tables in analytical re


        %% read data
        h = dbload(settings.histcore_name);
        h = changedata_FB(h,SS);
        dbmm = histdata_FB(h, settings);

        % remove forecast from database
        d = dbclip(dbmm,settings.hrng); 
        
        % remove forecast from database
        d = dbclip(d,settings.hrng);

        %% Run the filtering step
        dtunes = get_tunes_FB(SS);

        dbfilter = filterhistory(m, d, dtunes, SS, settings.hrng);
        f = dbfilter.mean;
        dbf = f;

        %db_ejud1 = dbload(settings.adhoc_data);

        dbm = dbextend(d, fcastdata_FB(h, f, settings, SS));

        % merge databases
        dbp = dbextend(dbm, dbf);


        % load model_g3;
        SS.filtering = false;

        %% remove structural shocks over forecasting horizon
        list = get(m,'enames');
        for i = 1:length(list);
            dbp.(list{i})(settings.comprng) = zeros(size(settings.comprng));
        end

        %% Expectation scheme, IMPOSE expert JUDGEMENT
        % load expectations scheme
        try
            expect_scheme = expDesign(m,settings.expectations_scheme);
        catch
            msgbox('Expectation scheme not defined!!!','Expectations scheme error','error');
            disp('Expectation scheme not defined.');
        end

        %--imposing NTF Exchange Rate outlook--% NOTE:

        %dbp_realimag = dbextend(dbp_realimag, db_ejud);

        %% MAKE THE PLAN


          fcast_plan   = plan(m, settings.comprng);
            % fixing expected outlooks
            % fixes are primarily taken from histcore(through changedata)
            % if you want to change some fix, do it in adhocdata, otherwise IT WOULD NOT BE REPORTED!!!!!!

        %     fcast_plan  = exogenize( fcast_plan, 'usdeur',           settings.pstarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_USDEUR',       settings.pstarrng);
        %     
        %     fcast_plan  = exogenize( fcast_plan, 'dot_p_BrentUSD_tilde',       settings.pstarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_p_BrentUSD_tilde',       settings.pstarrng);
        %     
        %     fcast_plan  = exogenize( fcast_plan, 'dot_pstar_other_tilde',         settings.pstarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_pstar_other_tilde',               settings.pstarrng);
        %     
        %     fcast_plan  = exogenize( fcast_plan, 'dot_pstar_tilde' ,             settings.pstarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_p_ener_ex_oil_tilde',       settings.pstarrng);
        %     
        %     fcast_plan  = exogenize( fcast_plan, 'dot_cpi_star_tilde',            settings.pstarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_dot_cpi_star_tilde',        settings.pstarrng);
        %        
        %     fcast_plan  = exogenize( fcast_plan, 'y_star_gap',       settings.nstarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_y_star_gap',        settings.nstarrng);
        %
        %     fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend',        settings.nstarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_y_star_trend',        settings.nstarrng);
        %
        %     fcast_plan  = exogenize( fcast_plan, 'i_star',              settings.istarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_shadow_rate',     settings.istarrng);
        %     
        % 	  fcast_plan  = exogenize( fcast_plan, 'i_star_eq',           settings.istarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_i_star_eq',       settings.istarrng);       
        %     
        %     fcast_plan  = exogenize( fcast_plan, 'i_star_eu',           settings.istarrng);
        %     fcast_plan  = endogenize(fcast_plan, 'eps_Istar',           settings.istarrng);


        %% Simulation
        tic
        [dbfcast,~,~,~,expectations] = simulate(m, dbp, settings.comprng, 'plan', fcast_plan, ...
            'expectations',expect_scheme);
        % [dbfcast] = simulate(m, dbp, settings.comprng, 'plan', fcast_plan);
        disp('simulate time:');
        toc
        if isequal(exist('expectations','var'),1)
            disp(expectations);
        end


        %% Create databases
        d = dbextend(f,dbfcast);
        % add out of model core variables
        d		= make_outofcore_FB(d, SS, settings);
        % make transformations
        d_model = make_transformations_FB(d, SS, false);
        % add reporting layer of data
        dd     = make_transformations_present_FB(d_model, h, settings, SS);

        dd_final{iii} = dd;

        clear d d_model dd dbmm h fcast_plan dbfcast dbp dbm dbf f dbfilter dtunes;
    end;

    save([settings.outdata_dir '\' settings.report_prefix '-hair-fb.mat'],'dd_final');

end;


if compare_two_fb==1
    plot_to = 4:70; %1 (eval from 2Q 2000) 4 (eval from 1Q 2001) 70 (eval to 4Q 2019) 78 (eval to 4Q 2021)

        hair_new = load([settings.outdata_dir '\' settings.report_prefix '-hair-fb.mat']); label_new = 'new ';
        
        hair_old = load([settings.outdata_dir '\' ID_hair_old '-hair-fb.mat']); label_old = 'old ';
        

    if (plot_to(end)<78)
        plot_simul_rng = qq(2001,1):qq(2019,4);
    else
        plot_simul_rng = qq(2001,1):settings.ehist;
    end

    figure;
    for jj = plot_to

        subplot(2,2,1)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_i_star_eu hair_old.dd_final{jj}.zz_i_star_eu hair_old.dd_final{end}.ne_zz_i_star_eu]); title(comment(hair_new.dd_final{jj}.i_star)); axis tight;
        stat_zz_i_star_eu_new(jj) = make_stat(hair_new.dd_final{jj}.zz_i_star_eu, hair_new.dd_final{end}.ne_zz_i_star_eu);
        stat_zz_i_star_eu_g3(jj) = make_stat(hair_old.dd_final{jj}.zz_i_star_eu, hair_old.dd_final{end}.ne_zz_i_star_eu);
        legend(sprintf('%s%0.3g',label_new,sum(stat_zz_i_star_eu_new)),sprintf('%s%0.3g',label_old,sum(stat_zz_i_star_eu_g3)),'Location','SouthWest','Orientation','Vertical');

        subplot(2,2,2)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_dot_pstar_tilde4 hair_old.dd_final{jj}.zz_dot_pstar_tilde4 hair_old.dd_final{end}.ne_zz_dot_pstar_tilde4]); title(comment(hair_new.dd_final{jj}.dot_pstar_tilde4)); axis tight;
        stat_zz_dot_pstar_tilde4_new(jj) = make_stat(hair_new.dd_final{jj}.zz_dot_pstar_tilde4, hair_new.dd_final{end}.ne_zz_dot_pstar_tilde4);
        stat_zz_dot_pstar_tilde4_g3(jj) = make_stat(hair_old.dd_final{jj}.zz_dot_pstar_tilde4, hair_old.dd_final{end}.ne_zz_dot_pstar_tilde4);
        legend(sprintf('%s%0.3g','',sum(stat_zz_dot_pstar_tilde4_new)),sprintf('%s%0.3g','',sum(stat_zz_dot_pstar_tilde4_g3)),'Location','SouthWest','Orientation','Vertical');

        subplot(2,2,3)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_dot_pstar_other_tilde4 hair_old.dd_final{jj}.zz_dot_pstar_other_tilde4 hair_old.dd_final{end}.ne_zz_dot_pstar_other_tilde4]); title(comment(hair_new.dd_final{jj}.dot_pstar_other_tilde4)); axis tight;
        stat_zz_dot_pstar_other_tilde4_new(jj) = make_stat(hair_new.dd_final{jj}.zz_dot_pstar_other_tilde4, hair_new.dd_final{end}.ne_zz_dot_pstar_other_tilde4);
        stat_zz_dot_pstar_other_tilde4_g3(jj) = make_stat(hair_old.dd_final{jj}.zz_dot_pstar_other_tilde4, hair_old.dd_final{end}.ne_zz_dot_pstar_other_tilde4);
        legend(sprintf('%s%0.3g','',sum(stat_zz_dot_pstar_other_tilde4_new)),sprintf('%s%0.3g','',sum(stat_zz_dot_pstar_other_tilde4_g3)),'Location','SouthWest','Orientation','Vertical');

        subplot(2,2,4)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_dot_y_star4 hair_old.dd_final{jj}.zz_dot_y_star4 hair_old.dd_final{end}.ne_zz_dot_y_star4]); title(comment(hair_new.dd_final{jj}.dot_y_star4)); axis tight;
        stat_zz_dot_y_star4_new(jj) = make_stat(hair_new.dd_final{jj}.zz_dot_y_star4, hair_new.dd_final{end}.ne_zz_dot_y_star4);
        stat_zz_dot_y_star4_g3(jj) = make_stat(hair_old.dd_final{jj}.zz_dot_y_star4, hair_old.dd_final{end}.ne_zz_dot_y_star4);
        legend(sprintf('%s%0.3g','',sum(stat_zz_dot_y_star4_new)),sprintf('%s%0.3g','',sum(stat_zz_dot_y_star4_g3)),'Location','SouthWest','Orientation','Vertical');

    end
    
    
    figure;
    for jj = plot_to

        subplot(3,2,1)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_dot_usdeur hair_old.dd_final{jj}.zz_dot_usdeur hair_old.dd_final{end}.zz_dot_usdeur]); title(comment(hair_new.dd_final{jj}.dot_usdeur)); axis tight;
        stat_zz_dot_usdeur_new(jj) = make_stat(hair_new.dd_final{jj}.zz_dot_usdeur, hair_new.dd_final{end}.ne_zz_dot_usdeur);
        stat_zz_dot_usdeur_g3(jj) = make_stat(hair_old.dd_final{jj}.zz_dot_usdeur, hair_old.dd_final{end}.ne_zz_dot_usdeur);
        legend(sprintf('%s%0.3g',label_new,sum(stat_zz_dot_usdeur_new)),sprintf('%s%0.3g',label_old,sum(stat_zz_dot_usdeur_g3)),'Location','SouthWest','Orientation','Vertical');

        subplot(3,2,2)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_z_gap hair_old.dd_final{jj}.zz_z_gap]); title(comment(hair_new.dd_final{jj}.z_gap)); axis tight;
        
        subplot(3,2,3)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_dot_pstar_RP_tilde hair_old.dd_final{jj}.zz_dot_pstar_RP_tilde]); title(comment(hair_new.dd_final{jj}.dot_pstar_RP_tilde)); axis tight;

        subplot(3,2,4)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_r_star_gap hair_old.dd_final{jj}.zz_r_star_gap]); title(comment(hair_new.dd_final{jj}.r_star_gap)); axis tight;
     
        subplot(3,2,5)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_dot_y_star_trend hair_old.dd_final{jj}.zz_dot_y_star_trend]); title(comment(hair_new.dd_final{jj}.dot_y_star_trend)); axis tight;
        
        subplot(3,2,6)
        hold on;
        plot(plot_simul_rng,[hair_new.dd_final{jj}.zz_y_star_gap hair_old.dd_final{jj}.zz_y_star_gap]); title(comment(hair_new.dd_final{jj}.y_star_gap)); axis tight;

    end
    
    
end
