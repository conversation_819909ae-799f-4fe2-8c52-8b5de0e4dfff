%% Impulse-response functions

% This driver is written for generating IRFs for FB only of the new g3++ model. It does not work for g3+. 
% 
% - ID - paste current sz name
% - doReport - paste 1 if the report shall be produced, alternatively matlab graphs can be produced
% - shockType - select type of shocks that shall be used for generating IRFs 
% - bible - paste 1 if all variables shall be examined (paste zero and select only some of the variables in responseList below otherwise)
% - In %% Set models part, you may add another g3++ model (e.g. with different parameters setup) for comparison 
% @ Author: TP, 2023/5

clear all; close all;
disp([sprintf('\n'), 'DRIVER_IRF']);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Load input
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

ID          = 'model_november_free';	% name of the new FB sz (all - forecast, filter)
ext			= '';			% choose simulation type (MUST BE DEFINED IN SR_OPT!): 

% load sr_opt
settings.new  = load_settings_june(ID,'extend',ext);
settings.new  = load_report_options(settings.new);

settings.doGraphs			 = 0;           % Draw Matlab Graphs - yes/no
settings.doPPT               = 0;           % Export Graphs to PPT - yes/no (doGraphs required)
settings.doReport			 = 1;           % Create report file - yes/no (set format in sr_options)
settings.doSaving			 = 0;           % Export results to CSV - yes/no

settings.responseLength	 = 40;			% Length of responses (# of quarters)
settings.hitPeriod		 = [5];   		% Shock(s) hit in this periods (matrix), e.g. [1:4 7]
settings.varTransform	 = 1;			% Transformations of variables (zz_) on/off - TODO: DOKONCIT!!!
settings.varnamedisp	 = 0;			% 1 - display variable names, 0 - display comments

shockType				 = 'b';	        % Expected (scheme) 'e': expected shocks based on a scheme defined by expDesignDefault
                                        % Expected (fully) 'ef': fully expected shocks (no scheme)
                                        % Unexpected 'u': unexpected shocks
                                        % Both 'b': a combination of 'e' and 'u' cases (if expected shock defined in scheme, then the scheme is followed; otherwise fully expected) 

settings.shocksize         = 1;           % 0: one std of shock, true model value, for expected usually 0
                                        % 1: one std of shock, expected shock size is taken from unexpected
                                        % 2: 0.025                                        
                                        
settings.formatSubplot	 = [5,4];		% e.g. [2,3] - 2 rows, 3 columns;
settings.lgndInSubplot	 = 1;			% adds legend in this subplot

settings.one_graph_compare = 1;           % 1 - comparison of different models' IRFs in one graph, 0 - draw IRFs for each model in separate graphs
settings.bible             = 1;           % 1 - create graphs for bible (i.e. expected and unexpected in one graph), works only for g3+ model and for option 'b'
                                        % bible has its own response selection


% names of shock or 'all' for all shocks
    shockList = {...
                'all'
%                 'eps_y_star_gap'
%                 'eps_dot_y_star_trend_fund'
%                 'eps_dot_y_star_trend_shift'
%                 'eps_Istar'
%                 'eps_i_star_eq'
%                 'eps_shadow_rate'
%                 'eps_USDEUR'
%                 'eps_dot_z_eq'
%                 'eps_prem_usdeur'
%                 'eps_pstar_other_tilde'
%                 'eps_pstar_tilde'
%                 'eps_pstar_energy_tilde'
%                 'eps_pstar_RP_tilde'
%                 'eps_dot_cpi_star_tilde'
%                 'eps_energy_share_ppi_star_gap '
                };

    
% names of responses or 'all' for all responses, 
% NOTE: option bible has its % own responseList (see below)
if settings.bible ~= 1
    responseList = {...
% %         'all'
%         'y_star_gap'
%         'dot_y_star_trend_fund'
%         'dot_y_star_trend'
%         'dot_y_star'
%         'dot_y_star_trend_shift'
%         'i_star'
%         'i_star_eq'
%         'i_star_eu'
%         'r_star_gap'
%         'shadow_rate_gap'
%         'dot_pstar_other_tilde'
%         'dot_pstar_energy_tilde'
%         'dot_pstar_tilde'
%         'dot_cpi_star_tilde'
%         'dot_pstar_RP_tilde'
%         'energy_share_ppi_star_gap'
%         'dot_usdeur'
%         'usdeur'
%         'z_gap'
%         'prem_usdeur'
%         'dot_z_eq'
        };
elseif settings.bible == 1  % Do not change!
    responseList = {...
        'y_star_gap'
        'dot_y_star_trend_fund'
        'dot_y_star_trend'
        'dot_y_star'
        'dot_y_star_trend_shift'
        'i_star'
        'i_star_eq'
        'i_star_eu'
        'r_star_gap'
        'shadow_rate_gap'
        'dot_pstar_other_tilde'
        'dot_pstar_energy_tilde'
        'dot_pstar_tilde'
        'dot_cpi_star_tilde'
        'dot_pstar_RP_tilde'
        'energy_share_ppi_star_gap'
        'dot_usdeur'
        'usdeur'
        'z_gap'
        'prem_usdeur'
        'dot_z_eq'
        };
end
           
%% Set models

% Models already prepared in solved model structure format (including
% path!), and model nickname
% NOTE: model nickname cannot include signs when exporting data to .csv (e.g. g3+) 
inputList = {...
% 	MODEL PREFIX						NICKNAME
%	ID									legend
    'model_november_free'                'g3++ november'
%     'model_november_usdeur'                'usdeur'
%    'FB_may'                'model_may'
	};
         
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Run impulse responses
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

[irf,m] = impulse_responses_FB(inputList,shockList,responseList,shockType,settings);
                    
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
