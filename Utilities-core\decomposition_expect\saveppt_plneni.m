%% SAVEPPT saves plots to PowerPoint.
function saveppt_plneni(filespec,stitle,stext,filetype)
%
% saveppt  function for saving actual graph to ppt file, if the file exist it would add new file at the end.
%
% Syntax:
%   saveppt('file1.ppt')
%   saveppt('file1.ppt','title of the slide','text below the picture','jpg')
% Arguments:
%   filespec string - file name with absolute path;
%   stitle,stext string;
%   filetype string of valid filetype (ai,bmp,emf,eps,fig,jpg,pbm,pcx,pdf,pgm,png,ppm,tif

% keyboard;
% Establish valid file name:
if nargin<1 | isempty(filespec);
    [fname, fpath] = uiputfile('*.pptx');
    if fpath == 0; return; end
    filespec = fullfile(fpath,fname);
else
    [fpath,fname,fext] = fileparts(filespec);
    if isempty(fpath); fpath = pwd; end
    if isempty(fext); fext = '.pptx'; end
    filespec = fullfile(fpath,[fname,fext]);
end

% Default slide title:
if nargin<2
    stitle = '';
end

% Default text:
if nargin<3
    stext  = '';
end


% Start an ActiveX session with PowerPoint:
ppt = actxserver('PowerPoint.Application');
ppt.visible = 1;

% Capture current figure/model into clipboard:
if nargin<4
    %print -dmeta -painters -r0
    saveas(gcf,'pic1.emf','emf');
    pic1_file=[pwd '\pic1.emf'];
else
    saveas(gcf,'pic1',filetype);
    pic1_file=[pwd '\pic1.' filetype];
end

if ~exist(filespec,'file');
    % Create new presentation:
    op = invoke(ppt.Presentations,'Add');
else
    % Open existing presentation:
    op = invoke(ppt.Presentations,'Open',filespec,[],[],1);% 0 znamena, ze neni file videt
end

% Get current number of slides:
slide_count = get(op.Slides,'Count');

% Add a new slide (with title object):
slide_count = int32(double(slide_count)+1);
new_slide = invoke(op.Slides,'Add',slide_count,11);

% Create title for slide:
if length(stitle)>0
    set(new_slide.Shapes.Title.TextFrame.TextRange,'Text',stitle);
    %     set(new_slide.Shapes.Title.TextFrame.TextRange.Font,'Size',28);
    %     set(new_slide.Shapes.Title.TextFrame.TextRange.ParagraphFormat,'Alignment','ppAlignLeft');
end

% Get height and width of slide:
slide_H = op.PageSetup.SlideHeight;
slide_W = op.PageSetup.SlideWidth;

% keyboard;
% Insert text into the text field:
if length(stext)>0
    addtext = invoke(new_slide.Shapes,'AddTextbox',1,single(0.04*slide_W), single(0.8*slide_H), single(0.9213*slide_W), single(0.9*slide_H));
    set(addtext.TextFrame.TextRange,'Text',stext);
    set(addtext.TextFrame.TextRange.Font,'Size',20);
    addtext.TextFrame.TextRange.font.Name = 'Arial Narrow';
    addtext.TextFrame.TextRange.font.Bold=0;
    
    set(addtext.TextFrame.TextRange.ParagraphFormat.Bullet,'Type','ppBulletUnnumbered');
    set(addtext.textFrame.TextRange.paragraphFormat.Bullet,'RelativeSize',1.2);
    set(addtext.textFrame.TextRange.paragraphFormat.Bullet.Font.Color,'RGB',30*256^2+43*256+213);%vysvetleni viz o radek niz
    % decimal form
    % R*256^2+G*256+B, zde je ale format RBG, a ne RGB
    % takze B*256^2+G*256+R nakonec...!!!!!
end

% keyboard;

% Insert the file:
% pic1 = invoke(new_slide.Shapes,'AddPicture',pic1_file,'msoFalse','msoTrue',0,single(double(0.3*slide_H)),single(double(slide_W)),single(double(0.7*slide_H))); %'left, top, width, height
pic1 = invoke(new_slide.Shapes,'AddPicture',pic1_file,'msoFalse','msoTrue',0,single(double(0.103*slide_H)),single(double(slide_W)),single(double(0.7*slide_H))); %'left, top, width, height
delete(pic1_file);

% set height and width of picture:
% set(pic1,'LockAspectRatio',0)
% pic_W = set(pic1,'Width',single(double(slide_W)));
% pic_H = set(pic1,'Height',single(double(0.7*slide_H)));

% Center picture on page (below title area):
% set(pic1,'Left',0);
% set(pic1,'Top',single(double(0.2*slide_H)));

if ~exist(filespec,'file')
    % Save file as new:
    invoke(op,'SaveAs',filespec,11);
else
    % Save existing file:
    try
        invoke(op,'Save');
    catch
        keyboard;
    end
end

% Close the presentation window:
invoke(op,'Close');

% Quit PowerPoint
invoke(ppt,'Quit');

return;