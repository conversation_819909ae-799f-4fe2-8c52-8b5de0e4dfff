%-----------report-data--------------------%
function dbout = book_fcast_ZB(d, m, settings, heading, basename)
disp(['Creating ' heading]);

rng_t = settings.ehist-1:settings.end_pred; % tables range
rng_g = settings.shist+80:settings.end_pred_long; % Graphs range
rng_m = settings.shist+80:settings.ehist; % Measurement error's range, see line 320
sdate = settings.start_pred;

p = get(m,'parameters');
eList = dbnames(d,'nameFilter','^eps_[^exp]\w*');
eList_exp = dbnames(d,'nameFilter','^eps_exp_\w*');
omegaList = dbnames(d,'nameFilter','^omega_\w*');

%% list from tables - Please keep it consistent with tables
list = {'zz_dot_pstar_tilde', 'zz_n_star', 'zz_i_star', 'zz_r_star', ...
        'ne_zz_dot:pstar_tilde', 'ne_zz_n_star', 'ne_zz_i_star', 'ne_zz_r_star',...
        };

dbout = dbbatch(d, '$0', 'resize(d.$0, rng_t);', 'namelist', list);
dbout = dbout*list;


%--open the report--%

x = report.new(heading);

%**************************************************************************

% %--Graph--%
sty = struct(); 
sty.line.lineWidth = 2;
sty.line.lineStyle = '-';
sty.line.Color = {[0 0 1]};
sty.highlight.faceColor = [0.8,0.8,0.8];
sty.legend.location = 'SouthOutside';
sty.legend.Orientation = 'Horizontal';
sty.axes.ylim=[ ...
                    '!! ylim = get(H,''ylim'');', ...
                    'k = 0.05*(ylim(2)-ylim(1));', ...
                    'SET = [ylim(1)-k,ylim(2)+k];'];
         


% post = [ ...
%     'pos = get(H,''position'');', ...
%     'k = 0.0962;', ...
%     'set(H,''position'',[pos(1),pos(2)-k,pos(3),pos(4)+k]);'];

post = [ ...
    'leg = findobj(gcf,''Location'',''SouthOutside'');'...
    'set(leg,''location'',''none'');',...
    'posleg = get(leg,''position'');', ...
    'pos = get(H,''position'');', ...
    'k = 0.0073;', ...
    'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
    'k = 0.062;', ...
    'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];

sty.line.Color = {[1 0 0],[0 0.5 0],[0 0 1],[0 1 1],[1 1 0],[1 0 1]};


%**************************************************************************
%% pages 1 - 7

% page 1
x.table('Forecast Summary','range',rng_t,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);

x.subheading('');
x.subheading('Inflation');
x.series(char(get(d.ne_zz_dot_pstar_tilde4,'comment')), d.ne_zz_dot_pstar_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.ne_zz_dot_pstar_tilde,'comment')), d.ne_zz_dot_pstar_tilde,'format','%.1f','units','%pa');
x.series('Inflation Target', tseries(rng_t,(m.dot_pstar_tilde^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_dot_pstar_RP_tilde4,'comment')), d.zz_dot_pstar_RP_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.zz_dot_pstar_RP_tilde,'comment')), d.zz_dot_pstar_RP_tilde,'format','%.1f','units','%pa');
x.series('Steady State', tseries(rng_t,(m.dot_pstar_RP_tilde^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.ne_zz_dot_pstar_other_tilde4,'comment')), d.ne_zz_dot_pstar_other_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.ne_zz_dot_pstar_other_tilde,'comment')), d.ne_zz_dot_pstar_other_tilde,'format','%.1f','units','%pa');
x.series('Steady State', tseries(rng_t,(m.dot_pstar_other_tilde^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_dot_pstar_energy_tilde4,'comment')), d.zz_dot_pstar_energy_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.zz_dot_pstar_energy_tilde,'comment')), d.zz_dot_pstar_energy_tilde,'format','%.1f','units','%pa');
x.series('Steady State', tseries(rng_t,(m.dot_pstar_energy_tilde^4-1)*100),'format','%.1f','units','%pa');

% x.subheading('');
% x.series(char(get(d.zz_pstar_energy_tilde_gap,'comment')), d.zz_pstar_energy_tilde_gap,'format','%.1f','units','%pa');
% x.series('Steady State', tseries(rng_t,(m.pstar_energy_tilde_gap^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_energy_share_ppi_star_gap,'comment')), d.zz_energy_share_ppi_star_gap,'format','%.1f','units','%pa');
x.series('F. Weight of Energy PPI', d.weight_dot_pstar_energy_tilde,'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_dot_cpi_star_tilde4,'comment')), d.zz_dot_cpi_star_tilde4,'format','%.1f','units','%pa');
x.series(char(get(d.zz_dot_cpi_star_tilde,'comment')), d.zz_dot_cpi_star_tilde,'format','%.1f','units','%pa');
x.series('Steady State', tseries(rng_t,(m.dot_cpi_star_tilde^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.subheading('Interest Rate');
x.series(char(get(d.ne_zz_i_star,'comment')), d.ne_zz_i_star,'format','%.1f','units','%pa');
x.series('Foreign Shadow Rate', d.ne_zz_i_star_shadow,'format','%.1f','units','%pa');
x.series('Steady State', tseries(rng_t,(m.i_star^4-1)*100),'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_r_star_gap,'comment')), d.zz_r_star_gap,'format','%.1f','units','%pa');

% page 2
x.pagebreak();

x.table('Forecast Summary','range',rng_t,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);


x.subheading('');
x.subheading('Exchange Rate');
x.series(char(get(d.ne_zz_usdeur,'comment')), d.ne_zz_usdeur,'format','%.2f');
x.series('Steady State', tseries(rng_t,m.usdeur),'format','%.2f');

x.subheading('');
x.series(char(get(d.ne_zz_dot_usdeur4,'comment')), d.ne_zz_dot_usdeur4,'format','%.1f','units','%pa');
x.series(char(get(d.ne_zz_dot_usdeur,'comment')), d.ne_zz_dot_usdeur,'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_dot_z_eq4,'comment')), d.zz_dot_z_eq4,'format','%.1f','units','%pa');
x.series(char(get(d.zz_dot_z_eq,'comment')), d.zz_dot_z_eq,'format','%.1f','units','%pa');
x.series(char(get(d.zz_z_gap,'comment')), d.zz_z_gap,'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.zz_prem_usdeur,'comment')), d.zz_prem_usdeur,'format','%.1f','units','%pa');

x.subheading('');
x.subheading('Real Sector Variables');
x.series([char(get(d.zz_dot_y_star_trend4,'comment')) ' g3+'], d.zz_dot_y_star_trend4,'format','%.1f','units','%pa');
x.series([char(get(d.zz_dot_y_star_trend,'comment')) ' g3+'], d.zz_dot_y_star_trend,'format','%.1f','units','%pa');
% x.series(char(get(d.ne_zz_dot_y_star_trend4,'comment')), d.ne_zz_dot_y_star_trend4,'format','%.1f','units','%pa');
x.series([char(get(d.zz_dot_y_star_trend_fund4,'comment')) ' g3+'], d.zz_dot_y_star_trend_fund4,'format','%.1f','units','%pa');
% x.series(char(get(d.ne_zz_dot_y_star_trend,'comment')), d.ne_zz_dot_y_star_trend,'format','%.1f','units','%pa');
x.series([char(get(d.zz_dot_y_star_trend_fund,'comment')) ' g3+'], d.zz_dot_y_star_trend_fund,'format','%.1f','units','%pa');
x.series([char(get(d.zz_dot_y_star_trend_shift4,'comment')) ' g3+'], d.zz_dot_y_star_trend_shift4,'format','%.1f','units','%pa');
x.series([char(get(d.zz_dot_y_star_trend_shift,'comment')) ' g3+'], d.zz_dot_y_star_trend_shift,'format','%.1f','units','%pa');

x.subheading('');
% x.series(char(get(d.ne_zz_y_star_gap,'comment')), d.ne_zz_y_star_gap,'format','%.1f','units','%pa');
x.series([char(get(d.zz_y_star_gap,'comment')) ' g3+'], d.zz_y_star_gap,'format','%.1f','units','%pa');

x.subheading('');
x.series(char(get(d.ne_zz_dot_y_star,'comment')), d.ne_zz_dot_y_star,'format','%.1f','units','%pa');
x.series([char(get(d.zz_dot_y_star,'comment')) ' g3+'], d.zz_dot_y_star,'format','%.1f','units','%pa');
x.series('Steady State', tseries(rng_t,(m.dot_y_star^4-1)*100),'format','%.1f','units','%pa');


% page 3

x.pagebreak();

x.figure('Summary','range', rng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',rng_g(1):8  :rng_g(end),'zeroline',true);
 x.graph(get(d.zz_dot_pstar_tilde, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_tilde_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_pstar_tilde);
    x.series('Model',d.zz_dot_pstar_tilde);   
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_pstar_other_tilde, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_other_tilde_ss^4-1)*100); 
    x.series('Data',d.ne_zz_dot_pstar_other_tilde);
    x.series('Model',d.zz_dot_pstar_other_tilde);   
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_pstar_energy_tilde, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_energy_tilde_ss^4-1)*100);    
    x.series('Data',d.ne_zz_dot_pstar_energy_tilde);  
    x.series('Model',d.zz_dot_pstar_energy_tilde);
   x.highlight('',sdate:rng_g(end));
%  x.graph(get(d.zz_pstar_energy_tilde_gap, 'comment'),'legend',true,'postprocess',post,'style',sty );
%    x.series('Model',d.zz_pstar_energy_tilde_gap);
%    x.series('SS',tseries(rng_g,0));
%    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_pstar_RP_tilde, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_RP_tilde_ss^4-1)*100);
    x.series('Model',d.zz_dot_pstar_RP_tilde);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_cpi_star_tilde, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_cpi_star_tilde_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_cpi_star_tilde);
    x.series('Model',d.zz_dot_cpi_star_tilde); 
    x.highlight('',sdate:rng_g(end));

% page 4    
x.pagebreak();
 
x.figure('Summary','range', rng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',rng_g(1):8:rng_g(end),'zeroline',true);  
 x.graph(get(d.ne_zz_i_star, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.i_star_ss^4-1)*100);
    x.series('Data',d.ne_zz_i_star);
    x.series('Shadow',d.ne_zz_i_star_shadow);
    x.series('Model',d.zz_i_star);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_r_star_gap, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',tseries(rng_g,0)); 
    x.series('Model',d.zz_r_star_gap);  
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_y_star_trend_fund, 'comment'),'legend',true,'postprocess',post,'style',sty );
%    x.series('Data',d.ne_zz_dot_y_star_trend);
    x.series('SS',(d.dot_y_star_trend_ss^4-1)*100);
    x.series('Model',d.zz_dot_y_star_trend_fund); 
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_y_star_trend, 'comment'),'legend',true,'postprocess',post,'style',sty );
   x.series('Total',d.zz_dot_y_star_trend);
   x.series('One-off shift',d.zz_dot_y_star_trend_shift);
   x.series('SS',(d.dot_y_star_trend_ss^4-1)*100);
   x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_y_star_gap, 'comment'),'legend',true,'postprocess',post,'style',sty );
%    x.series('Data',d.ne_zz_y_star_gap);
    x.series('SS',tseries(rng_g,0));
    x.series('Model',d.zz_y_star_gap);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_y_star, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_y_star_ss^4-1)*100);  
    x.series('Data',d.ne_zz_dot_y_star);
    x.series('Model',d.zz_dot_y_star);    
    x.highlight('',sdate:rng_g(end));

% page 5
x.pagebreak();

x.figure('Summary','range', rng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',rng_g(1):8:rng_g(end),'zeroline',true);
 x.graph(get(d.ne_zz_usdeur, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',d.usdeur_ss);
    x.series('Data',d.ne_zz_usdeur); 
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.ne_zz_dot_usdeur, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_usdeur_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_usdeur);  
    x.series('Model',d.zz_dot_usdeur);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_z_gap, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',tseries(rng_g,0));  
    x.series('Model',d.zz_z_gap);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_z_eq, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_z_eq_ss^4-1)*100);
    x.series('Model',d.zz_dot_z_eq);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_prem_usdeur, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',tseries(rng_g,0));  
    x.series('Model',d.zz_prem_usdeur);
    x.highlight('',sdate:rng_g(end));

% page 6   
x.pagebreak();

x.figure('Summary','range', rng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',rng_g(1):8:rng_g(end),'zeroline',true);
 x.graph(get(d.zz_dot_pstar_tilde4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_tilde_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_pstar_tilde4);
    x.series('Model',d.zz_dot_pstar_tilde4);  
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_pstar_other_tilde4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_other_tilde_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_pstar_other_tilde4);
    x.series('Model',d.zz_dot_pstar_other_tilde4);  
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_pstar_energy_tilde4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_energy_tilde_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_pstar_energy_tilde4);  
    x.series('Model',d.zz_dot_pstar_energy_tilde4);  
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_pstar_RP_tilde4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_pstar_RP_tilde_ss^4-1)*100);
    x.series('Model',d.zz_dot_pstar_RP_tilde4);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_cpi_star_tilde4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_cpi_star_tilde_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_cpi_star_tilde4);
    x.series('Model',d.zz_dot_cpi_star_tilde4);   
   x.highlight('',sdate:rng_g(end));

% page 7  
x.pagebreak();

x.figure('Summary','range', rng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',rng_g(1):8:rng_g(end),'zeroLine=',true);
    
 x.graph(get(d.zz_energy_share_ppi_star_gap, 'comment'),'legend',true,'postprocess',post,'style',sty,'zeroLine=',true);
    x.series('SS',tseries(rng_g,0));   
    x.series('Model',d.zz_energy_share_ppi_star_gap);
    x.highlight('',settings.start_pred:rng_g(end)); 
 x.graph('F. Weight of Energy PPI','legend',true,'postprocess',post,'style',sty,'zeroLine=',true); 
    x.series('SS',tseries(rng_g,d.energy_share_ppi_star));
    x.series('Data',d.weight_dot_pstar_energy_tilde);  
    x.highlight('',settings.start_pred:rng_g(end));
   
% page 8
x.pagebreak();

x.figure('Summary','range', rng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',rng_g(1):8:rng_g(end),'zeroline',true);
 x.graph(get(d.zz_dot_y_star_trend_fund4, 'comment'),'legend',true,'postprocess',post,'style',sty );
%    x.series('Data',d.ne_zz_dot_y_star_trend4);
    x.series('SS',(d.dot_y_star_trend_ss^4-1)*100);  
    x.series('Model',d.zz_dot_y_star_trend_fund4);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_y_star_trend4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_y_star_trend_ss^4-1)*100);
    x.series('Total',d.zz_dot_y_star_trend4);
    x.series('One-off shift',d.zz_dot_y_star_trend_shift4);  
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_y_star4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_y_star_ss^4-1)*100); 
    x.series('Data',d.ne_zz_dot_y_star4);
    x.series('Model',d.zz_dot_y_star4);
    x.highlight('',sdate:rng_g(end));
x.graph(get(d.ne_zz_y_star, 'comment'),'legend',true,'postprocess',post,'style',sty );
   x.series('Data',d.ne_zz_y_star);
   x.series('Trend total',d.zz_y_star_trend);  
   x.highlight('',sdate:rng_g(end));
 x.graph(get(d.ne_zz_dot_usdeur4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_usdeur_ss^4-1)*100);
    x.series('Data',d.ne_zz_dot_usdeur4);  
    x.series('Model',d.zz_dot_usdeur4);
    x.highlight('',sdate:rng_g(end));
 x.graph(get(d.zz_dot_z_eq4, 'comment'),'legend',true,'postprocess',post,'style',sty );
    x.series('SS',(d.dot_z_eq_ss^4-1)*100);   
    x.series('Model',d.zz_dot_z_eq4);
    x.highlight('',sdate:rng_g(end));
   
%% Residuals

%unexpected
for ix = 1 : length(eList)
	if mod(ix,9)==1
		x.figure('Residuals - Exp. scheme','range', rng_g, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',rng_g(1):12:rng_g(end));
	end
	x.graph(eList{ix},'style',sty,'zeroLine=',true);
	x.series(get(d.(['zz_' eList{ix}]), 'comment'),d.(['zz_' eList{ix}]));
end
%expected
for ix = 1 : length(eList_exp)
	if mod(ix,9)==1
		x.figure('Residuals - Expected','range', rng_g, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',rng_g(1):12:rng_g(end));
	end
	x.graph(eList_exp{ix},'style',sty,'zeroLine=',true);
	x.series(get(d.(['zz_' eList_exp{ix}]), 'comment'),d.(['zz_' eList_exp{ix}]));
end
%measurement
for ix = 1 : length(omegaList)
	if mod(ix,9)==1
		x.figure('Measurement errors','range', rng_m, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',rng_m(1):12:rng_m(end));
	end
	x.graph(omegaList{ix},'style',sty,'zeroLine=',true);
	x.series(get(d.(['zz_' omegaList{ix}]), 'comment'),d.(omegaList{ix}));
end


%% Contributions

stybar=struct();
stybar.figure.colormap=[0 0 0.95; 1 0 0; 1 1 0; 0 1 0; 1 0 1; 0 0.5 0.5; 0.9 0.7 0.1; 0 1 1];
stybar.line.lineWidth = {3,2};
stybar.line.Color = {[0.6 0.6 0.6],[0 0 0]};
stybar.legend.location = 'SouthOutside';
stybar.legend.Orientation = 'Horizontal';
%stybar.highlight.faceColor = [0.8,0.8,0.8];
stybar.highlight.faceColor = {[0.92,0.92,0.92],[0.85,0.85,0.85]}; % lighter grey
stybar.axes.xgrid = 'off';
stybar.axes.ylim=[ ...
                    '!! ylim = get(H,''ylim'');', ...
                    'k = 0.05*(ylim(2)-ylim(1));', ...
                    'SET = [ylim(1)-k,ylim(2)+k];'];

post = [ ...
    'leg = findobj(gcf,''Location'',''SouthOutside'');'...
    'set(leg,''location'',''none'');',...
    'posleg = get(leg,''position'');', ...
    'pos = get(H,''position'');', ...
    'k = 0.014;', ...
    'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
    'k = 0.057;', ...
    'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];

% page 13
x.pagebreak(); 

% dot_pstar_tilde decomposition - data view
x.figure('Observed Decomposition','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent','zeroLine=',true);
 x.graph(get(d.ne_zz_dot_pstar_tilde, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.ne_zz_dot_pstar_tilde_contrib_pstar_other_tilde d.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde], ...
	   'plotfunc',@conbar,'legend',{'Core','Energy'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('EA PPI',d.ne_zz_dot_pstar_tilde);
   x.series('Sum',[d.ne_zz_dot_pstar_tilde_contrib_pstar_other_tilde+d.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde]);  
   x.highlight('',sdate:rng_g(end), 'colour', [0.5 0.5 0.5]);
x.figure('','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent','zeroLine=',true);
 x.graph(get(d.zz_dot_pstar_tilde4, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.ne_zz_dot_pstar_tilde_contrib_pstar_other_tilde4 d.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde4], ...
	   'plotfunc',@conbar,'legend',{'Core','Energy'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('EA PPI', d.ne_zz_dot_pstar_tilde4); 
   x.series('Sum',[d.ne_zz_dot_pstar_tilde_contrib_pstar_other_tilde4+d.ne_zz_dot_pstar_tilde_contrib_pstar_energy_tilde4]);
   x.highlight('',sdate:rng_g(end));
   
% page 14
x.pagebreak();

% dot_pstar_tilde decomposition - model view
x.figure('Model Decomposition','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent','zeroLine=',true); 
 x.graph(get(d.zz_dot_pstar_tilde, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_tilde_contrib_nonener d.zz_dot_pstar_tilde_contrib_energy d.zz_dot_pstar_tilde_contrib_resid], ...
	   'plotfunc',@conbar,'legend',{'Core','Energy','Residual'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('EA PPI',d.zz_dot_pstar_tilde);
   x.series('Sum',d.zz_dot_pstar_tilde_contrib_energy+d.zz_dot_pstar_tilde_contrib_nonener+d.zz_dot_pstar_tilde_contrib_resid);  
   x.highlight('',sdate:rng_g(end));
x.figure('','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent'); 
 x.graph(get(d.zz_dot_pstar_tilde4, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_tilde_contrib_nonener4 d.zz_dot_pstar_tilde_contrib_energy4 d.zz_dot_pstar_tilde_contrib_resid4], ...
	   'plotfunc',@conbar,'legend',{'Core','Energy','Residual'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('EA PPI', d.zz_dot_pstar_tilde4); 
   x.series('Sum',d.zz_dot_pstar_tilde_contrib_energy4+d.zz_dot_pstar_tilde_contrib_nonener4+d.zz_dot_pstar_tilde_contrib_resid4);
   x.highlight('',sdate:rng_g(end));
   
% page 15
x.pagebreak();

% dot_pstar_other_tilde decomposition
x.figure('Model Decomposition','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent','zeroLine=',true);  
 x.graph('EA Core PPI (EUR, QoQ)','legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_other_tilde_contrib_ogap d.zz_dot_pstar_other_tilde_contrib_lagdev d.zz_dot_pstar_other_tilde_contrib_expdev d.zz_dot_pstar_other_tilde_contrib_usdeur d.zz_dot_pstar_other_tilde_contrib_resid], ...                         
	   'plotfunc',@conbar,'legend',{'O. Gap','Lag','Expectations','EUR/USD','Shock'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model p other',d.zz_dot_pstar_other_tilde);
   x.series('Sum',d.zz_dot_pstar_other_tilde_contrib_ogap+d.zz_dot_pstar_other_tilde_contrib_lagdev+d.zz_dot_pstar_other_tilde_contrib_expdev+d.zz_dot_pstar_other_tilde_contrib_usdeur+d.zz_dot_pstar_other_tilde_contrib_resid); 
   x.highlight('',sdate:rng_g(end));
x.figure('','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent');  
 x.graph('EA Core PPI (EUR, YoY)','legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_pstar_other_tilde_contrib_ogap4 d.zz_dot_pstar_other_tilde_contrib_lagdev4 d.zz_dot_pstar_other_tilde_contrib_expdev4 d.zz_dot_pstar_other_tilde_contrib_usdeur4 d.zz_dot_pstar_other_tilde_contrib_resid4], ...
	   'plotfunc',@conbar,'legend',{'O. Gap','Lag','Expectations','EUR/USD','Shock'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('Model p other',d.zz_dot_pstar_other_tilde4);
   x.series('Sum',d.zz_dot_pstar_other_tilde_contrib_ogap4+d.zz_dot_pstar_other_tilde_contrib_lagdev4+d.zz_dot_pstar_other_tilde_contrib_expdev4+d.zz_dot_pstar_other_tilde_contrib_usdeur4+d.zz_dot_pstar_other_tilde_contrib_resid4);
   x.highlight('',sdate:rng_g(end));
   
% page 16   
x.pagebreak();

% GDP decomposition to trend and gap
x.figure('Model Decomposition','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ...
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent','zeroLine=',true);
 x.graph('Foreign Output Growth (QoQ)','legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_y_star_contrib_gap d.zz_dot_y_star_contrib_trend_fund d.zz_dot_y_star_contrib_trend_shift], ...
       'plotfunc',@conbar,'legend',{'O. Gap','Persistent Trend','One-Off Trend'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('EA Output Growth',d.zz_dot_y_star);
   x.series('Sum',d.zz_dot_y_star_contrib_gap+d.zz_dot_y_star_contrib_trend_fund+d.zz_dot_y_star_contrib_trend_shift);
   x.highlight('',sdate:rng_g(end)); 
x.figure('','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ...
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent');
 x.graph('Foreign Output Growth (YoY)','legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_y_star_contrib_gap4 d.zz_dot_y_star_contrib_trend_fund4 d.zz_dot_y_star_contrib_trend_shift4], ...
        'plotfunc',@conbar,'legend',{'O. Gap','Persistent Trend','One-Off Trend'},'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('EA Output Growth', d.zz_dot_y_star4); %,'legend',NaN);
   x.series('Sum',d.zz_dot_y_star_contrib_gap4+d.zz_dot_y_star_contrib_trend_fund4+d.zz_dot_y_star_contrib_trend_shift4);
   x.highlight('',sdate:rng_g(end));

% page 17   
x.pagebreak();

% y_star_gap decomposition
x.figure('Model Decomposition','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent','zeroLine=',true);   
 x.graph([char(get(d.zz_y_star_gap, 'comment')) ', %'],'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_y_star_gap_contrib_lag d.zz_y_star_gap_contrib_rmci d.zz_y_star_gap_contrib_usdeur d.zz_y_star_gap_contrib_resid], ...                                   
	   'plotfunc',@conbar,'legend',{'Lagged','RMCI','EUR/USD','Demand Shock'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('O. Gap',d.zz_y_star_gap);
   x.series('Sum',d.zz_y_star_gap_contrib_lag+d.zz_y_star_gap_contrib_rmci+d.zz_y_star_gap_contrib_usdeur+d.zz_y_star_gap_contrib_resid);         
   x.highlight('',sdate:rng_g(end));

 x.graph([char(get(d.zz_i_star_eu, 'comment')) ',% p.a'],'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_i_star_contrib_ss d.zz_i_star_contrib_smoothing d.zz_i_star_contrib_ogap d.zz_i_star_contrib_inflexpdev d.zz_i_star_contrib_resid], ...               
	   'plotfunc',@conbar,'legend',{'SS','Smoothing','O. Gap','E. Inflation','MP Shock'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('F. MP Rate',d.zz_i_star_eu); % change bcs of shadow rate
   x.series('Sum',d.zz_i_star_contrib_ogap+d.zz_i_star_contrib_inflexpdev+d.zz_i_star_contrib_smoothing+d.zz_i_star_contrib_ss+d.zz_i_star_contrib_resid);%d.zz_i_star_contrib_ogap+d.zz_i_star_contrib_inflexpdev+d.zz_i_star_contrib_smoothing+d.zz_i_star_contrib_ss+d.zz_i_star_contrib_resid);   
   x.highlight('',sdate:rng_g(end));
   
% page 18  
x.pagebreak();

% cpi_star_tilde decomposition   
x.figure('CPI Model Decomposition','range', rng_g, 'subplot',[2 1],'dateformat','YY:P', ... 
        'style', stybar,'datetick',rng_g(1):4:rng_g(end),'ordering','descent','zeroLine=',true);    
 x.graph(get(d.zz_dot_cpi_star_tilde, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_cpi_star_tilde_contrib_cpi_ss d.zz_dot_cpi_star_tilde_contrib_pstartilde  d.zz_dot_cpi_star_tilde_contrib_resid], ...
	   'plotfunc',@conbar,'legend',{'Inflation Target','PPI','CPI shock'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('F. CPI - model',d.zz_dot_cpi_star_tilde);
   x.series('Sum',d.zz_dot_cpi_star_tilde_contrib_pstartilde+d.zz_dot_cpi_star_tilde_contrib_cpi_ss+d.zz_dot_cpi_star_tilde_contrib_resid);   
   x.highlight('',sdate:rng_g(end));

 x.graph(get(d.zz_dot_cpi_star_tilde4, 'comment'),'legend',true,'postprocess',post, 'style', stybar);
   x.series('',[d.zz_dot_cpi_star_tilde_contrib_cpi_ss4 d.zz_dot_cpi_star_tilde_contrib_pstartilde4  d.zz_dot_cpi_star_tilde_contrib_resid4], ...
	   'plotfunc',@conbar,'legend',{'Inflation Target','PPI','CPI shock'}, ...
       'plotoptions',{'colormap',stybar.figure.colormap});
   x.series('F. CPI - model', d.zz_dot_cpi_star_tilde4); %log((d.dot_pstar_tilde-d.eps_pstar_tilde)*(d.dot_pstar_tilde{-1}-d.eps_pstar_tilde{-1})* ...
             %(d.dot_pstar_tilde{-2}-d.eps_pstar_tilde{-2})*(d.dot_pstar_tilde{-3}-d.eps_pstar_tilde{-3}))*100);
   x.series('Sum',d.zz_dot_cpi_star_tilde_contrib_pstartilde4+d.zz_dot_cpi_star_tilde_contrib_cpi_ss4+d.zz_dot_cpi_star_tilde_contrib_resid4);
   x.highlight('',sdate:rng_g(end));


%**************************************************************************

x.publish([basename '.pdf'],'paperSize','a4paper','display',false);
