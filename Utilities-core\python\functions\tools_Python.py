# -*- coding: utf-8 -*-
"""
Created on Wed Aug 19 12:48:54 2020

@author: U06541
@troubleshooting: <PERSON><PERSON>@cnb.cz
"""

import datetime as dt

# --------------------------------------------------------------------------- #

def run_sr_options(sz,
                   sz_old
                   ):
    
    '''
    This function creates various dates needed for plotting.
    
    Inputs of the function are:
        sz:         label of the current inflation report in the form of a string 
        sz_old:     label of the last inflation report in the form of a string
                    
    All inputs are obligatory!
    
    LIST OF DATES CREATED
    
    Dates for start:
        start_date:                  start date, modified in the summer forecasting round (IR no. 5)        ['YYYY-MM']
        start_date_long:             start date (longer history)                                            ['YYYY-MM']
        start_date_lev:              start date for level graphs                                            ['YYYY-MM']
        start_date_lev_long:         start date for level graphs (longer history)                           ['YYYY-MM']
        start_date_NTF:              start date for NTF projection                                          ['YYYY-MM']
        start_year_yy:               start date for yearly graphs                                           [YYYY]
        start_year_yy_q:             start date for yearly graphs (quarterly definition)                    ['YYYY-MM']
        start_date_decomp:           start date for decomposition                                           [dt.date(YYYY,M,D)]
        start_date_fan:              start date for fan charts                                              [dt.date(YYYY,M,D)]
                
    Dates for end:
        end_date:                    end date of forecast, modified in the summer forecast (IR no. 5)       ['YYYY-MM'] 
        end_date_NTF:                end date for NTF projection (1 quarter ahead)                          ['YYYY-MM'] 
        end_date_old:                range of the prevevious forecast                                       ['YYYY-MM'] 
        end_hist_date:               end date of history                                                    ['YYYY-MM'] 
        end_hist_date_tradeNonTrade: end date of history (tradable and non-tradable components)             ['YYYY-MM']
        end_hist_date_old:           end date of history (previous inflation report)                        ['YYYY-MM']
        end_hist_date_old2:          end date of history (previous inflation report, "gdp-style" graphs)    ['YYYY-MM']
        end_date_decomp:             end date for decomposition                                             ['YYYY-MM'] 
        end_date_fan:                end date for fan charts                                                [dt.date(YYYY,M,D)]
        
    Dates for shading:  
        ehist:                       end of history (current inflation report)                              ['DD.M.YYYY']                                                                         
        ehist_yy:                    end of history for yearly GDP graph                                    ['DD.M.YYYY'] 
        ehist_old:                   end of history (previous inflation report)                             ['DD.M.YYYY'] 
        ehist_old2:                  end of history (previous inflation report, "gdp-style" graphs)         ['DD.M.YYYY']
        
    Various:
        last_fiscal_year:            last year with complete fiscal data set ("actual" label in the tabel)  [YYYY]
    
    
    Example:
        run_sr_options('2020sz05','2020sz03')
        The function returns dates relevant for inflation reports 2020sz05 and 2020sz03.
        
    Aug 2020 Jan Zacek

    Lastly modified: March 2021 Jan Zacek     
    
    '''
    
    
    # Preliminary string modifications
    sz           = sz[:8]
    sz_parse     = sz.split('sz')
    sz_year      = sz_parse[0]
    sz_no        = sz_parse[1]
    sz_old       = sz_old[:8]
    sz_old_parse = sz_old.split('sz')
    sz_old_year  = sz_old_parse[0]
    sz_old_no    = sz_old_parse[1]
    
    # Years and months deductions
    if sz_no in ['01', '02']:
        years_back           = 5
        years_yy_fiscal      = 2
        years_end            = 1
        month_end_hist       = '12'
        month_start_gray     = '8'
        year_start_gray      = str(int(sz_year) - 1)
        year_end_hist        = str(int(sz_year) - 1)
        year_ehist_plneni    = str(int(sz_year) - 1) 
        year_start_date_NTF  = sz_year
        month_start_date_NTF = '1'
        month_end_date_plneni = 10
        year_ehist_yy        = str(int(sz_year) - 2)
        year_start_fan       = int(sz_year) - 3
        year_end_fan         = int(sz_year) + 1
        month_end_fan        = 9
    elif sz_no in ['03', '04']:
        years_back           = 5
        years_yy_fiscal      = 2
        years_end            = 1
        month_end_hist       = '3'
        month_start_gray     = '11'
        year_start_gray      = str(int(sz_year) - 1)
        year_end_hist        = sz_year
        year_ehist_plneni    = str(int(sz_year) - 1)  
        year_start_date_NTF  = sz_year
        month_start_date_NTF = '4'
        month_end_date_plneni = 1
        year_ehist_yy        = str(int(sz_year) - 1)
        year_start_fan       = int(sz_year) - 2
        year_end_fan         = int(sz_year) + 1
        month_end_fan        = 12
    elif sz_no in ['05', '06']:
        years_back           = 4
        years_yy_fiscal      = 1
        years_end            = 2
        month_end_hist       = '6'
        month_start_gray     = '2'
        year_start_gray      = str(int(sz_year))
        year_end_hist        = sz_year
        year_ehist_plneni    = str(int(sz_year) - 1) 
        year_start_date_NTF  = sz_year
        month_start_date_NTF = '7'
        month_end_date_plneni = 4
        year_ehist_yy        = str(int(sz_year) - 1)
        year_start_fan       = int(sz_year) - 2
        year_end_fan         = int(sz_year) + 2
        month_end_fan        = 3
    elif sz_no in ['07', '08']:
        years_back           = 4
        years_yy_fiscal      = 1
        years_end            = 2
        month_end_hist       = '9'
        month_start_gray     = '5'
        year_start_gray      = str(int(sz_year))
        year_end_hist        = sz_year
        year_ehist_plneni    = str(int(sz_year) - 1) 
        year_start_date_NTF  = sz_year
        month_start_date_NTF = '10'
        month_end_date_plneni = 7
        year_ehist_yy        = str(int(sz_year) - 1)
        year_start_fan       = int(sz_year) - 2
        year_end_fan         = int(sz_year) + 2                          
        month_end_fan        = 6    
    
    if sz_old_no in ['01', '02']:
        years_end_old        = 1
        year_end_hist_old    = str(int(sz_old_year) - 1)
        month_end_hist_old   = '12'
        month_end_hist_old2  = '9'
        year_end_hist_old2   = str(int(sz_old_year) - 1)
        month_start_date_plneni = 1
    elif sz_old_no in ['03', '04']:
        years_end_old        = 1
        year_end_hist_old    = sz_old_year
        month_end_hist_old   = '3'
        month_end_hist_old2  = '12'
        year_end_hist_old2   = str(int(sz_old_year) - 1)
        month_start_date_plneni = 4
    elif sz_old_no in ['05', '06']:
        years_end_old        = 2
        year_end_hist_old    = sz_old_year
        month_end_hist_old   = '6'
        month_end_hist_old2  = '3'
        year_end_hist_old2   = sz_old_year
        month_start_date_plneni = 7
    elif sz_old_no in ['07', '08']:
        years_end_old        = 2
        year_end_hist_old    = sz_old_year
        month_end_hist_old   = '9'
        month_end_hist_old2  = '6'
        year_end_hist_old2   = sz_old_year
        month_start_date_plneni = 10
    
    # Start dates
    start_date                  = str(int(sz_year) - years_back) + '-01'
    start_date_long             = str(int(sz_year) - years_back - 14) + '-01'
    start_date_lev              = str(int(sz_year) - years_back - 3) + '-01'
    start_date_lev_long         = str(int(sz_year) - years_back - 11) + '-01'                   
    start_date_NTF              = year_start_date_NTF + '-' + month_start_date_NTF.zfill(2)
    start_date_decomp           = dt.date(int(year_start_date_NTF),int(month_start_date_NTF),1)
    start_year_yy               = int(sz_year) - years_yy_fiscal
    start_year_yy_q             = str(int(start_year_yy)) + '-01'
    start_date_fan              = dt.date(int(year_start_fan),int(month_end_hist),1)
                                              
    # End dates
    end_date                    = str(int(sz_year) + years_end) + '-12'
    end_date_old                = str(int(sz_old_year) + years_end_old) + '-12'
    end_date_decomp             = end_date_old
    end_hist_date               = year_end_hist + '-' + month_end_hist.zfill(2)
    end_hist_date_tradeNonTrade = year_end_hist + '-' + str(int(month_end_hist) - 1).zfill(2)
    end_date_NTF                = year_start_date_NTF + '-' + str(int(month_start_date_NTF) + 2).zfill(2)
    end_hist_date_old           = year_end_hist_old + '-' + month_end_hist_old.zfill(2)
    end_hist_date_old2          = year_end_hist_old2 + '-' + month_end_hist_old2.zfill(2)
    end_date_fan                = dt.date(int(year_end_fan),int(month_end_fan),1)
    
    # Dates for shading
    ehist                       = '15.' + str(int(month_end_hist) - 1) + '.' + year_end_hist
    ehist_old                   = '15.' + str(int(month_end_hist_old) - 1) + '.' + year_end_hist_old
    ehist_old2                  = '15.' + str(int(month_end_hist_old2) - 1) + '.' + year_end_hist_old2
    ehist_yy                    = '30.6.' + year_ehist_yy
    ehist_mm                    = '15.' + str(int(month_end_hist) + 1) + '.' + year_end_hist 
    ehist_hdp                   = '15.' + str(int(month_start_gray)) + '.' + year_start_gray

    # Dates for plneni
    start_date_plneni           = str(int(sz_year) - years_back - 2) + '-01'
    decomp_plneni_start         = dt.date(int(sz_old_year),month_start_date_plneni,1)  
    decomp_plneniSS_start       = str(int(year_end_hist_old2)) + '-' + str(int(month_end_hist_old2)-2)
    end_date_plneni             = dt.date(int(year_end_hist), month_end_date_plneni,1) 
    ehist_plneni                = '15.' +  str(int(month_end_fan) - 1) + '.' + year_ehist_plneni

    # Various
    last_fiscal_year            = int(sz_year) - years_yy_fiscal

    return (start_date, start_date_long, start_date_lev, start_date_lev_long, start_date_NTF, start_date_decomp, start_year_yy, \
           start_year_yy_q, end_date, end_date_old, end_date_decomp, end_hist_date, end_hist_date_tradeNonTrade, end_date_NTF, \
           end_hist_date_old, end_hist_date_old2, ehist, ehist_old, ehist_old2, ehist_yy, last_fiscal_year, start_date_fan, \
           end_date_fan, decomp_plneni_start, decomp_plneniSS_start, end_date_plneni, ehist_plneni, start_date_plneni, ehist_mm, ehist_hdp)
    # Currently 30 output dates. If more output dates added, update the number of outputs!



# --------------------------------------------------------------------------- #

def label_TSeriesFromDPSZ(sz_labels
                          ):
    
    '''
    This function creates labels for TSeries extracted from the DPSZ database.
    
    Input of the function is vector sz_labels with the obligatory elements:
        sz_label_DPSZ:         label of the current snapshot/version in the form of a string or number
        sz_old_label_DPSZ:     label of the last snapshot/version in the form of a string or number
                    
    The remaining elements of the vector sz_labels are optional and denote labels
        of specific communication lists; if '' then sz_label_DPSZ assigned automatically to those lists
    
    Example:
        label_TSeriesFromDPSZ('2020sz05_database', 4, '', 6)
        The function returns labels: '_2020sz05_database', '_ver4', '_2020sz05_database', '_ver6' 

        label_TSeriesFromDPSZ('', 2020sz07_database, '', 6)
        The function returns labels: '', '_2020sz07_database', '', '_ver6'
        
    Sep 2020 Jan Zacek
    Last modified: Jakub Bechny Feb 2021    
    
    '''
    
    
    # Labeling procedure
    
    labels_TS = []
    
    # sz_label_DPSZ_TS
    if sz_labels[0] == '':
        sz_label_DPSZ_TS = ''
    elif isinstance(sz_labels[0], int):
        sz_label_DPSZ_TS = '_ver' + str(sz_labels[0])
    else:
        sz_label_DPSZ_TS = '_' + sz_labels[0]
    labels_TS.append(sz_label_DPSZ_TS)
        
    # sz_old_label_DPSZ_TS
    if sz_labels[1] == '':
        sz_old_label_DPSZ_TS = ''
    elif isinstance(sz_labels[1], int):
        sz_old_label_DPSZ_TS = '_ver' + str(sz_labels[1])
    else:
        sz_old_label_DPSZ_TS = '_' + sz_labels[1]
    labels_TS.append(sz_old_label_DPSZ_TS)
    
    # remaining versions of lists for current sz
    for i in range(2, len(sz_labels)):
        if sz_labels[i] == '':
            sz_label_DPSZ_TS_i = sz_label_DPSZ_TS
        elif isinstance(sz_labels[i], int):
            sz_label_DPSZ_TS_i = '_ver' + str(sz_labels[i])
        else:
            sz_label_DPSZ_TS_i =  '_' + sz_labels[i]
        labels_TS.append(sz_label_DPSZ_TS_i)
        del sz_label_DPSZ_TS_i
                                
    return (labels_TS)

# --------------------------------------------------------------------------- #

def labels_TSeriesForDPSZ(sz_labels
                          ):
    
    '''
    This function creates labels for extraction of TSeries from the DPSZ database.
    
    Input of the function is vector sz_labels with the obligatory elements:
        sz_label_DPSZ:         label of the current snapshot/version in the form of a string or number
        sz_old_label_DPSZ:     label of the last snapshot/version in the form of a string or number
                    
    The remaining elements of the vector sz_labels are optional and denote labels
        of specific communication lists; if '' then sz_label_DPSZ assigned automatically to those lists
      
    Example:
        labels_TSeriesForDPSZ('2021sz01_database', '2020sz07_database', '', 6)
        The function returns labels: '2021sz01_database', '2020sz07_database', '2021sz01_database', 6       
        
    Feb 2021 Jakub Bechny    
    
    '''
    
    
    # Labeling procedure
    
    labels_DPSZ = []
    
    # sz_label_DPSZ given
    labels_DPSZ.append(sz_labels[0])
        
    # sz_old_label_DPSZ also given
    labels_DPSZ.append(sz_labels[1])
    
    # remaining versions of lists for current sz
    for i in range(2, len(sz_labels)):
        if sz_labels[i] == '': #if emppty, use sz_label_DPSZ
            sz_label_DPSZ_i = sz_labels[0]
        else: # otherwise, use what is given in sz_labels
            sz_label_DPSZ_i = sz_labels[i]
        labels_DPSZ.append(sz_label_DPSZ_i)
        del sz_label_DPSZ_i
                                
    return (labels_DPSZ)

