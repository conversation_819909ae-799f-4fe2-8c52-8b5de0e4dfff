function [handle] = drawbar(dseries,varargin)
%
% DRAWBAR  bar plot function for time series specially adjusted for reports.
%
% Syntax:
%   [h,rng] = drawbar(dseries,...)
%   [h,rng] = drawbar(dseries,range,...)
% Arguments:
%   h numeric; rng numeric; dseries tseries
% Options:
%   'legend'
%   'type'           char style of figure 'doc1', 'doc2', 'ppt'
%	'palette'		numeric - predefined set of colors and/or markers 
%							- 1 klasik
%							- 5 sparta
%							- 6 bohemka
%   'colors'        numeric - # colors used from palette
%   'title'         string of title
%   'endhist'      numeric - from StartHistory till EndHistory
%                   grayarea
%   no more Matlab PLOT options
%
% G3 tools

% @ rewritten Frantisek Brazdik, CNB, 2009-08
%   ZH, March 2014, properties 'colors' and 'palette'

%% initialize data

default = {
    'legend',{''},...
    'type','doc2',...
    'endhist',[],...
    'title',[],...
    'history_shift',0,...
    'post_process',[]...
    'pre_process',[]...
    'palette',1,...
	'colors',8,...
    'click_legend',0,...
    'click_legend_order',0,...
    'decomp_whole',[],...
    'decomp_list',[],...
    'ivar',nan,...
    'highlight',1,... 
    'dseries2',[],...
    'alpha',0.5    
    };

if nargin == 1
    ts_range = get(dseries,'range');
    options = passopt(default);
elseif nargin == 2
    ts_range = varargin{1};
    options = passopt(default);
elseif (nargin >2)
    ts_range = varargin{1};
    options = passopt(default,varargin(2:end));
else
    error('Incorrect number of inputs.')
end
% if end of history not specified, take the end of series
if isempty(options.endhist),
    options.endhist = ts_range(end);
end;

dseries2 = options.dseries2;
alpha = options.alpha;


%% separate negative and positive values
um = dseries(ts_range);
uposm = um;
uposm(find(um < 0)) = 0;
unegm = um;
unegm(find(um > 0)) = 0;
upos = tseries(ts_range, uposm);
uneg = tseries(ts_range, unegm);


uplus = sum(upos,2); uminus = sum(uneg,2);
ures = uplus + uminus;

if ~isempty(dseries2)
    um2 = dseries2(ts_range);
    uposm2 = um2;
    uposm2(find(um2 < 0)) = 0;
    unegm2 = um2;
    unegm2(find(um2 > 0)) = 0;
    upos2 = tseries(ts_range, uposm2);
    uneg2 = tseries(ts_range, unegm2);


    uplus2 = sum(upos2,2); uminus2 = sum(uneg2,2);
    ures2 = uplus2 + uminus2;    
end
%% plot the bar graph
% keyboard;
         
if ~isempty(dseries2)
    g2 =  bar(upos2,'BarLayout','stacked','EdgeColor',[0 0 0],'barwidth',0.8);%,'linestyle','none');
    ch = get(g2,'child');
    for child=1:length(ch)
        set(ch{child},'facea',0.5);
        set(ch{child},'Zdata',0.25*ones(5,length(get(ch{child},'Xdata'))));
        Ydata=get(ch{child},'Ydata');
        Ydata(1,:)=Ydata(2,:);
        set(ch{child},'Ydata',Ydata);
    end
    
    hold on
    
    g3 =  bar(uneg2,'BarLayout','stacked','EdgeColor',[0 0 0],'barwidth',0.8);%,'linestyle','none');
    ch = get(g3,'child');
    for child=1:length(ch)
        set(ch{child},'facea',0.5);
        set(ch{child},'Zdata',0.25*ones(5,length(get(ch{child},'Xdata'))));
        Ydata=get(ch{child},'Ydata');
        Ydata(1,:)=Ydata(2,:);
        set(ch{child},'Ydata',Ydata);
	end
end   

if isempty(dseries2)
    g =  bar(upos,'BarLayout','stacked','EdgeColor',[0 0 0]);
    % g(1:2) = bar(upos,'BarLayout','stacked','EdgeColor',[0 0 0]);
else
    g =  bar(upos,'BarLayout','stacked','EdgeColor',[0 0 0],'barwidth',0.4);%,'linestyle','none');
    ch = get(g,'child');
    for child=1:length(ch)
        set(ch{child},'facea',1);
        set(ch{child},'Zdata',0.5*ones(5,length(get(ch{child},'Xdata'))));
        Ydata=get(ch{child},'Ydata');   %Xdata=get(ch{child},'Xdata');   
        Ydata(1,:)=Ydata(2,:);          %Xdata(1,:)=Xdata(1,:)+0.1;
        set(ch{child},'Ydata',Ydata);   %set(ch{child},'Xdata',Xdata);        
	end
end

hold on;

if isempty(dseries2)
    g1 = bar(uneg,'BarLayout','stacked','EdgeColor',[0 0 0]);
    % g(3:4) = bar(uneg,'BarLayout','stacked','EdgeColor',[0 0 0]);
else
    g1 = bar(uneg,'BarLayout','stacked','EdgeColor',[0 0 0],'barwidth',0.4);%,'linestyle','none');
    ch = get(g1,'child');
    for child=1:length(ch)
        set(ch{child},'facea',1);
        set(ch{child},'Zdata',0.5*ones(5,length(get(ch{child},'Xdata'))));
        Ydata=get(ch{child},'Ydata');   %Xdata=get(ch{child},'Xdata');   
        Ydata(1,:)=Ydata(2,:);          %Xdata(1,:)=Xdata(1,:)+0.1;
        set(ch{child},'Ydata',Ydata);   %set(ch{child},'Xdata',Xdata);
	end
end

set(gca,'YGrid','on');

%% Setting parameters for the graph visual aspect

% SET GRAPH STYLE
[gstyle, result] =  define_graph_style('drawbar',options);
if result
    disp('Wrong graph style!');
end;

series_nmbr = size(dseries,2);

if options.colors == 0
		set(gcf, 'ColorMap',rand(series_nmbr,3));
else
	colser_nmbr = min(length(gstyle.barcolormap),options.colors);
	if series_nmbr <= colser_nmbr
		set(gcf, 'ColorMap',gstyle.barcolormap(1:series_nmbr,:));
	else
		set(gcf, 'ColorMap',[gstyle.barcolormap(1:colser_nmbr,:) ; rand(series_nmbr-colser_nmbr,3)]);
	end;
end

if ~isempty(dseries2)
    p1 = plot(ts_range,ures2,'Color','w','Marker','o','MarkerSize',7,'LineWidth',gstyle.LineWidth+1.5,'LineStyle','none');
    p2 = plot(ts_range,ures2,'Color',gstyle.flinecolor,'Marker','o','MarkerSize',6,'LineWidth',gstyle.LineWidth,'LineStyle','none');
    p3 = plot(ts_range,ures,'Color','w','Marker',gstyle.flinemarker,'MarkerSize',gstyle.MarkerSize,'LineWidth',gstyle.LineWidth+1.5);
    p4 = plot(ts_range,ures,'Color',gstyle.flinecolor,'Marker',gstyle.flinemarker,'MarkerSize',gstyle.MarkerSize,'LineWidth',gstyle.LineWidth);
    set(p1,'Zdata',ones(size(get(p1,'Xdata'))));
    set(p2,'Zdata',ones(size(get(p2,'Xdata'))));
    set(p3,'Zdata',ones(size(get(p3,'Xdata'))));
    set(p4,'Zdata',ones(size(get(p4,'Xdata'))));
else
    plot(ts_range,ures,'Color','w','Marker',gstyle.flinemarker,'MarkerSize',gstyle.MarkerSize,'LineWidth',gstyle.LineWidth+1.5);
    plot(ts_range,ures,'Color',gstyle.flinecolor,'Marker',gstyle.flinemarker,'MarkerSize',gstyle.MarkerSize,'LineWidth',gstyle.LineWidth);
end
% g(5) = plot(ts_range,ures,'Color',gstyle.flinecolor,'Marker',gstyle.flinemarker,'MarkerSize',gstyle.MarkerSize,'LineWidth',gstyle.LineWidth);
% legend(g([1 2 5]),{'Hours worked per employee','Employees','Employees (full-time equivalent)'});

%% pre-processing
for ii=1:size(options.pre_process,1)
    w = warning ('off','all');
    eval(char(options.pre_process(ii,:)));
    warning(w);
end;

%% Plot modifications of the actual graph
h = get(gcf,'children');

set(gcf,'Units','centimeters') ;
set(gcf,'Position',gstyle.figure_size);
set(gca,'FontName',gstyle.AxesFontName);
set(gca,'FontWeight',gstyle.AxesFontWeight);
set(gca,'FontSize',gstyle.AxesFontSize);

if ~strcmp(options.title,'')
    set(gca,'Title',text('String',options.title));
    set(get(gca,'Title'),'FontName',gstyle.TitleFontName);
    set(get(gca,'Title'),'FontWeight',gstyle.TitleFontWeight);
    set(get(gca,'Title'),'FontSize',gstyle.TitleFontSize);
end

if ~strcmp(options.legend,'')
    lg = legend(options.legend,'Location','West'); %the legend
    set(lg, 'FontName', gstyle.LegendFontName);
    set(lg, 'FontWeight', gstyle.LegendFontWeight);
    set(lg, 'FontSize', gstyle.LegendFontSize);
end



% modify xticklabel
xtick = cellstr(get(gca,'XTickLabel'));
first_tick = xtick{1}(6);
seasonlist = cell2mat(xtick);
lseasonlist = length(unique(seasonlist(:,6)));
for i = 1:length(xtick),
    year=xtick{i}(3:4); season = xtick{i}(6);
    switch iscell(xtick)
        case strcmp(season,'1');
            if season == first_tick 
                xtick_mod{i} = strcat('I/',year);
            else
                xtick_mod{i} = strcat(' I');
            end;
        case strcmp(season,'2');
            if season == first_tick
                xtick_mod{i} = strcat('II/',year);
                if lseasonlist > 1 
                    first_tick = '1';
                end
            else
                xtick_mod{i} = strcat(' II');
            end;
        case strcmp(season,'3');
            if season == first_tick     
                xtick_mod{i} = strcat('III/',year);
                if lseasonlist > 1 
                    first_tick = '1';
                end
            else
                xtick_mod{i} = strcat('III');
            end;
        case strcmp(season,'4');
            if season == first_tick 
                xtick_mod{i} = strcat('IV/',year);
                if lseasonlist > 1 
                    first_tick = '1';
                end
            else
                xtick_mod{i} = strcat(' IV');
            end;
    end
end
set(gca,'XTickLabel',char(xtick_mod));

%make offset for first and last bar
Xlimsize = get(gca,'Xlim');
set(gca,'Xlim', Xlimsize  - [gstyle.BarOffset -gstyle.BarOffset]);
if options.highlight == 1
    if options.endhist < (ts_range(end)+1) || options.endhist == (ts_range(end)+1)
        highlight(options.endhist:(ts_range(end)+1));
    end
end
hold off;

%% post-processing
for ii=1:size(options.post_process,1)
    eval(char(options.post_process(ii,:)));
end;

%% Decomposition - detailed grouping (clickable legend)

if options.click_legend
    lg_entries = get(lg,'children');
    groups     = evalin('caller','options.groups');
    groups_nms = evalin('caller','options.groups_names');
    varnow     = evalin('caller','decomp_list(ivar)');
    
    onClick_feature(lg_entries,varnow,groups,groups_nms,options.decomp_whole,options.decomp_list,options.ivar,options.click_legend_order);
end

%% UI tools

% Always on top Java feature
% + .pdf printer
% + .pptx exporter

% if args.visible
    pushbuttons(gcf);
% end

%% send handle out
if nargout > 0
    handle = g;
end

end %<eof>