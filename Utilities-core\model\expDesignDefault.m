function def = expDesignDefault(mode)
% Default expectations setup

switch lower(mode)
	
    case 'minimal'
        def = { ...
% 			'CLUSTERS OF SHOCKS                                                     WEIGHTS VECTOR
			{'eps_y_star_gap','eps_Istar','eps_target'}                             [1 0.8 0.6 0.4 0.2]
            {'eps_p_BrentUSD','eps_pstar_other_tilde','eps_USDEUR','eps_pREG', ...
			 'eps_gov','eps_costpushG','eps_uip','eps_costpushC', ...
			 'eps_mpolicy'}                                                         [0.5 0.5]
              };
        
     case 'setup1'
        def = { ...
% 			'CLUSTERS OF SHOCKS                                                     WEIGHTS VECTOR
            {'eps_target'}                                                          [ones(1,40)]
            {'eps_Istar','eps_Pstar','eps_y_star_gap','eps_dot_y_star_trend', ...
            'eps_y_star_trend', 'eps_p_BrentUSD',...
            'eps_pstar_other_tilde','eps_USDEUR',...
            'eps_p_ener_ex_oil_tilde', 'eps_i_star_eq'}                             [ones(1,8) (1:-0.2:0.1)]
            {'eps_pREG','eps_gov','eps_costpushG'}                                  [0.75 0.5 0.25]
            {'eps_uip','eps_costpushC'}                                             [0.5 0.25]
              };
          
    case 'setup2'
        def = { ...
% 			'CLUSTERS OF SHOCKS                                                     WEIGHTS VECTOR
            {'eps_target'}                                                          [ones(1,40)]
            {'eps_Istar','eps_Pstar','eps_y_star_gap','eps_dot_y_star_trend', ...
            'eps_y_star_trend', 'eps_p_BrentUSD',...
            'eps_pstar_other_tilde','eps_USDEUR',...
            'eps_p_ener_ex_oil_tilde', 'eps_i_star_eq'}                             [ones(1,8) (1:-0.2:0.1)]
            {'eps_pREG','eps_gov','eps_costpushG'}                                  [0.75 0.5]
            {'eps_uip','eps_costpushC'}                                             [0.5 0.25]
              };
          
	case 'expscheme_fb'
        def = { ...
% 			'CLUSTERS OF SHOCKS                                                     WEIGHTS VECTOR
            {'eps_Istar','eps_shadow_rate','eps_i_star_us',...
            'eps_pstar_energy_tilde','eps_pstar_other_tilde','eps_pstar_tilde',...
            'eps_dot_cpi_star_tilde','eps_pstar_RP_tilde','eps_dot_z_eq',...
            'eps_dot_y_star_trend_fund','eps_y_star_gap',... 
            'eps_dot_y_star_trend_shift','eps_USDEUR','eps_prem_usdeur', ...
            'eps_energy_share_ppi_star_gap'}                                        [ones(1,4) 0.75 0.50 0.25]
            {'eps_exp_i_star_us','eps_i_star_eq','eps_exp_Istar','eps_exp_shadow_rate','eps_exp_i_star_eq',...
            'eps_exp_pstar_energy_tilde','eps_exp_pstar_other_tilde',...
            'eps_exp_pstar_tilde', 'eps_exp_dot_cpi_star_tilde',...
            'eps_exp_pstar_RP_tilde','eps_exp_dot_z_eq',...
            'eps_exp_dot_y_star_trend_fund','eps_exp_y_star_gap',...
            'eps_exp_dot_y_star_trend_shift','eps_exp_USDEUR',...
            'eps_exp_prem_usdeur', 'eps_exp_energy_share_ppi_star_gap'}          	ones(1,40)
            };    
          
    case 'replication'
        def = { ...
% 			'CLUSTERS OF SHOCKS                                                     WEIGHTS VECTOR
			{'eps_Istar','eps_target'}                                              [ones(1,6) (1:-0.1:0.1)]
			{'eps_Pstar'}                                                           [ones(1,6) (1:-0.2:0.2)]
            {'eps_y_star_gap'}                                                      [ones(1,6) (1:-0.05:0.05)]
              };
          
    case 'full_exp'
        def = { ...
% 			'CLUSTERS OF SHOCKS                                                     WEIGHTS VECTOR
			{'eps_y_star_gap','eps_Pstar','eps_Istar','eps_target'}                 [ones(1,40)]
              };	
          
    case 'unexp'
        def = { ...
% 			'CLUSTERS OF SHOCKS                                                     WEIGHTS VECTOR
			{'eps_y_star_gap','eps_Pstar','eps_Istar','eps_target'}                 [0.0001]
              };	
        
    otherwise
        error('Unknown default setup for the expectations object');
end

end %<eof>
