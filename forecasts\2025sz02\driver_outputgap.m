%**************************************************************************
%% DRIVER_OUTPUTGAP
%    - This driver decomposes foreign GDP into gap and trend components
%    - The resulting decomposition is saved in new histcore ..\database\Output-data\histcore_name_adj
%    - Decomposition:
%       - Prolonged fcast range is used for history, i.e., the filtering step is applied
%       - Purely endogenous if no ad-hoc expert tunes imposed
%       - Expert tunes imposed via get_tunes_FB (tune_dot_y_star_trend_fund, tune_dot_y_star_trend_shift, tune_y_star_gap)
% 
% @ last revision: JZ CNB, July 2023
%************************************************************************** 

close all
clear all
disp([sprintf('\n'), 'DRIVER_OUTPUTGAP']);

%**************************************************************************
%% Set options
do_filter_book		= 0; 
do_filter_detail	= 0;
do_filter_compare   = 0;
do_filter_pt        = 0; 

% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('GLOBALSETTINGS.mat');

disp(['FB_ID: ', FB_ID]);

% load settings
settings = load_settings(FB_ID);

% modify settings
settings.hrng = settings.shist:settings.end_pred_long;
settings.FB_gdp_decomp = 1;


%**************************************************************************
%% Read model and parameters
SS.filtering     = true;
SS               = setparam_FB(settings, 'filtering', SS.filtering);
SS.FB_gdp_decomp = settings.FB_gdp_decomp;
m = readmodel('../../Utilities-core/model/inc_g3_FB.model',SS);

%**************************************************************************
%% Load data
% h histcore database
h = dbload(settings.histcore_name, 'leadingRow', 'date', 'freq=', 4,...
 'delimiter=', ',');

% enrich histcore database by some ad-hoc data sources
h = changedata_FB(h,settings,SS);

% dbm model database
dbm = histdata_FB(h, settings);

% remove forecast from database
d = dbclip(dbm,settings.hrng);                                  

% add tunes
dtunes = get_tunes_FB(SS);

%**************************************************************************
%% Run the filtering step
dbfilter = filterhistory(m, d, dtunes, SS,settings.hrng);
f = dbfilter.mean;
% add out of model core variables
f = make_outofcore_FB(f, SS, settings);
% make transformations
f_model = make_transformations_FB(f, SS, false);
% add reporting layer of data
ff = make_transformations_present_FB(f_model, h, settings, SS);

%**************************************************************************
%% Report
if do_filter_book
    book_filter_FB(ff, m, settings, ...
        ['Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-book-outputgap-fb']);
end

if do_filter_detail
    book_filter_detail_FB(ff, m, settings, ...
        ['Detailed Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-detail-outputgap-fb']);
end

if do_filter_compare
    ff_old  = dbload([settings.outdata_dir '\' settings.oldfrc_prefix '-outputgap-fb.csv']);
    settings_old  = load([settings.outdata_dir '\' settings.oldfrc_prefix '-settings.mat']);
    book_filter_compare_FB(ff, ff_old, m, settings,settings_old, ...
        ['Compare Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-v-' settings.oldfrc_prefix '-filter-outputgap-comp-fb']);
end

if do_filter_pt
    book_filter_pt_FB(ff, m, settings, ...
        ['Detailed Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-outputgap-detail-pt']);
end

%**************************************************************************
%% Prepare histcore output databases and check trend/gap decomposition
h_adj = h;

list_add = {{'zz_y_star_trend', 'gdp_emu_trend'},{'zz_y_star_trend_fund', 'gdp_emu_trend_fund'},...
            {'zz_dot_y_star_trend_shift','gdp_emu_trend_shift'}, {'zz_y_star_gap','gdp_emu_gap'}, {'zz_prem_usdeur','prem_usdeur'}};

for jj = 1:length(list_add)
    if strcmp(list_add{jj}{1},'zz_y_star_gap')
        h_adj.(list_add{jj}{2}) = ff.(list_add{jj}{1})/100;
    else
        h_adj.(list_add{jj}{2}) = ff.(list_add{jj}{1});
    end
end

list_remove = {'equilibrium_i_star','weight_dot_pstar_energy_tilde','weight_pstar_energy_tilde','p_brentUSD_tilde','p_brent_tilde'};
h_adj = rmfield(h_adj,list_remove);

% See the resulting decomposition
figure('Color', 'white','Position', [100 100 800 400]);
    plot([h_adj.gdp_emu h_adj.gdp_emu_trend h_adj.gdp_emu_trend_fund],'linewidth', 2.5); grid on; 
    legend('GDP', 'Potential','Potential - fund', 'Location', 'best'); hold off;

figure('Color', 'white','Position', [100 100 800 400]);
    plot([ff.zz_dot_y_star_trend],'linewidth', 2.5); grid on; hold on; 
    XT = get(gca, 'XTick')'; XTL = get(gca,'XTickLabel')';
    pl1 = plot((ff.tune_dot_y_star_trend_fund^4-1)*100,'-o'); hold on;
    pl2 = plot((ff.tune_dot_y_star_trend_shift^4-1)*100,'-o');
    set(pl1,'MarkerFaceColor','red','MarkerEdgeColor','white','MarkerSize',10); 
    set(pl2,'MarkerFaceColor','green','MarkerEdgeColor','white','MarkerSize',10); 
    set(gca,'Xtick',XT); set(gca,'XTickLabel', XTL);
    legend('Potential (QoQ, %)','Tunes - fund','Tunes - shift', 'Location', 'best'); hold off;

figure('Color', 'white','Position', [100 100 800 400]);
    plot([ff.zz_y_star_gap],'linewidth', 2.5); grid on; hold on; 
	XT = get(gca, 'XTick')'; XTL = get(gca,'XTickLabel')';
    pl2 = plot((ff.tune_y_star_gap-1)*100,'-o'); 
    set(pl2,'MarkerFaceColor','red','MarkerEdgeColor','white','MarkerSize',10); 
	set(gca,'Xtick',XT); set(gca,'XTickLabel', XTL);
    legend('Output gap','Tunes', 'Location', 'best');

% Check the sum of components
if sum(h_adj.gdp_emu - (h_adj.gdp_emu_trend + h_adj.gdp_emu_trend.*h_adj.gdp_emu_gap)) > 0.00001;
	error('GDP decomposition does not add up to GDP itself!');
end

%**************************************************************************
%% Save 
% histcore with gdp decomposition
dbsave(h_adj,[settings.histcore_name_adj],Inf,'format','%.16e');
% filter db: only model variables
save([settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.mat'],'-struct','f');
% output database: model and reporting level of variables, csv
dbsave(ff,[settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.csv'],Inf,'format','%.16e');
%%

%%

dd_old  = dbload([settings.outdata_dir '\' settings.oldfrc_prefix '-outputgap-fb.csv']);
%dd_old = dbload('N:\ZahrBlock\ModelReset\forecasts\database\Output-data\2023sz07_fbs_new-forecast-fb.csv');

disp([sprintf('\n'), 'Comparison of Gaps, Trends, GDP Growths']);
[ff.zz_y_star_gap dd_old.zz_y_star_gap ff.dot_y_star_trend_fund dd_old.dot_y_star_trend_fund ff.dot_y_star dd_old.dot_y_star]

diff_gdp = ff.zz_y_star-dd_old.zz_y_star;
disp([sprintf('\n'), 'GDP LEVEL DIFFERENCE']);
diff_gdp(settings.end_pred) -diff_gdp(settings.ehist)

diff_tnd = ff.zz_y_star_trend_fund-dd_old.zz_y_star_trend_fund;
disp([sprintf('\n'), 'GDP TREND LEVEL % DIFFERENCE']);
diff_tnd(settings.end_pred) - diff_tnd(settings.ehist)

(diff_tnd(settings.end_pred) - diff_tnd(settings.ehist)) / (diff_gdp(settings.end_pred) -diff_gdp(settings.ehist))


%bs = dbload('N:\ZahrBlock\ModelReset\forecasts\database\Output-data\2023sz07_fbs_new-forecast-fb.csv');

data_plot1 = dbclip(ff, qq(2021,1):settings.end_pred+8);
data_plot2 = dbclip(dd_old, qq(2021,1):settings.end_pred+8);
%data_plot3 = dbclip(bs, qq(2021,3):settings.end_pred);

figure('Color', 'white','Position', [200 100 1600 1000]);
subplot(2,2,2)
hold on; grid on;
plot(data_plot2.dot_y_star, 'linewidth', 2, 'Color', 'Blue');
plot(data_plot1.dot_y_star, 'linewidth', 2, 'Color', 'Red');
%plot(data_plot3.dot_y_star, 'linewidth', 2, 'Color', 'Green'); hold on;
plot(data_plot1.dot_y_star_trend_ss, 'linewidth', 1.5, 'Color', 'Black');
legend('Growth GDP Old','Growth GDP New', 'Location', 'best');
hold off;
subplot(2,2,1)
hold on; grid on;
plot(data_plot2.zz_y_star_gap, 'linewidth', 2, 'Color', 'Blue'); 
plot(data_plot1.zz_y_star_gap, 'linewidth', 2, 'Color', 'Red');
%plot(data_plot3.zz_y_star_gap, 'linewidth', 2, 'Color', 'Green'); hold on;
legend('Output Gap Old','Output Gap New', 'Location', 'best');
hold off;
subplot(2,2,3)
hold on; grid on;
plot(data_plot2.dot_y_star_trend_fund, 'linewidth', 2, 'Color', 'Blue'); 
plot(data_plot1.dot_y_star_trend_fund, 'linewidth', 2, 'Color', 'Red'); 
%XT = get(gca, 'XTick')'; XTL = get(gca,'XTickLabel')';
tunez = plot(data_plot1.tune_dot_y_star_trend_fund,'-o');
%plot(data_plot3.dot_y_star_trend, 'linewidth', 2, 'Color', 'Green'); hold on;
plot(data_plot1.dot_y_star_trend_ss, 'linewidth', 1.5, 'Color', 'Black'); hold on;
set(tunez,'MarkerFaceColor','Black','MarkerEdgeColor','white','MarkerSize',2); 
%set(gca,'Xtick',XT); set(gca,'XTickLabel', XTL);
legend('Trend Old','Trend New', 'Location', 'best');
hold off;
subplot(2,2,4)
hold on; grid on;
plot(data_plot2.zz_y_star, 'linewidth', 2, 'Color', 'Blue');
plot(data_plot1.zz_y_star, 'linewidth', 2, 'Color', 'Red');
%plot(data_plot3.dot_y_star, 'linewidth', 2, 'Color', 'Green'); hold on;
legend('GDP Old','GDP New', 'Location', 'best');
hold off;



