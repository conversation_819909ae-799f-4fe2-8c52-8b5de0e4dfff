%**************************************************************************
function m = readmodel(name, p)
%**************************************************************************

m = model(name, 'assign', p);

% assign the parameters to the model
% m = assign(m, p);

[flag,list] = isnan(m,'parameters');
if flag == true, error('Parameter not assigned: %s.\n',list{:}); end

[flag,list] = isnan(m,'sstate');
if flag == true, error('Steady state not assigned: %s.\n',list{:}); end

[flag,meval,list] = chksstate(m);
if flag == false, error('Equation fails to hold in steady state: %s.\n',list{:}); end

[m,eigval] = solve(m);

[flag] = isstationary(m);
if flag == true
	display('The model is Stationary!'); 
else
	display('NOTE: The model IS NOT Stationary');
end