function decomp_explode_newold(decomp_list,groups,groups_nms)

% keyboard;

%% driver_decomp_newold <stolen>

sr_opt = evalin('base','sr_opt');%sr_options;

new_ID                  = evalin('base','new_ID');% name of the new sz (all - forecast, filter)
old_ID                  = evalin('base','old_ID');% name of the old sz or SS

compute_decomposition   = 0;                    % yes - new decomposition is computed; no - reuse of the data
displaygroups           = evalin('base','displaygroups');
detail_level            = evalin('base','detail_level');    % 0 - do just with respect to vars, don't decompose into quarters,
                                                            % 1 - separate NTF obs and fixes on forecast into quarters,
                                                            % 2 - decompose into quarters all variables
                                                
firstgroups             = 0; % if no groups specified, this # groups is created

decomp_range            = evalin('base','decomp_range');%sr_opt.fcastrng;
plot_range              = evalin('base','plot_range');%sr_opt.fcastrng;      	% range which will be depicted
modelchange             = evalin('base','modelchange');%1;						% shows contribution of model changes in graphs and reports
zz_transform            = evalin('base','zz_transform');%1;						% transformation to %

doXLS                   = 0;						% export contributions in .xls format
doReport                = 0;						% report in PDF
doGraphs                = 1;						% MATLAB graphs
graph_style             = evalin('base','graph_style');%0;                        % 1 - new, 0 - old;
language                = evalin('base','language');%'CZ';						% language version EN/CZ

output_ID               = 'temp';% this string is added to saved report and xls name; could be also empty string:
								 % '2text' use this ID for official version exported to xls (graph in text)
grouping_type           = evalin('base','grouping_type');   % 1 - Zuzka - READ_GROUPS.M 
                                                            % 0 - Franta - SET_GROUPS.M
click_legend            = 1;                                % Turn on/off onClick feature for detailed decomposition
autogroups = 'MANUAL';

%% Define groups

% decomp_list --> inherited argument
% groups_nms --> inherited argument
% groups --> inherited argument
   
%% Rest of driver_decomp_newold code

decomp =evalin('base','decomp');

% [contrib, groups_nms] = report_decomposition(decomp, sr_opt, plot_range, ...
  report_decomposition(decomp, sr_opt, plot_range, ...
	'decomp_list',decomp_list, ...
	'grouping_type',grouping_type, ...
	'autogroups',autogroups, ...
	'firstgroups',firstgroups,...
	'xls',doXLS, ...
	'report',doReport, ...
	'graphs',doGraphs, ...
	'output_id',output_ID, ...
	'language',language, ...
	'zz_transform',zz_transform, ...
    'graph_style', graph_style, ...
    'print_replace_all',    true, ...
    'displaygroups',        displaygroups, ...
    'manual_groups_names',  groups_nms, ...
    'manual_groups',  groups, ...
    'click_legend', click_legend);

end %<eof>