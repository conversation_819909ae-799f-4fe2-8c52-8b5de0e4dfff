function  update_callbacks_decomp(hfig)

    UserData = getappdata(hfig,'UserData');
    decomp       = UserData.decomp;
    decomp_names = UserData.legend_names;
    precision    = UserData.precision;

    hManager = uigetmodemanager(hfig);
	
    dcm_obj = datacursormode(hfig);
    
    set(dcm_obj,'Enable','on');
    
    set(dcm_obj,'UpdateFcn',{@custom_datatip_decomp,decomp,decomp_names,precision});
    
    try
        set(hManager.WindowListenerHandles, 'Enable', 'off');  % HG1
    catch
        [hManager.WindowListenerHandles.Enabled] = deal(false);  % HG2
    end
    
    set(hfig, 'WindowKeyPressFcn', []);

    set(hfig,'KeyPressFcn',{@key_down_decomp,decomp,decomp_names,precision});

end