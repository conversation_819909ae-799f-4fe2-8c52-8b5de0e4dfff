function decomp = merge_decomp_stages(varargin)

% Input: variables in decomp format - in the order from new to old!!!
% Output: a variable in decomp format

if nargin==0
	error('No stages to merge!');
end

%% Check consistency

newIDs = {};
oldIDs = {};
for ix = 1 : nargin
	newIDs = [newIDs,varargin{ix}.new_ID];
	oldIDs = [oldIDs,varargin{ix}.old_ID];
end
if length(unique(newIDs))<length(newIDs) || length(unique(oldIDs))<length(oldIDs)
	error('Inconsistent stages for decomposition!');
end


%% Merge       

% initialise 
endog_vars = varargin{1}.endog_vars;
decomp_range = varargin{1}.decomp_range;
for ix = 2:nargin
	if (decomp_range(1) < varargin{ix}.decomp_range(1)) || ... 
	   (decomp_range(end) > varargin{ix}.decomp_range(end))
		error('Range of decomposition exceeds some of loaded partial decomposition ranges! Recompute...');
	end
	if ~isempty(setdiff(union(endog_vars,varargin{ix}.endog_vars),intersect(endog_vars,varargin{ix}.endog_vars)))
		error('Some endogenous variables are missing in some decomposition stages! Recompute...');
	end
end

input_names = varargin{1}.input_names;
input_datenames = varargin{1}.input_datenames;
input_vect = varargin{1}.input_vect;
store_matrix = varargin{1}.store_matrix;

% fill store_matrix & co.
for ix = 2:nargin
	for iy = 1:length(varargin{ix}.input_datenames)
		[isthere,ind] = ismember(varargin{ix}.input_datenames{iy},input_datenames);
		if isthere
			store_matrix(:,ind,:) = store_matrix(:,ind,:) + varargin{ix}.store_matrix(:,iy,:);
		else
			store_matrix(:,end+1,:) = varargin{ix}.store_matrix(:,iy,:);
			input_datenames = [input_datenames; varargin{ix}.input_datenames{iy}];
			input_names = [input_names, varargin{ix}.input_names{iy}];
			input_vect = [input_vect; varargin{ix}.input_vect(iy,:)];
		end
	end
end

% fill truediffmat
truediffmat = NaN(length(decomp_range),length(endog_vars));
joint_endogvars = intersect(fieldnames(varargin{1}.d_new),fieldnames(varargin{end}.d_old));
for k = 1 : length(endog_vars)
    if any(strcmp(endog_vars{k},joint_endogvars))
        if isempty(regexp(endog_vars{k},'\w*_exp$','ONCE'))
            if varargin{1}.islog.(endog_vars{k})
                truediffmat(:,k) = ...
                    log(imag(varargin{1}.d_new.(char(endog_vars(k)))(decomp_range)) ./ ...
                    imag(varargin{end}.d_old.(char(endog_vars(k)))(decomp_range)));
            else
                truediffmat(:,k) = ...
                    imag(varargin{1}.d_new.(char(endog_vars(k)))(decomp_range)) - ...
                    imag(varargin{end}.d_old.(char(endog_vars(k)))(decomp_range));
            end
        else
            if varargin{1}.islog.(endog_vars{k-length(endog_vars)/2})
                truediffmat(:,k) = ...
                    log(real(varargin{1}.d_new.(char(endog_vars{k-length(endog_vars)/2}))(decomp_range)) ./ ...
                    real(varargin{end}.d_old.(char(endog_vars{k-length(endog_vars)/2}))(decomp_range)));
            else
                truediffmat(:,k) = ...
                    real(varargin{1}.d_new.(char(endog_vars{k-length(endog_vars)/2}))(decomp_range)) - ...
                    real(varargin{end}.d_old.(char(endog_vars{k-length(endog_vars)/2}))(decomp_range));
            end
        end
    end
end

%% Output
decomp.store_matrix		= store_matrix;
decomp.truediffmat		= truediffmat;
decomp.endog_vars		= endog_vars;
decomp.input_names		= input_names;
decomp.input_vect		= input_vect;
decomp.input_datenames	= input_datenames;

decomp.decomp_range		= decomp_range;

decomp.d_new			= varargin{1}.d_new;
decomp.d_old			= varargin{end}.d_old;
decomp.hist_rng			= varargin{end}.hist_rng;
decomp.fut_rng			= varargin{1}.fut_rng;
decomp.trans_rng		= decomp.hist_rng(end)+1:decomp.fut_rng(1)-1;

decomp.islog			= varargin{1}.islog;
decomp.new_ID			= varargin{1}.new_ID;
decomp.old_ID			= varargin{end}.old_ID;
decomp.plan_type		= varargin{1}.plan_type;
decomp.decomp_type      = '';
decomp.detail_level     = varargin{1}.detail_level;
decomp.limit			= varargin{1}.limit;
