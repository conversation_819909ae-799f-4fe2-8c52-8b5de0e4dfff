# -*- coding: utf-8 -*-
"""
Created on Wed May 13 10:13:25 2015

@author: U04483
"""

import datetime
import pandas as pd
import numpy as np
import requests
import time
from lxml import html
import win32com.client, os

XLAPP_VISIBLE = False

def convertIrisDates2Pandas(tseries):
    '''
    converts IRIS(Matlab) time format into pandas time format
    
    '''
    outtseries=[]
    if len(tseries)>0:
        for item in tseries:
            if 'Comment' in item:
                outtseries.append(None)
            elif 'Class' in item:
                outtseries.append(None)    
            elif 'M' in item:
                outtseries.append(pd.Period(item.replace('M','-'), freq='M').to_timestamp())
            elif 'Q' in item:
                outtseries.append(pd.Period(item, freq='Q-DEC').to_timestamp())
            elif 'Y' in item:
                outtseries.append(pd.Period(item.replace('Y',''), freq='A').to_timestamp())
                
#B   business day frequency
#C   custom business day frequency (experimental)
#D   calendar day frequency
#W   weekly frequency
#M   month end frequency
#BM  business month end frequency
#MS  month start frequency
#BMS business month start frequency
#Q   quarter end frequency
#BQ  business quarter endfrequency
#QS  quarter start frequency
#BQS business quarter start frequency
#A   year end frequency
#BA  business year end frequency
#AS  year start frequency
#BAS business year start frequency
#H   hourly frequency
#T   minutely frequency
#S   secondly frequency
#L   milliseconds
#U   microseconds        
#    
    
    return outtseries

def getTseriesFromDBASE(tseries,
                        sdate=1996,
                        edate=2025,
                        database='',                        
                        fileSlovnik_name='XXXaaaXXX.XXX',
                        PrekladIn='XXXaaaXXX',                        
                        sheetCSV='',
                        INTforecast=False,
                        DBexistujici=False,                        
                        file_makra=os.path.abspath(os.path.dirname(__file__)) + '\\..\\..\\tools\\Makra.xlsm', #os.path.abspath('Makra.xlsm'),                        
                        file_temp=os.path.abspath('tempData.xlsx'),
                        fileCSV=os.path.abspath('tempData.csv')
                        ):
    '''
    This function open makro file filespec and 
     run in it macro VytvorCSVzListu, which
     needs :
     string database - if also want other databases, then input: '|2014sz03'
     string file_setup - file with list of time series from Database410 
     string file_temp - temporary file co extract data from Database410
     string fileCSV - output csv file
     string fileSlovnik_name - dictionary file
     string PrekladIn - sheet name in dictionary file
     string sdate - starting year '1996'
     string edate - ending year
     all paths must be absolute!!!
     Jan 2014 Frantisek Kopriva    
    '''
    if len(sheetCSV) == 0:
        pomstr = tseries.split('_')
        if pomstr[2]=='dd':
              sheetCSV = 'Database_D'
        elif pomstr[2]=='mm':
              sheetCSV = 'Database_M'
        elif pomstr[2]=='qq':
              sheetCSV = 'Database_Q'
        elif pomstr[2]=='yy':
              sheetCSV = 'Database_Y'
        else:
              print(u'Nespravný vstup časových řad.')
      
    win32com.client.pythoncom.CoInitializeEx(win32com.client.pythoncom.COINIT_APARTMENTTHREADED )
    #ppt = win32com.client.gencache.EnsureDispatch('PowerPoint.Application')
    xlApp = win32com.client.Dispatch('Excel.Application')
    xlApp.Visible = XLAPP_VISIBLE
    time.sleep(0.25)
    xlApp.Workbooks.Open(Filename=file_makra,ReadOnly=1)
    ntry = 0
    savedOK = False
#    print(tseries)     
    while ntry < 10 and savedOK == False:
        ntry = ntry+1
        savedOK = xlApp.Application.Run('VytvorCSVzNazvu',tseries,database,file_temp,fileCSV,sheetCSV,fileSlovnik_name,PrekladIn,sdate,edate,INTforecast,DBexistujici)
#    print('Pocet pokusu volani makra VytvorCSVzNazvu DB410 - ',ntry)       
#    print(' ')
    xlApp.Workbooks(1).Close(SaveChanges=0)
    xlApp.Application.Quit()
    time.sleep(0.25)    
    del xlApp
    win32com.client.pythoncom.CoUninitialize()              
           
    if savedOK:
        outData = pd.read_csv(fileCSV,encoding='latin1')
        outData['novy_index']=None
        outData['novy_index']=pd.DatetimeIndex(convertIrisDates2Pandas(outData['Unnamed: 0'].values))
        outData.set_index('novy_index',inplace=True)
        del outData['Unnamed: 0']
        outData=outData.apply(pd.to_numeric, errors='coerce')
        #for row in outData:
         #   outData.row = pd.to_numeric(outData.row, errors='coerce')        
    else:
        outData = None
        print('Casove rady se nepodarilo vyexportovat.')
    
    return outData        
       
def createCSVfromList(file_setup,
                      fileCSV,
                      sheetCSV,                      
                      sdate=1996,
                      edate=2025,
                      INTforecast=False,
                      DBexistujici=False,                      
                      fileSlovnik_name='XXXaaaXXX',
                      PrekladIn='XXXaaaXXX',
                      file_temp=os.path.abspath('tempData.xlsx'),
                      filespec=os.path.abspath(os.path.dirname(__file__)) + '\\Makra.xlsm'):
    '''
    This function open makro file filespec and run in it macro VytvorCSVzListu, which needs :
    string file_setup - file with list of time series from Database410 
    string file_temp - temporary file co extract data from Database410
    string fileCSV - output csv file
    string fileSlovnik_name - dictionary file
    string PrekladIn - sheet name in dictionary file
    string sdate - starting year '1996'
    string edate - ending year
    all paths must be absolute!!!
    Jan 2015 Frantisek Kopriva    
    '''
    '''
    ukazka pouziti
    souborMakra=os.path.abspath(r'..\..\tools\Makra.xlsm');
    fileSlovnik_name = os.path.abspath(r'..\..\tools\PrekladFcastBookNewDB.xlsx');    
    PrekladIn = 'PrekladIn';
    path_opt.indata_dir       = r'..\database\Input-data';
    path_opt.outdata_dir      = r'..\database\Output-data';    
    allOK = createCSVfromList(souborMakra,...
                  os.path.abspath(r'..\..\tools\XLSseznamy\HistcoreGDPseznam.xlsx'),...
                  os.path.abspath([path_opt.indata_dir '\tempData.xlsx']),...
                  os.path.abspath(histdir + r'csv\GDP.csv'),'Database_Q',...
                  fileSlovnik_name, PrekladIn,...
                  '1996','2020');    
    '''
    if not ':' in fileCSV:
        if not '//' in fileCSV:
            fileCSV = os.path.abspath(fileCSV)      
    win32com.client.pythoncom.CoInitializeEx(win32com.client.pythoncom.COINIT_APARTMENTTHREADED )
    #ppt = win32com.client.gencache.EnsureDispatch('PowerPoint.Application')
    xlApp = win32com.client.Dispatch('Excel.Application')
    xlApp.Visible = XLAPP_VISIBLE
    time.sleep(0.1)
    xlApp.Workbooks.Open(Filename=filespec,ReadOnly=1)
    ntry = 0
    savedOK = False
    while ntry < 50 and savedOK == False:
        ntry = ntry+1
        savedOK = xlApp.Application.Run('VytvorCSVzListu',file_setup,file_temp,fileCSV,sheetCSV, fileSlovnik_name,PrekladIn, sdate, edate, INTforecast,DBexistujici) 
#    print('Pocet pokusu volani makra VytvorCSVzListu DB410 - ',ntry)        
    xlApp.Workbooks(1).Close(SaveChanges=0)
    xlApp.Application.Quit()
    time.sleep(0.1)
    
    del xlApp
    win32com.client.pythoncom.CoUninitialize()
    
    if savedOK:  
        outData = pd.read_csv(fileCSV)
        outData['novy_index']=None
        outData['novy_index']=pd.DatetimeIndex(convertIrisDates2Pandas(outData['Unnamed: 0'].values))
        outData.set_index('novy_index',inplace=True)
        del outData['Unnamed: 0']
        outData = outData.convert_objects(convert_numeric=True)
    else:
        outData = None
        print('Casove rady se nepodarilo vyexportovat.')    
    
    return outData  
    
def createCSVfromListARAD(file_setup,
                          fileCSV,
                          sheetCSV,                      
                          sdate=1996,
                          edate=2025,                    
                          fileSlovnik_name='XXXaaaXXX',
                          PrekladIn='XXXaaaXXX',
                          file_temp=os.path.abspath('tempData.xlsx'),
                          filespec=os.path.abspath(os.path.dirname(__file__)) + '\\Makra.xlsm'):
    '''
    This function open makro file filespec and 
     run in it macro VytvorCSVzListu, which
     needs :
     string file_setup - file with list of time series from Database410 
     string file_temp - temporary file co extract data from Database410
     string fileCSV - output csv file
     string fileSlovnik_name - dictionary file
     string PrekladIn - sheet name in dictionary file
     string sdate - starting year '1996'
     string edate - ending year
     all paths must be absolute!!!
     Jan 2014 Frantisek Kopriva    
    '''
    pass
#    if not ':' in fileCSV:
#        if not '//' in fileCSV:
#            fileCSV = os.path.abspath(fileCSV)      
#    win32com.client.pythoncom.CoInitializeEx(win32com.client.pythoncom.COINIT_APARTMENTTHREADED )
#    #ppt = win32com.client.gencache.EnsureDispatch('PowerPoint.Application')
#    xlApp = win32com.client.Dispatch('Excel.Application')
#    xlApp.Visible = XLAPP_VISIBLE
#    time.sleep(0.1)
#    xlApp.Workbooks.Open(Filename=filespec,ReadOnly=1)
#    savedOK = xlApp.Application.Run('VytvorCSVzListuARAD',file_setup,file_temp,fileCSV,sheetCSV, fileSlovnik_name,PrekladIn, sdate, edate)
#    xlApp.Workbooks(1).Close(SaveChanges=0)
#    xlApp.Application.Quit()
#    
#    del xlApp
#    win32com.client.pythoncom.CoUninitialize()
#    
#    if savedOK:  
#        outData = pd.read_csv(fileCSV)
#        outData['novy_index']=None
#        outData['novy_index']=pd.DatetimeIndex(convertIrisDates2Pandas(outData['Unnamed: 0'].values))
#        outData.set_index('novy_index',inplace=True)
#        del outData['Unnamed: 0']
#        outData = outData.convert_objects(convert_numeric=True)
#    else:
#        outData = None
#        print('Casove rady se nepodarilo vyexportovat.')    
#    
#    return outData  
      
def exrate_excelmakro(ctvrtleti=1,rok=2015,fixvalue=False):
    
    if ctvrtleti == 1:
      startDate = "1.1." + str(rok)
      endDate = "31.3." + str(rok)
    elif ctvrtleti == 2:
      startDate = "1.4." + str(rok)
      endDate = "30.6." + str(rok)
    elif ctvrtleti == 3:
      startDate = "1.7." + str(rok)
      endDate = "30.9." + str(rok)
    elif ctvrtleti == 4:
      startDate = "1.10." + str(rok)
      endDate = "31.12." + str(rok)

    url = 'http://www.cnb.cz/miranda2/m2/cs/financni_trhy/devizovy_trh/kurzy_devizoveho_trhu/vybrane.html?mena=EUR&od=%s&do=%s' %(startDate,endDate)
    page=requests.get(url)
    tree = html.fromstring(page.text)
    table = tree.xpath('//table')[0]   
    
    fieldnames=[]
    for th in table.xpath('tr/th'):
        mystring = th.text_content().strip()
        mystring = mystring.split()
        mystring = [mystr.rstrip() for mystr in mystring]
        mystring = ' '.join(mystring)
        fieldnames.append(mystring)
    
    data=[]
    for tr in table.xpath('tr'):
        radek={}
        for i,td in enumerate(tr.xpath('td')):
            if i==0:
                radek[fieldnames[i]]= datetime.datetime.strptime(td.text_content().strip(),'%d.%m.%Y')
            elif i==1:
                radek[fieldnames[i]]= float(td.text_content().strip().replace(',','.'))
        if len(radek)>0:
            data.append(radek)

    df = pd.DataFrame(data)
    df.set_index('Datum',inplace = True)
    if len(df)<63:
        if fixvalue:
            brange = pd.bdate_range(start = df.Kurz.index.values[-1]+1,end =datetime.datetime.strptime(endDate,'%d.%m.%Y'), tz='Europe/Prague')
            df = df.append(pd.DataFrame(index = brange, data={'Kurz':[df.Kurz.values[-1]]*len(brange)}))
    exrate_avg = df.Kurz.sum()/df.Kurz.count()            
    
    return exrate_avg, df
    

def pribor_excelmakro(ctvrtleti=1,rok=2015,fixvalue=False):
    
    url = 'http://www.cnb.cz/cs/financni_trhy/penezni_trh/pribor/vybrane.jsp?term=3M&year=%s&format=HTML' %(rok)
    page=requests.get(url)
    tree = html.fromstring(page.text)
    table = tree.xpath('//table')[0]   
    
    fieldnames=[]

    
    data=[]
    for j,tr in enumerate(table.xpath('tr')):
        radek={}
        for i,td in enumerate(tr.xpath('td')):
            if j==0:
                mystring = td.text_content().strip()
                mystring = mystring.split()
                mystring = [mystr.rstrip() for mystr in mystring]
                mystring = ' '.join(mystring)
                fieldnames.append(mystring)        
            else:
                if i==0:
                    print(td.text_content().strip())
                    radek[fieldnames[i]]= datetime.datetime.strptime(td.text_content().strip(),'%d.%m.%Y')
                elif i>0:
                    radek[fieldnames[i]]= float(td.text_content().strip().replace(',','.'))
        if len(radek)>0:
            data.append(radek)

    df = pd.DataFrame(data)
#    print(df)
    df.set_index('Datum',inplace = True)
    
    if ctvrtleti == 1:
      startDate = str(rok) + '-01-01'
      endDate = str(rok) + '-03-31'
    elif ctvrtleti == 2:
      startDate = str(rok) + '-04-01'
      endDate = str(rok) + '-06-30'
    elif ctvrtleti == 3:
      startDate = str(rok) + '-07-01'
      endDate = str(rok) + '-09-30'
    elif ctvrtleti == 4:
      startDate = str(rok) + '-10-01'
      endDate = str(rok) + '-12-31'
      
    df = df[startDate:endDate]
    print(df)
    if len(df)<63:
        if fixvalue:
            brange = pd.bdate_range(start = df.PRIBOR.index.values[-1]+1,end =datetime.datetime.strptime(endDate,'%Y-%m-%d'), tz='Europe/Prague')
            df = df.append(pd.DataFrame(index = brange, data={'PRIBOR':[df.PRIBOR.values[-1]]*len(brange)}))
 
    pribor_avg = df.PRIBOR.sum()/df.PRIBOR.count()
    
    
    return pribor_avg, df
        
def prevedXLSdoCSV_excelmakro(fileVSTUP,fileVYSTUP):
    df = pd.read_excel(fileVSTUP)
    df.to_csv(fileVYSTUP,index = False)
#function ex_rate=exrate_excelmakro(filespec,fileVSTUP,fileVYSTUP)
#%Open a COM server on Matlab
#excel = actxserver('Excel.Application');
#Workbook = excel.Workbooks.Open(filespec);
#excel.Run('PrevedFILExlsDOcsv',fileVSTUP,fileVYSTUP);
#excel.ActiveWorkbook.Close(false);
#excel.Quit;
#disp(['File saved.']);
#return   
    
def prevedCSVdoXLS_excelmakro(fileVSTUP,fileVYSTUP):
    df = pd.read_csv(fileVSTUP)
    df.to_excel(fileVYSTUP,index = False)    