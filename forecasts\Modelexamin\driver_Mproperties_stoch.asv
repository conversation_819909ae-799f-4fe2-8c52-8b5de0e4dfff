%% This script solves model and show properties of the model
% The computation of FEVD requires a non-corelated structural shocks,
% correct for this when running with correlations
% F. Brazdik

%% Prepare environemt
clear all; close all;

disp([sprintf('\n'), 'DRIVER_M-PROPERTIES_STOCH']);

if isempty(strfind(path, ['\Utilities-extra\_SPECTRAL\tools', pathsep]))
    addpath('..\..\Utilities-extra\_SPECTRAL\tools')
end

%% set the report parameters
% Set options
% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('..\baseline\GLOBALSETTINGS.mat');
disp(['Report: ', FB_ID]);
% load settings
settings = load_settings_june(FB_ID);

settings.acforder = 1;
settings.freqstep = 0.05;
settings.reporfmt = 'pdf';
settings.shock_num = 6;
settings.fevdperiods = 32;

%% Load models
load([settings.outdata_dir '\model_november_free_usdeur-kalman-fb.mat']);
mm{1}=m;

load([settings.outdata_dir '\model_november_free-kalman-fb.mat']);
mm{2}=m;

% load([settings.outdata_dir '\develop_FB_simple-model-fb.mat']);
% mm{3}=m;

mm_labels ={'USDEUR', 'Nov 23', 'Param_AR'};

%% Prepare data
%transform data into model units
bs=dbload([settings.outdata_dir '\model_november_free-pre-filter-fb.csv']);
d_bs = bs*dbnames(bs,'NameFilter','obs(.*)');

d_o.dot_y_star = exp(d_bs.obs_Y_STAR/100 - d_bs.obs_Y_STAR{-1}/100);
d_o.y_star_gap = exp(d_bs.obs_Y_STAR_GAP/100); % gap a trend mozna nezna stary model
%d_o2.dot_y_star_trend = exp(d_bs2.obs_Y_STAR_TREND/100 - d_bs2.obs_Y_STAR_TREND{-1}/100);

d_o.i_star = d_bs.obs_I_STAR/400+1; 
d_o.i_star_eq = d_bs.obs_I_STAR_EQ/400+1; 
d_o.i_star_eu = d_bs.obs_I_STAR_EU/400+1; 

d_o.usdeur = exp(d_bs.obs_USDEUR/100);

%d_o2.dot_pstar_energy_tilde = exp(d_bs2.obs_PSTAR_ENERGY_TILDE/100 - d_bs2.obs_PSTAR_ENERGY_TILDE{-1}/100);
d_o.dot_pstar_other_tilde = exp(d_bs.obs_PSTAR_OTHER_TILDE/100 - d_bs.obs_PSTAR_OTHER_TILDE{-1}/100);
d_o.dot_pstar_tilde = exp(d_bs.obs_PSTAR_TILDE/100 - d_bs.obs_PSTAR_TILDE{-1}/100);
d_o.dot_cpi_star_tilde = exp(d_bs.obs_CPI_STAR_TILDE/100 - d_bs.obs_CPI_STAR_TILDE{-1}/100);

listvar = dbnames(d_o); %alter listvar order to change the normalization variable in reports
names = listvar;


%% MODELs

freq = 0:settings.freqstep:pi;
for ii=1:length(mm)
    
    %acf
    [C{ii},R{ii},list{ii}] = acf(mm{ii},'order',settings.acforder,'select',listvar);
    %spd
    [S{ii},D{ii}] = xsf(mm{ii},freq,'select',listvar);
    [Cf{ii},Rf{ii},listf{ii}] = acf(mm{ii},'order',1,'filter','1600 / (1600 + 1/(1-L)^2 * 1/(1-1/L)^2)','select',listvar);
    
end;

vx = VAR();
[w,data] = estimate(vx,d_o,dbnames(d_o),Inf); % inf = refers to range
[Cd,Rd] = acf(w,'order',settings.acforder); % autocovariance and autocorrelation function
[Sd,Dd] = xsf(w,freq); % power spectrum and spectral density function
[Cdf,Rdf] = acf(w,'order',1,'filter','1600 / (1600 + 1/(1-L)^2 * 1/(1-1/L)^2)');


%% reporting
xx = report.new('Stochastic properties','visible',false);


%% Visualise ACF 

presetfigure('Visible','off');

subplot(2,2,1);
plotmat(Rd(:,:,1),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
grid('on');
title('Data');
for ii=1:length(mm)
    subplot(2,2,ii+1);
    plotmat(R{ii}(:,:,1),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
    grid('on');
    title(['Model ' mm_labels{ii}]);
end;
xx.figure('Contemporaneous correlations',gcf);

presetfigure('Visible','off');
subplot(2,2,1);
plotmat(Rd(:,:,2),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
grid('on');
title('Data');
for ii=1:length(mm)
    subplot(2,2,ii+1);
    
    plotmat(R{ii}(:,:,2),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
    grid('on');
    title(['Model ' mm_labels{ii}]);
end;
xx.figure('First-order correlations',gcf);

presetfigure('Visible','off');
subplot(2,2,1);
plotmat(Rdf(:,:,1),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
grid('on');
title('Data');
for ii=1:length(mm)
    subplot(2,2,ii+1);
    plotmat(Rf{ii}(:,:,1),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
    grid('on');
    title(['Model' mm_labels{ii}]);
end;
xx.figure('Contemporaneous correlations (HP filtered)',gcf);

presetfigure('Visible','off');
subplot(2,2,1);
plotmat(Rdf(:,:,2),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
grid('on');
title('Data');
for ii=1:length(mm)
    subplot(2,2,ii+1);
    plotmat(Rf{ii}(:,:,2),'rownames',names,'colnames',strread(num2str(1:length(listvar)),'%s'),'scale',1);
    grid('on');
    title(['Model' mm_labels{ii}]);
end;
xx.figure('First-order correlations (HP filtered)',gcf);

%% Forecast error variance decomposition

for ii=1:length(mm)
    [A,B,list,a,b] = fevd(mm{ii},1:settings.fevdperiods);
    FEVD{ii}=b;
end;


for i = 0 : length(listvar)-1
    if mod(i,3)==0
        presetfigure('Visible','off');
    end;
    for ii = 0:length(mm)-1
        subplot(3,length(mm),length(mm)*mod(i,3)+ii+1)
        x = 100*FEVD{ii+1}.(listvar{i+1});
        [x,index] = sort(x,'sumabs');
        x = [x{:,1:settings.shock_num},sum(x{:,settings.shock_num+1:end},2)];
        area(x);
        nx=index(1:settings.shock_num);
        elist = get(mm{ii+1},'elist');
        leg=strrep(elist(nx),'_','\_');
        l=legend([leg,'Others'],'Location','East');
        set(l,'FontSize',6);
        grid on
        axis('tight');
        title([mm_labels{ii+1} ' - ',names{i+1}]);
        xlabel('Quarters ahead');
        ylabel('Percent');
    end;
    if mod(i,3)==2
        xx.figure('FEVD - relative',gcf);
        xx.pagebreak();
    end;
    if mod(i,3)==1 && i ==length(listvar)
        xx.figure('FEVD - relative',gcf);
        xx.pagebreak();
    end;
    
end

xx.publish([settings.outreport_dir '\' 'Stochprops-report-' settings.headline_prefix '-DATAModel' settings.reporfmt]);
