function [contrib, groups_nms] = report_decomposition(decomp, sr_opt, plot_range, varargin)

default = {...
    'report',				1, ...			 %
    'graphs',				1, ...		  	 %
    'grip',					0, ...           %
    'xls',					0, ...           %
    'grouping_type',		1, ...
    'autogroups',			1, ...
    'automatic_groups'		1, ...
    'firstgroups',			[], ...
    'output_id',			'', ...
    'language',				'EN', ...
    'decomp_list',			{},...
    'zz_transform',			1, ...
    'graph_style',			1, ...
    'grip_type',            'ppt' , ...
    'grip_lim',             1,  ...
    'print_replace_all',    false, ...
    'displaygroups',        true, ...
    'click_legend',			0, ...
    'click_legend_order',	0, ...
    'custom_datatips',      0, ...
    'manual_groups_names',	[], ...
    'manual_groups',		[], ...
    'export_graphs',        0, ...
    'export_path',          [] ...
    };

%--parse options using IRIS --%
options = passopt(default, varargin{1:end});

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% CREATE OUTPUT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

tic;

stack = dbstack();% stack control

if regexp(stack(end).name,'^decomp_explode')
    groups_nms = options.manual_groups_names;
    groups = options.manual_groups;
else
    % automatic groups
    if options.grouping_type
        groups_nms = [];
        groups = [];
    else
        switch options.automatic_groups
            case 0
                options.autogroups = 'MANUAL';
            case 1
                groups_nms = [];
                groups = [];
            case 2
                options.autogroups = 0;
            case 3
                options.autogroups = 0;
            case 4
                options.autogroups = 0;
            case 5
                options.autogroups = 'ALL_VARIABLES';
                pom_names = dbnames(decomp.d_new,'nameFilter',['(^obs_|^eps_)\w+']);
                groups = {};
                groups_nms = [];
                for aa=1:length(pom_names)
                    groups = {groups{:},{['\w+(__' pom_names{aa} ')$']}};
                    groups_nms = [groups_nms; rename_factor(pom_names(aa),decomp.d_new,'CZ'), rename_factor(pom_names(aa),decomp.d_new,'EN')];
                end
                x = groups(:);
        end
    end
end

% create groups
if options.grouping_type	% Zuzana
    disp('Setting groups...');
    [groups, groups_nms, groups4display] = fill_groups( ...
        options.autogroups, decomp.input_datenames, sr_opt.new.start_pred, ...
        'language', options.language, ...
        'displaygroups', false, ...
        'manual_groups_names',  groups_nms, ...
        'manual_groups',    groups);
else						% Franta
    [groups, groups_nms] = prepare_groups(options.autogroups, ...
        decomp.input_datenames, ...
        'language', options.language, ...
        'displaygroups',false, ...
        'manual_groups_names',  groups_nms, ...
        'manual_groups',    groups);
    groups4display = groups;
    
    if options.print_replace_all
        for i=1:length(groups4display)
            pom1=groups4display{i};
            j=1;
            while j<=length(pom1)
                if all(isstrprop(pom1{j}(1:4),'digit'))
                    pom2         = pom1{j}(7:end);
                    pom1{j}      = strrep(pom1{j},pom1{j}(1:6), 'all');
                    index_pom    = strncmp(cellfun(@fliplr,pom1,'UniformOutput',false),fliplr(pom2),length(pom2));
                    index_pom(j) = 0;
                    index_pom    = index_pom<1;
                    pom1=pom1(index_pom);
                end
                j=j+1;
            end
            groups4display{i}=pom1;
        end
    end
end

% display on screen
if options.displaygroups
    displaygroups(groups4display,groups_nms,options.language);
end

%if strcmp(stack(end).name,'decomp_explode') || strcmp(stack(end).name,'decomp_explode_newold')
if regexp(stack(end).name,'^decomp_explode')
    fprintf(2,'Regrouping the decomposition...');
else
    printgroups2file(groups4display, groups_nms);
end

contrib = reducedecomp(decomp, groups, groups_nms,...
    'language', options.language);
% pridan napocet dekompozice levelu kurzu
if any(strcmp(options.decomp_list,'ne_zz_s')) || options.grip==2
    [kurz_prispevky kurz_truediff kurz_new kurz_old]= decomp_czk_level(contrib, sr_opt);
    contrib.store_matrix(:,:,end+1)=kurz_prispevky;
    contrib.truediffmat(:,end+1) = kurz_truediff;
    contrib.d_new.ne_zz_s = tseries(contrib.decomp_range,kurz_new);
    contrib.d_old.ne_zz_s = tseries(contrib.decomp_range,kurz_old);
    contrib.endog_vars={contrib.endog_vars{:},'ne_zz_s'};
end

%if ~strcmp(stack(2).name,'decomp_explode') && ~strcmp(stack(2).name,'decomp_explode_newold')
if ~regexp(stack(2).name,'^decomp_explode')
    disp(['Saving to ' sr_opt.cmp.outdata_dir '\' sr_opt.cmp.cmpreport_prefix ...
        '-contrib' options.output_id '.mat']);
    save([sr_opt.cmp.outdata_dir '\' sr_opt.cmp.cmpreport_prefix ...
        '-contrib' options.output_id],'contrib');
end

if ~isempty(options.decomp_list) && ...
        (options.report || options.graphs || options.grip || options.xls)
    % set groups and groups names
    % draw, report
    if options.graphs
        graph_decomp(contrib, options.decomp_list, ...
            'zz_transform', options.zz_transform, ...
            'plot_rng', plot_range, ...
            'firstgroups', options.firstgroups, ...
            'graph_style', options.graph_style, ...
            'groups', groups, ...
            'groups_names', groups_nms, ...
            'language',options.language, ...
            'click_legend',options.click_legend, ...
            'click_legend_order',options.click_legend_order, ...
            'decomp_whole',decomp, ...
            'custom_datatips', options.custom_datatips, ...
            'export_graphs',options.export_graphs, ...
            'export_path',options.export_path);
    end
    if options.grip==1
        draw_grip(contrib, sr_opt.new.start_pred,options.autogroups, ...
            'language', options.language, ...
            'type',	options.grip_type, ...
            'lim',	options.grip_lim, ...
            'flname',[sr_opt.cmp.outgraph_dir ,'\', sr_opt.cmp.cmpreport_prefix]);
    elseif options.grip==2
        draw_grip_exrate(contrib, sr_opt.new.start_pred, sr_opt, ...
            'language', options.language, ...
            'type',	options.grip_type, ...
            'lim',	options.grip_lim, ...
            'flname',[sr_opt.cmp.outgraph_dir ,'\', sr_opt.cmp.cmpreport_prefix]);
    end
    if options.report
        report_decomp(contrib, options.decomp_list, ...
            'zz_transform', options.zz_transform, ...
            'firstgroups', options.firstgroups, ...
            'plot_rng', plot_range, ...
            'heading', ['Forecast Decomposition: ' sr_opt.cmp.cmpreport_prefix ' ' options.output_id], ...
            'fname', [sr_opt.cmp.outreport_dir '\' sr_opt.cmp.cmpreport_prefix ...
            '-decomposition' options.output_id]);
    end
    if options.xls
        export2xls(contrib, options.decomp_list, groups_nms, ...
            'plot_rng', plot_range, ...
            'zz_transform', options.zz_transform, ...
            'fname', [sr_opt.cmp.outdata_dir '\' sr_opt.cmp.cmpreport_prefix ...
            '-decomposition' options.output_id]);
    end
end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

toc

end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function name = rename_factor(name,d,language)
% Choose opt:
% 1: replace '_' with ' '
% 2: replace with comment from dataset
% 3: different language versions
% otherwise: keep original name

opt = 3;

switch opt
    case 1
        name = strrep(name,'_',' ');
    case 2
        name = comment(d.(char(name)));
    case 3
        switch upper(language)
            case 'CZ'
                name = strrep(name,'_',' ');
            case 'EN'
                name = comment(d.(char(name)));
        end
end

end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
