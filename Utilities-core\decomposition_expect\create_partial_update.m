function [d_out] = create_partial_update(contrib, ngroup_update, sr_opt, ...
	d_pure_new, d_pure_old, varargin)

disp('Creating partially updated simulation...');

if nargin > 5
	d_new_orig = varargin{1};
end

load(sr_opt.new.model_name);
SS = load(sr_opt.new.ss_name);
load(sr_opt.old.plan_name);
load(sr_opt.old.plan_surprise_name);

if isequal(ngroup_update,1)
    h = changedata_2015sz07(sr_opt.old.histcore_name,sr_opt.old);
else
    h = changedata_2017sz02(sr_opt.new.histcore_name,sr_opt.new);
end

g3_update = d_pure_old;
old_endogvars = fieldnames(d_pure_old);
for ivar = 1 : length(contrib.endog_vars)
    if any(strcmp(regexprep(contrib.endog_vars{ivar},'_exp',''),old_endogvars))
        for ix = 1 : ngroup_update
    % 	for ix = 1:length(contrib.input_datenames)-1
            if ivar>(length(contrib.endog_vars)/2)
                if contrib.islog.(contrib.endog_vars{ivar-length(contrib.endog_vars)/2})  
                    g3_update.(contrib.endog_vars{ivar-length(contrib.endog_vars)/2})= ...
                        1i.*imag(g3_update.(contrib.endog_vars{ivar-length(contrib.endog_vars)/2})) + ...
                        real(g3_update.(contrib.endog_vars{ivar-length(contrib.endog_vars)/2})) .*  ...
                        exp(tseries(contrib.decomp_range,contrib.store_matrix(:,ix,ivar)));             
                else
                    g3_update.(contrib.endog_vars{ivar-length(contrib.endog_vars)/2})= ...
                        g3_update.(contrib.endog_vars{ivar-length(contrib.endog_vars)/2}) + ...
                        tseries(contrib.decomp_range,contrib.store_matrix(:,ix,ivar));
                end    
            else
                if contrib.islog.(contrib.endog_vars{ivar})
                    g3_update.(contrib.endog_vars{ivar})= ...
                        real(g3_update.(contrib.endog_vars{ivar})) + ...
                        1i.*imag(g3_update.(contrib.endog_vars{ivar})) .*  ...
                        exp(tseries(contrib.decomp_range,contrib.store_matrix(:,ix,ivar)));
                else
                    g3_update.(contrib.endog_vars{ivar})= ...
                        g3_update.(contrib.endog_vars{ivar}) + ...
                        tseries(contrib.decomp_range,1i*contrib.store_matrix(:,ix,ivar));
                end                 
            end 
        end
    else
        disp('Warning: tu nema skocit');        
    end
end

%% Create database

g3_update = dbextend(d_pure_new,g3_update);

eps_names = dbnames(g3_update,'nameFilter','^eps_\w*');
d = dbfun(@(x) imag(x),g3_update);
db_eps = g3_update*eps_names;
db_eps = dbfun(@(x) imag(x)+real(x),db_eps);
d = dbextend(d,db_eps);
d = dbfun(@(x,y) comment(x,comment(y)),d,d_pure_new);

fcast_plan_report = fcast_plan_join(fcast_plan, fcast_plan_surprise, m, sr_opt.old.comprng);

if nargin > 5
	d = dbextend(dbclip(d_new_orig,sr_opt.old.hrng),d);
end

d_gdp   = make_accum(d, h, SS.filtering, sr_opt.old);
d_model = make_transformations(d_gdp, SS, false);
d_model = make_transformations_mc(d_model, SS);
if isequal(ngroup_update,1)
	dd = make_transformations_present_2015sz07(d_model, h, sr_opt.old, false, ...
		'fcast_plan', fcast_plan_report, 'fcast_link', 'hp');
else
	dd = make_transformations_present(d_model, h, sr_opt.old, false, ...
		'fcast_plan', fcast_plan_report, 'fcast_link', 'hp');
end


% if nargin > 5
% 	exo_list = {'ne_zz_dot_pREG','ne_zz_dot_pREG4', ...
% 				'ne_zz_dot_pREG_tax','ne_zz_dot_pREG_tax4', ...
% 				'ne_zz_dot_g','ne_zz_dot_g4','ne_zz_g', ...
% 				'ne_zz_dot_nom_g','ne_zz_dot_nom_g4','ne_zz_nom_g', ...
% 				'ne_zz_dot_pG','ne_zz_dot_pG4','ne_zz_pG', ...
% 				'ne_zz_dot_n_star','ne_zz_dot_n_star4', ...
% 				'ne_zz_dot_p_star_tilde','ne_zz_dot_p_star_tilde4', ...
% 				'ne_zz_i', ...
% 				};
% 	dd_aux = d_new_orig*exo_list;
% 	dd = dbextend(dd, dd_aux);
% end

d_pom      = dbfun(@(x,y) comment(x,comment(y)),dd,d_pure_new);
d_out		= dbextend(dd,d_pom);

%% Save green

if ngroup_update>1
    dbsave(d_out, [sr_opt.new.outdata_dir '\' sr_opt.new.report_prefix '-GREEN-forecast.csv'],Inf,'format','%.16e');
    dbsave(g3_update, [sr_opt.new.outdata_dir '\' sr_opt.new.report_prefix '-GREEN-forecast_pure.csv'],Inf,'format','%.16e');
    % !!! adjust sr_ID for old - add new extend
    load(sr_opt.new.ID_name);
    sr_ID.extend.green = [sr_opt.new.report_prefix '-GREEN'];
    save(sr_opt.new.ID_name,'sr_ID');

    disp([sprintf('\n') 'Following extend was added to sr_ID: ' sr_opt.new.report_prefix]);
    disp(['		''green'':	''' [sr_opt.new.report_prefix '-GREEN'] '''']);
end
