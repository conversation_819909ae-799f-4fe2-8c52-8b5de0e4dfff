function graph_decomp(decomp, decomp_list, varargin)
%----------------------------------------------------------------%
% inputs:
% decomp		-- decomposition of two forecasts (including filters)
% decomp_list	-- endogenous variables to report
%----------------------------------------------------------------%
% @ last revision: za, CNB, Apr 2012

%---defaults and varargin---%
default =	{	...
    'plot_rng',			decomp.decomp_range,  ...
    'zz_transform', 	false, ...
    'firstgroups',          [], ...
    'graph_style',          1, ...
    'groups',           {}, ...
    'groups_names',      {}, ...
    'click_legend',       0, ...
    'click_legend_order', 0, ...
    'export_graphs',  0, ...
    'export_path', [], ...
    'custom_datatips', 0 ...
    'decomp_whole', [] ...
    };

options = passopt(default,varargin{1:end});

% adjust plot_rng and others
options.plot_rng = max(decomp.decomp_range(1),options.plot_rng(1)) : ...
    min(decomp.decomp_range(end),options.plot_rng(end));
if ~isempty(decomp.hist_rng)
    hist_rng = max(decomp.hist_rng(1),options.plot_rng(1)) : ...
        min(decomp.hist_rng(end),options.plot_rng(end));
end
if ~isempty(decomp.trans_rng)
    trans_rng = max(decomp.trans_rng(1),options.plot_rng(1)) : ...
        min(decomp.trans_rng(end),options.plot_rng(end));
end
if ~isempty(decomp.fut_rng)
    fut_rng = max(decomp.fut_rng(1),options.plot_rng(1)) : ...
        min(decomp.fut_rng(end),options.plot_rng(end));
end

% check decomp_list
[~,all_loc] = ismember('all',decomp_list);
[~,allexp_loc] = ismember('all_exp',decomp_list);
if any(allexp_loc)
    decomp_list	= decomp.endog_vars;
elseif any(all_loc)
    index		= regexp(decomp.endog_vars,'_exp');
    index		= cellfun(@isempty,index);
    decomp_list	= decomp.endog_vars(index);
end
[~,decomp_loc] = ismember(decomp_list,decomp.endog_vars);
if ~all(decomp_loc)
    disp([decomp_list(find(decomp_loc==0)) ' not found...']);
    error('Wrong decomp_list!');
end



% prepare tseries
varname = cell(length(decomp_loc),1);
varnamecom = cell(length(decomp_loc),1);
um = cell(length(decomp_loc),1);
truediff = cell(length(decomp_loc),1);
checksum = cell(length(decomp_loc),1);
loc_list = [];
options.decomp_whole.endog_vars = {};
for ivar = 1:length(decomp_loc)
    if ~isempty(options.decomp_whole)
        options.decomp_whole.endog_vars{ivar} = decomp.endog_vars{decomp_loc(ivar)}; 
    end
    loc_list = [loc_list, decomp_loc(ivar)];
    varname{ivar} = decomp.endog_vars{decomp_loc(ivar)};
    varnamecom{ivar} = comment(decomp.d_new.(varname{ivar}));
    um{ivar} = tseries(options.plot_rng, ...
        decomp.store_matrix(options.plot_rng-decomp.decomp_range(1)+1,:,decomp_loc(ivar)));
    truediff{ivar} = tseries(options.plot_rng, ...
        decomp.truediffmat(options.plot_rng-decomp.decomp_range(1)+1,decomp_loc(ivar)));
    checksum{ivar} = sum(um{ivar},2);
        if isequal(varname{ivar}, 'dot_cpi4')
        varnamecom{ivar} = 'MP inflace mzr.';
        end

        if isequal(varname{ivar}, 'i')
        varnamecom{ivar} = 'Sazby 3M Pribor p.a. v p.b.';
        end
    if options.zz_transform
        if strmatch(varname{ivar}(end),'4') % annual or quarter data
            annual = 1;
        else annual = 4;
        end
        um{ivar} = 100*annual*um{ivar};
        truediff{ivar} = 100*annual*truediff{ivar};
        checksum{ivar} = 100*annual*checksum{ivar};
    end
end

if ~isempty(options.decomp_whole)
    options.decomp_whole.store_matrix = options.decomp_whole.store_matrix(options.plot_rng-decomp.decomp_range(1)+1,:,loc_list);
    options.decomp_whole.truediffmat = options.decomp_whole.truediffmat(options.plot_rng-decomp.decomp_range(1)+1,loc_list);
    options.decomp_whole.decomp_range = options.plot_rng-decomp.decomp_range(1)+1;
end

% draw
for ivar = 1 : length(decomp_list)
    tser_tmp = um{ivar};
    factor_names = decomp.input_datenames;
    if ~isempty(options.firstgroups) && options.firstgroups>0
        [~,sort_index] = sort(sum(abs(tser_tmp),1),'descend');
        if length(factor_names)>options.firstgroups
            tser_tmp = tseries(options.plot_rng,[tser_tmp(:,sort_index(1:options.firstgroups-1)) ...
                sum(tser_tmp(:,sort_index(options.firstgroups:end)),2)]);
            factor_names = [factor_names(sort_index(1:options.firstgroups-1)); {'Rest'}];
        else
            tser_tmp = tseries(options.plot_rng,tser_tmp(:,sort_index));
            factor_names = factor_names(sort_index);
        end
    end

    fig = figure('Name',varname{ivar});
    if options.graph_style
        conbar(options.plot_rng,tser_tmp);
        lg = legend(strrep(factor_names,'_','\_'),'location','best','fontSize',12);
        if ~isempty(intersect(decomp.hist_rng,options.plot_rng))
            highlight(intersect(hist_rng,options.plot_rng));
        end
        if ~isempty(intersect(decomp.trans_rng,options.plot_rng))
            highlight(intersect(trans_rng,options.plot_rng));
        end
        if ~isempty(intersect(decomp.fut_rng,options.plot_rng))
            highlight(intersect(fut_rng,options.plot_rng));
        end
        hold on;
        plot(options.plot_rng,[truediff{ivar} checksum{ivar}]);
        hold off;
    else
        strnms = '';
        for jcycle = 1 : length(factor_names)
            strnms = [strnms,'''', factor_names{jcycle},''','];
        end
        str_pre_process = ['lg = legend({', strnms, '},''Location'',''EastOutside'');'];
		drawbar(tser_tmp,options.plot_rng,'type','ppt2','endhist',decomp.fut_rng(1),...
		'title',strrep(varnamecom{ivar},'_','\_'),...
		'pre_process',str_pre_process, 'palette',1, 'colors',15,...
		'click_legend',options.click_legend,'click_legend_order',options.click_legend_order,'decomp_whole',options.decomp_whole,'decomp_list',decomp_list,'ivar',ivar);
    end
    if options.graph_style
        qstyle(fig, prepare_style(options.graph_style));
        title(strrep(varnamecom{ivar},'_','\_'));
        improve_legend_position;
    end
    if options.export_graphs
        out_pos = get(gcf,'OuterPosition');                 % figure size setting for export to presentation
        set(gcf,'OuterPosition',[out_pos(1) out_pos(2) 0.8*1.5*out_pos(4) 1.5*out_pos(4)]);
        leg=findobj(gcf,'type','axes','tag','legend');   % legend position inside graph for presentation
        if length(leg)>1
            return
        end
        set(leg,'location','best');
%         pause
        saveas(gcf,[options.export_path,char(varname{ivar})],'fig'); % export graph to file for presentation
        saveas(gcf,[options.export_path,char(varname{ivar})],'bmp');
    end
    if options.custom_datatips
        set_decomp_datatip(tser_tmp,factor_names);
    end
end


end %-of the MAIN FUNCTION------------------------------------------------%



function sty = prepare_style(graph_style)

switch graph_style
    case 0
        sty = struct();
        sty.line.color = {'black', 'white'};
        sty.line.lineStyle = {'-'};
        sty.line.lineWidth = {1.5 , 3};
        sty.line.marker = {'none'};
        sty.highlight.facecolor = {[1.0,1.0,1.0],[.94,0.94,0.94],[0.87,0.87,0.87]};
        sty.highlightcaption.color = 'black';
        sty.highlightcaption.fontsize = 10;
    case 1
        sty = struct();
        sty.line.color = {'white', 'black'};
        sty.line.lineStyle = {'-'};
        sty.line.lineWidth = {3 , 1.5};
        sty.line.marker = {'none'};
        sty.highlight.facecolor = {[0.87,0.87,0.87],[.94,0.94,0.94],[1.0,1.0,1.0]};
        sty.highlightcaption.color = 'black';
        sty.highlightcaption.fontsize = 10;
    otherwise
        warning('Wrong graph style parameter!');
end

end % of SUB function ----------------------------------------------------%


