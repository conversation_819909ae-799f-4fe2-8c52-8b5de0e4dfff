function [is_trans,ini2obs,just_filter,obs_free,rls2shocks,obs2shocks,varargout] = ...
	restore_decomp_settings(decomp_type)

% Displays description of decomposition setting and restores
% basic decomposition parameters
% OUTPUT:	is_trans: 
%			ini2obs		: 
%			just_filter	:
%			obs_free	:
%			rls2shocks	:
%			obs2shocks	: 
% INPUT:	decomp_type : string identifier
% created:	za, Feb 2013

varargout{1} = false;

switch decomp_type
	case '111011'
		disp('filter do soku, vcetne rls');
	case '111010'
		disp('filter do observables, rls do soku');
	case '111000' 
		disp('filter do observables, vcetne rls');
	case '110111' 
		
	case '110110' 
		
	case '110100' 
		
	case '110011' 
		
	case '110010' 
		
	case '110000' 
		
	case '100000' 
		disp('standardni dekompozice forecast');
	case '011001' 
		disp('filter do soku');
	case '011000' 
		disp('filter do observables');
	case '010101' 
		
	case '010100' 
		
	case '010001' 
		
	case '010000' 
		disp('standardni GRIP');
	case '000000' 
		disp('standardni dekompozice alternativ');
	otherwise
		warning('This decomp_type string is not valid combination for decomposition!');
		varargout{1} = true;
end

is_trans		=  str2num(decomp_type(1));
ini2obs			=  str2num(decomp_type(2));
just_filter		=  str2num(decomp_type(3));
obs_free		=  str2num(decomp_type(4));
rls2shocks		=  str2num(decomp_type(5));
obs2shocks		=  str2num(decomp_type(6));

% disp(['is_trans = ' decomp_type(1)]);
% disp(['ini2obs = ' decomp_type(2)]);
% disp(['just_filter = ' decomp_type(3)]);
% disp(['obs_free = ' decomp_type(4)]);
% disp(['rls2shocks = ' decomp_type(5)]);
% disp(['obs2shocks = ' decomp_type(6)]);

end  %--- of the MAIN function -----------------------------------------------------%


