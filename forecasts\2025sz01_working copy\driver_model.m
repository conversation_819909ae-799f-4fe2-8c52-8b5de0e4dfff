%**************************************************************************
%% DRIVER_MODEL
%**************************************************************************
%yol = load('N:\novy_zahranicni_blok_do_414\model\forecasts\database\Output-data\2019sz05_fbs-settings');
% close all
clear all
disp([sprintf('\n'), 'DRIVER_MODEL']);

load('GLOBALSETTINGS.mat');
disp(['FB_ID: ', FB_ID]);
settings = load_settings(FB_ID);

%% Read model and parameters
SS = setparam_ZB_new([],settings);
SS.filtering = true;

SS = ssmodel_ZB_new(SS);

m = readmodel('../../Utilities-core/model_november/inc_g3p_ZB_new.model',SS);

%% Save

if SS.filtering
	save([settings.outdata_dir '\' settings.report_prefix '-kalman-fb.mat'], 'm');
else
	save([settings.outdata_dir '\' settings.report_prefix '-model-fb.mat'], 'm');
end

%**************************************************************************

