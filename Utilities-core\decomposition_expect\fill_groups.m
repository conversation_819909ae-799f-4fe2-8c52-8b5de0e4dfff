function [G<PERSON>UP<PERSON>, GROUPS_NMS, GROUPS4DISPLAY] = fill_groups(autogroups,datenames,sfore,varargin)

%- MAIN FUNCTION ---------------------------------------------------------%

%% parse options

default = {...
	'language',             'CZ' ...
	'displaygroups',		true ...
    'manual_groups_names',  {}, ...
    'manual_groups',        {}...  
	};

options = passopt(default, varargin{1:end});

% language
switch upper(options.language)
	case {'CZ'}
		language = 2;
	case {'EN'}
		language = 3;
end

%% split datenames
[date,type,name] = split_datenames_(datenames);

%% make groups set manually 

pattern = unique(type);
if isempty(autogroups) || isequal(autogroups,0)
    groups = [];
    groups_nms = [];
elseif isequal(autogroups,'Factors') % individual factors; grouped for all dates
	groups = cell(1,0);
	groups_nms = cell(0,3);
	isingroup = zeros(size(type));
	for j = 1:length(pattern)
		index = strmatch(pattern{j},type,'exact');
		pat_name = unique(name(index));
		groups_aux = cell(1,0);
		groups_nms_aux = cell(0,3);
		for i = 1:length(pat_name)
			iaux = strmatch(pat_name{i},name,'exact');
			iaux = intersect(iaux,index);
			grouptype = unique(type(iaux));
			isingroup(iaux) = isingroup(iaux)+ones(size(isingroup(iaux))); 
			used_datenames = (regexprep(grouptype,'(\S*)',['all__$1__' pat_name{i}]));
			groups_aux(:,i) = {findloc_datenames_(used_datenames,date,type,name,type)};
			str = strrep([pattern{j} ': ' pat_name{i}],'_','\_');
			groups_nms_aux(i,:) = {str   str  str};
		end
		aux = size(groups,2);
		groups(:,aux+1:aux+size(groups_aux,2)) = groups_aux(:,:);
		aux = size(groups_nms,1);
		groups_nms(aux+1:aux+size(groups_nms_aux,1),:) = groups_nms_aux(:,:);
	end
else
    if strcmp(autogroups,'MANUAL')
        groupstruct = options.manual_groups;
        groups_nms = options.manual_groups_names;
    else
        [groupstruct, groups_nms] = read_groups(autogroups);
    end
	[groupstruct] = replace_sfore4date(groupstruct,sfore);
	[groups,isingroup] = fillgroups_findrest_(datenames,groupstruct);
	if size(groups,2) < size(groups_nms,1)
		groups_nms = groups_nms(1:end-1,:);
	end
	if size(groups,2) > size(groups_nms,1)
		groups_nms(end+1,:) = {'rest', 'Ostatni', 'Rest'};
	end
end

check_groups_(isingroup,date,type,name);

% delete empty groups
index = [];
for i = 1:size(groups,2)
	if ~isempty(groups{:,i})
		index = [index, i];
	end
end
groups			= groups(:,index);
groups_nms		= groups_nms(index,:);
groups4display	= replace_dates4all(groups,datenames);

%% output

GROUPS			= groups;
GROUPS_NMS		= groups_nms;
GROUPS4DISPLAY	= groups4display;

% display on screen
if options.displaygroups
	displaygroups(GROUPS4DISPLAY,GROUPS_NMS,language);
end


end %- of MAIN FUNCTION --------------------------------------------------%



function [used,isingroup] = fillgroups_findrest_(datenames,groupstruct)
% fills groups with used datenames and also creates rest group when
% necessary

isingroup = zeros(size(datenames));
used = cell(1,length(groupstruct));
for i = 1:length(groupstruct)
	used{i} = unique(findloc_datenames_(groupstruct{i},datenames),'stable');
	iaux = ismember(datenames,used{i});
	isingroup(iaux) = isingroup(iaux)+ones(size(isingroup(iaux))); 
end

usedaux = [used{:}];

rest = setdiff(datenames,usedaux);
rest = rest(:)';

if ~isempty(rest)
	used{end+1} = rest;
	iaux = ismember(datenames,used{end});
	isingroup(iaux) = isingroup(iaux)+ones(size(isingroup(iaux))); 
end

end % of SUB function ----------------------------------------------------%



function [used] = findloc_datenames_(used_datenames,datenames)

index = [];
counter = 0;
unused = cell(0,1);

[date,type,name] = split_datenames_(datenames);
[date_aux,type_aux,name_aux] = split_datenames_(used_datenames);
% groupnumber = zeros(size(type));
for i = 1:length(used_datenames)
	if isequal(type_aux{i},'all')
		itypeaux = [1:length(type)]';
	else 
		itypeaux = strmatch(type_aux{i},type,'exact');
	end
	if isequal(name_aux{i},'all')
		inameaux = [1:length(name)]';
	else
		inameaux = strmatch(name_aux{i},name,'exact');
	end
	if isequal(date_aux{i},'all')
		idateaux = [1:length(date)]';
	else
		s = regexp(date_aux{i},':','split');
		dim = size(s,2);
		if isequal(dim,2)
			dat1 = s{1}; dat2 = s{2};
			s1 = regexp(dat1,'Q','split');
			s2 = regexp(dat2,'Q','split');
			rng = qq(str2num(s1{1}),str2num(s1{2})):qq(str2num(s2{1}),str2num(s2{2}));
			idateaux = [];
			for j = 1:length(rng)
				idateaux = [idateaux; strmatch(dat2str(rng(j)),date,'exact')];
			end
		else
			idateaux = strmatch(date_aux{i},date,'exact');
		end
	end
	iusedaux = intersect(intersect(itypeaux,inameaux),idateaux);
	if isempty(iusedaux)
		counter = counter+1;
		unused{counter,1} = sprintf('%s__%s__%s', char(date_aux{i}), ...
			char(type_aux{i}), char(name_aux{i}));
	else
		index = [index;iusedaux];
	end
end

used = cell(1,length(index));
for i = 1:length(index)
	used{i} = sprintf('%s__%s__%s', char(date(index(i))), ...
		char(type(index(i))), char(name(index((i)))));
end

if ~isempty(unused)
	disp('Datenames for these cathegories not found:');
	disp(unused);
end


end % of SUB function ----------------------------------------------------%



function [date,type,name] = split_datenames_(datenames)

s = regexp(datenames,'__','split');
ndn = length(datenames);
date = cell(ndn,1);
type = cell(ndn,1);
name = cell(ndn,1);
for i = 1:ndn
	date{i} = char(s{i}(1));
	type{i} = char(s{i}(2));
	name{i} = char(s{i}(3));
end

end % of SUB function ----------------------------------------------------%



function check_groups_(isingroup,date,type,name)

index0 = find(isingroup==0);
indexmore = find(isingroup>1);

datenames0 = cell(length(index0),1);
datenamesmore = cell(length(indexmore),1);

if ~isempty(index0)
	for ix = 1 : length(index0)
		datenames0{ix} = sprintf('%s__%s__%s', char(date(index0(ix))), ...
			char(type(index0(ix))), char(name(index0(ix))));
	end
end
if ~isempty(indexmore)
	for ix = 1 : length(indexmore)
		datenamesmore{ix} = sprintf('%s__%s__%s', char(date(indexmore(ix))), ...
			char(type(indexmore(ix))), char(name(indexmore(ix))));
	end
end

if size(datenames0,1)>0
	disp('These datenames are not included in any of the groups:');
	disp(datenames0);
	error('See previous comment...');
end
if size(datenamesmore,1)>0
	disp('These datenames are included in more than one group:');
	disp(datenamesmore);
	error('See previous comment...');
end

end % of SUB function ----------------------------------------------------%



function [groups4display] = replace_dates4all(groups,datenames)

[~,type,name] = split_datenames_(datenames);
typename = cell(length(datenames),1);
for ix = 1 : length(datenames)
	typename{ix} = sprintf('%s__%s', char(type{ix}), char(name{ix}));
end
pattern = unique(typename);
numgroups = length(groups);

groups4display = groups;

for ix = 1 : length(pattern)
	ind = zeros(1,numgroups);
	for ig = 1 : numgroups
		groupspec = groups4display{ig};
		for ii = 1 : length(groupspec)
			if ~isempty(regexp(groupspec{ii},[char(pattern(ix)),'$']))
% 			if ~isempty(strfind(groupspec{ii},char(pattern(ix))))
				ind(ig) = 1;
			end
		end
	end
	if sum(ind)==1
		ind = find(ind==1);
		groupspec = groups4display{ind};
		% odstran jednotlive
		iitem = [];
		for ii = 1 : length(groupspec)
			if isempty(regexp(groupspec{ii},[char(pattern(ix)),'$']))
% 			if isempty(strfind(groupspec{ii},char(pattern(ix))))
				iitem = [iitem,ii];
			end
		end
		groupspec = groupspec(iitem);
		% pridaj all
		groupspec = [groupspec,['all__' char(pattern(ix))]];
		groups4display{ind} = groupspec;
	end
end

end % of SUB function ----------------------------------------------------%



function [groupstruct] = replace_sfore4date(groupstruct,sfore)

dat = dat2str(sfore);
for ix = 1 : length(groupstruct)
	groupstruct{ix} = strrep(groupstruct{ix},'sfore',dat);
end



end % of SUB function ----------------------------------------------------%
