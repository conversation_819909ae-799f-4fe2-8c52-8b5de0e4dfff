function dout = get_exogenized(plan,varargin)

opts = cell2struct(varargin(2:2:end),varargin(1:2:end),2);

if ~isfield(opts,'flag')
    opts.flag='real';
end

switch opts.flag
    case 'real'
        dout=struct();
        nper = plan.enddate-plan.startdate+1;
        for i=1:nper
            xlist=plan.xlist(plan.xanchors(:,i)) ;
            for j=1:length(xlist)
                if ~isfield(dout,xlist(j))
                    dout.(xlist{j})=tseries();
                end
                dout.(xlist{j})(plan.startdate+i-1)=1;
            end
        end
    case 'imag'
        dout=struct();
        nper = plan.enddate-plan.startdate+1;
        for i=1:nper
            xlist=plan.xlist(plan.xanchors(:,i)) ;
            for j=1:length(xlist)  
                if ~isfield(dout,xlist(j))
                    dout.(xlist{j})=tseries();
                end
                dout.(xlist{j})(plan.startdate+i-1)=1;
            end           
        end
    otherwise
        disp('erroooooorr');
end