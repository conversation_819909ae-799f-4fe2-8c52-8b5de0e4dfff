function AOTfeature(fhandle)

drawnow;
w = warning;
warning('off','MATLAB:HandleGraphics:ObsoletedProperty:JavaFrame');
jhandle = get(fhandle,'JavaFrame');
warning(w);
if dynammo.compatibility.newGraphics %  try % Matlab 2014b
    if jhandle.fHG2Client.getWindow.isAlwaysOnTop
        jhandle.fHG2Client.getWindow.setAlwaysOnTop(0);
    else
        jhandle.fHG2Client.getWindow.setAlwaysOnTop(1);
    end    
else %catch % Older Matlab
    if jhandle.fHG1Client.getWindow.isAlwaysOnTop
        jhandle.fHG1Client.getWindow.setAlwaysOnTop(0);
    else
        jhandle.fHG1Client.getWindow.setAlwaysOnTop(1);
    end
end

end %<eof>