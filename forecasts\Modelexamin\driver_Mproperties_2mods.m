%% This script solves model and show properties of the model
% Shows spectral analysis of model and data
%% Prepare environemt
clear all; close all;

disp([sprintf('\n'), 'DRIVER_M-PROPERTIES']);

if isempty(strfind(path, ['\Utilities-extra\_SPECTRAL\tools', pathsep]))
    addpath('..\..\Utilities-extra\_SPECTRAL\tools')
end

%% set the report parameters
% Set options
% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('..\baseline\GLOBALSETTINGS.mat');
disp(['Report: ', FB_ID]);
% load settings
settings = load_settings(FB_ID);

settings.acforder = 0;
settings.freqstep = 0.05;

%% Load models
load([settings.outdata_dir '\model_final-kalman-fb.mat']); % kalman for filration model, model for fcast model 
mm{1}=m;

load([settings.outdata_dir '\model_november-kalman-fb.mat']);
mm{2}=m;

% load([settings.outdata_dir '\develop_FB_simple-model-fb.mat']);
% mm{3}=m;

mm_labels ={'OLD', 'NEW', 'Param_AR'};

%% Prepare data
%transform data into model units
bs=dbload([settings.outdata_dir '\model_final-pre-filter-fb.csv']);
bs2=dbload([settings.outdata_dir '\model_november-pre-filter-fb.csv']); % new names model

d_bs = bs*dbnames(bs,'NameFilter','obs(.*)');

d_o.dot_y_star = exp(d_bs.obs_Y_STAR/100 - d_bs.obs_Y_STAR{-1}/100);
%d_o2.y_star_gap = exp(d_bs2.obs_Y_STAR_GAP/100); % gap a trend mozna nezna stary model
%d_o2.dot_y_star_trend = exp(d_bs2.obs_Y_STAR_TREND/100 - d_bs2.obs_Y_STAR_TREND{-1}/100);

d_o.i_star = d_bs.obs_I_STAR/400+1; 
d_o.i_star_eq = d_bs.obs_I_STAR_EQ/400+1; 
d_o.i_star_eu = d_bs.obs_I_STAR_EU/400+1; 

d_o.usdeur = exp(d_bs.obs_USDEUR/100);

%d_o2.dot_pstar_energy_tilde = exp(d_bs2.obs_PSTAR_ENERGY_TILDE/100 - d_bs2.obs_PSTAR_ENERGY_TILDE{-1}/100);
d_o.dot_pstar_other_tilde = exp(d_bs.obs_PSTAR_OTHER_TILDE/100 - d_bs.obs_PSTAR_OTHER_TILDE{-1}/100);
d_o.dot_pstar_tilde = exp(d_bs.obs_PSTAR_TILDE/100 - d_bs.obs_PSTAR_TILDE{-1}/100);
d_o.dot_cpi_star_tilde = exp(d_bs.obs_CPI_STAR_TILDE/100 - d_bs.obs_CPI_STAR_TILDE{-1}/100);

listvar1 = dbnames(d_o);

d_bs2 = bs2*dbnames(bs2,'NameFilter','obs(.*)');

d_o2.dot_y_star = exp(d_bs2.obs_Y_STAR/100 - d_bs2.obs_Y_STAR{-1}/100);
%d_o2.y_star_gap = exp(d_bs2.obs_Y_STAR_GAP/100); % gap a trend mozna nezna stary model
%d_o2.dot_y_star_trend = exp(d_bs2.obs_Y_STAR_TREND/100 - d_bs2.obs_Y_STAR_TREND{-1}/100);

d_o2.i_star = d_bs2.obs_I_STAR/400+1; 
d_o2.i_star_eq = d_bs2.obs_I_STAR_EQ/400+1; 
d_o2.i_star_eu = d_bs2.obs_I_STAR_EU/400+1; 

d_o2.usdeur = exp(d_bs2.obs_USDEUR/100);

%d_o2.dot_pstar_energy_tilde = exp(d_bs2.obs_PSTAR_ENERGY_TILDE/100 - d_bs2.obs_PSTAR_ENERGY_TILDE{-1}/100);
d_o2.dot_pstar_other_tilde = exp(d_bs2.obs_PSTAR_OTHER_TILDE/100 - d_bs2.obs_PSTAR_OTHER_TILDE{-1}/100);
d_o2.dot_pstar_tilde = exp(d_bs2.obs_PSTAR_TILDE/100 - d_bs2.obs_PSTAR_TILDE{-1}/100);
d_o2.dot_cpi_star_tilde = exp(d_bs2.obs_CPI_STAR_TILDE/100 - d_bs2.obs_CPI_STAR_TILDE{-1}/100);

listvar2 = dbnames(d_o2);
listvar{1} = listvar1;
listvar{2} = listvar2;

%% Show model properties
% check for multiple models
if length(mm)>1
    % Check steady state
    disp('Model s. state:');
    for ii=2:length(mm)
        if chksstate(mm{1}) == chksstate(mm{ii}),
            disp(['Model' mm_labels{1} ' and ' mm_labels{ii} ' are same']);
        else
            disp(['Model' mm_labels{1} ' and ' mm_labels{ii} ' differ']);
        end
    end;
    
    % stationarity of variables
    disp('Model stationarity:');
    for ii=2:length(mm)
        if isstationary(mm{1})==isstationary(mm{ii}),
            disp(['Model ' mm_labels{1} ' and ' mm_labels{ii} ' are same']);
        else
            disp(['Model ' mm_labels{1} ' and ' mm_labels{ii} ' differ']);
        end;
    end;
    
    
    disp('VAriables stationarity difference:');
    for ii=2:length(mm)
        get(mm{1},'stationary')-get(mm{ii},'stationary')
    end;
    
    
    % show growth q-o-q
    disp('Model differences in growths:');
    for ii=2:length(mm)
        get(mm{1},'ssgrowth')-get(mm{ii},'ssgrowth')
    end;
    
    % get the list of initial contidions
    
    disp('Model differences initial state:');
    for ii=2:length(mm)
        setdiff(get(mm{1},'required'),get(mm{ii},'required'))
    end;
    
end
%% MODEL

freq = 0:settings.freqstep:pi;
for ii=1:length(mm)   
    %acf
    [C{ii},R{ii},list{ii}] = acf(mm{ii},'order',settings.acforder,'select',listvar{ii});
    %spd
    [S{ii},D{ii}] = xsf(mm{ii},freq,'select',listvar{ii});
    
    d(ii).cov=C{ii};
    d(ii).corr=R{ii};
    d(ii).listvar=listvar{2};
    d(ii).type='model';
    d(ii).label=mm_labels{ii};
    d(ii).freq=freq;
    d(ii).sd=D{ii};
    d(ii).coher=xsf2coher(D{ii});
    d(ii).gain=xsf2gain(D{ii});
    [phase, d(ii).phaset] = xsf2phase(d(ii).sd, freq);
    
end

%% Data Moments
% population moments
vx = VAR();

[w,data] = estimate(vx,d_o2,listvar{2},Inf,'order',settings.acforder+4); % inf = refers to range
[Cw,Rw] = acf(w,'order',settings.acforder); % autocovariance and autocorrelation function

freq = 0:settings.freqstep:pi;
[Sw,Dw] = xsf(w,freq); % power spectrum and spectral density function
% bootstrap simple VAR model to handle sample uncertainty
Y = resample(w,data,Inf,1000,'method','bootstrap','wild',true);
W=VAR();
W = estimate(W,Y,listvar{2},inf,'order',settings.acforder+1);
index = isstationary(W);
disp('Number of explosive VARs:');
disp(sum(index == false));
[CW,RW] = acf(W,'order',settings.acforder); % upt to acforder
[SW,DW] = xsf(W,freq);

dd.listvar=listvar{2};

dd.cov=Cw;
dd.corr=Rw;
dd.covb=CW;
dd.corrb=RW;
dd.sd=Dw;

dd.coher = xsf2coher(Dw);
dd.gain  = xsf2gain(Dw);
dd.type = 'data';
dd.label = 'Data';
dd.freq = freq;
[phase, dd.phaset] = xsf2phase(Dw, freq);


%% Report comparison ACF, XSF
report_acf(dd,'cmp',d,'fname',[settings.outreport_dir '\' 'ACF-report-' settings.headline_prefix '-DATAModel-Order' num2str(settings.acforder) ],'pggraph',4);
report_xsf(dd,'cmp',d,'fname',[settings.outreport_dir '\' 'XSF-report-' settings.headline_prefix '-DATAModel' ],'pggraph',4);
