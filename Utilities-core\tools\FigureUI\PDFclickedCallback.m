function PDFclickedCallback(fhandle)

drawnow;

%% AOT feature off!

w = warning;
warning('off','MATLAB:HandleGraphics:ObsoletedProperty:JavaFrame');
jhandle = get(fhandle,'JavaFrame');
warning(w);
AOTsetup = jhandle.fHG1Client.getWindow.isAlwaysOnTop;
jhandle.fHG1Client.getWindow.setAlwaysOnTop(0);

%% Orientation
%     set(figs(ii),'PaperOrientation','landscape');
%     
%     set(figs(ii),'PaperUnits','normalized');
%     set(figs(ii),'PaperPosition', [0 0 1 1]);
    
set(fhandle,'PaperOrientation','landscape');
%set(fighandle,'PaperSize',A4dims(3:4));
%set(fighandle,'position',A4dims); 
set(fhandle,'PaperUnits','normalized');
set(fhandle,'PaperPosition',[0 0 1 1]);

%fig2save = PDFfigname();
fig2save = outfilename('Figure export to PDF', ...
                       'Enter PDF file name:', ...
                       rand_str(), ...default suggestion
                       '.pdf');

if ~strcmp(fig2save,'')
    print(fhandle,'-dpdf',fig2save,'-r150');
    disp(['--- Figure printed to ' fig2save]);
end

%% Previous AOT setup

if AOTsetup
    jhandle.fHG1Client.getWindow.setAlwaysOnTop(1);
else
    jhandle.fHG1Client.getWindow.setAlwaysOnTop(0);
end

end %<eof>