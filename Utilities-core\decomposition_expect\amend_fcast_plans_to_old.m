function fcast_plan = amend_fcast_plans_to_old(fcast_plan_decomp, m_new, sr_opt_new, sr_opt_old)

if sr_opt_old.start_pred==sr_opt_new.start_pred
    fcast_plan = fcast_plan_decomp;
else
    fcast_plan = plan(m_new, sr_opt_old.comprng);
   
    exog =  get_exogenized(fcast_plan_decomp);
    endog = dbextend(get_endogenized(fcast_plan_decomp),get_endogenized(fcast_plan_decomp,'flag','imag'));
    exog_names = dbnames(exog);
    endog_names = dbnames(endog);
    
    for xx=1:length(exog_names)
        range = exog.(exog_names{xx}).range;
        fcast_plan  = exogenize( fcast_plan, exog_names{xx}, range);
        range = endog.(endog_names{xx}).range;
        fcast_plan  = endogenize( fcast_plan, endog_names{xx}, range);
    end
    
    range = sr_opt_old.start_pred:sr_opt_new.ehist;
%     
% %     fcast_plan = exogenize(fcast_plan,'dot_pC' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_costpushY' , range);
%     
% %     fcast_plan = exogenize(fcast_plan,'dot_pJ' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_costpushJ' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_p' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_costpushC' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_pX' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_costpushX' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_pN' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_costpushN' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_gp' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_costpushG' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_w' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_labor' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'ell' , range); %dot_aL
% %     fcast_plan = endogenize(fcast_plan,'eps_aL' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_s' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_uip' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_c' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_habit' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'dot_j' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_aJ' , range);
%     
% %     fcast_plan = exogenize(fcast_plan,'dot_x' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_aX' , range);
% % 
% %     fcast_plan = exogenize(fcast_plan,'dot_n' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_aO' , range);
%     
%     fcast_plan = exogenize(fcast_plan,'dot_n_star' , range);
%     fcast_plan = endogenize(fcast_plan,'eps_Nstar' , range);
% 
    fcast_plan = exogenize(fcast_plan,'dot_p_star_tilde' , range);
    fcast_plan = endogenize(fcast_plan,'eps_Pstar' , range);
% 
% %     fcast_plan = exogenize(fcast_plan,'i' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_mpolicy' , range);
% 
    fcast_plan = exogenize(fcast_plan,'i_star' , range);
    fcast_plan = endogenize(fcast_plan,'eps_Istar' , range);
%     
    fcast_plan = exogenize(fcast_plan,'dot_pREG' , range);
    fcast_plan = endogenize(fcast_plan,'eps_pREG' , range);
% 
    fcast_plan = exogenize(fcast_plan,'target' , range);
    fcast_plan = endogenize(fcast_plan,'eps_target' , range);
%     
% %     fcast_plan = exogenize(fcast_plan,'dot_cpi' , range);
% %     fcast_plan = endogenize(fcast_plan,'eps_dot_cpi' , range);
%     
    fcast_plan = exogenize(fcast_plan,'dot_g' , range);
    fcast_plan = endogenize(fcast_plan,'eps_gov' , range);
    
    fcast_plan = exogenize(fcast_plan,'dot_gp' , range);
    fcast_plan = endogenize(fcast_plan,'eps_costpushG' , range);

end

end