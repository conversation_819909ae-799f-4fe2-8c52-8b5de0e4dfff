function DECOMP = reducedecomp(decomp, groups, groups_nms, varargin)

%- MAIN FUNCTION ---------------------------------------------------------%

%% parse options

default = {...
	'language',		'EN', ...
	};
options = passopt(default, varargin{1:end});

% language
switch upper(options.language)
	case {'CZ'}
		language = 2;
	case {'EN'}
		language = 3;
end

%% reduce matrix

if iscell(groups) && iscell(groups_nms)
    contrib = zeros(length(decomp.decomp_range),size(groups,2),length(decomp.endog_vars));
    for ivar = 1:length(decomp.endog_vars)
        for ix = 1:size(groups,2)
            [~,index] = ismember(groups{ix},decomp.input_datenames);
            contrib(:,ix,ivar) = sum(decomp.store_matrix(:,index,ivar),2);
        end
    end
    factor_names = groups_nms(:,language);
else
    contrib = decomp.store_matrix;
    factor_names = decomp.input_datenames;
end    

%% output

DECOMP.store_matrix			= contrib;
DECOMP.truediffmat			= decomp.truediffmat;
DECOMP.endog_vars			= decomp.endog_vars;
DECOMP.input_datenames		= factor_names;
DECOMP.decomp_range			= decomp.decomp_range;
DECOMP.hist_rng				= decomp.hist_rng;
DECOMP.trans_rng			= decomp.trans_rng;
DECOMP.fut_rng				= decomp.fut_rng;
DECOMP.d_new				= decomp.d_new;
DECOMP.d_old				= decomp.d_old;
DECOMP.limit				= decomp.limit;
DECOMP.islog				= decomp.islog;


check_decomposition(DECOMP,'limit',DECOMP.limit);

end %- of MAIN FUNCTION --------------------------------------------------%
