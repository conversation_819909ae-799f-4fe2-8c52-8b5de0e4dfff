%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function [sr_opt, varargout] = load_sr_options(sr_ID, varargin)

% Input:		sr_ID		- string (usually report prefix) for the main forecast
% 
%		options:
%				'old_ID'		- string (usually report prefix) for the compared forecast
%				'extend'		- prefix extension - must be defined in sr_options
%				'extend_old'	- prefix extension for the compared forecast -
%									must be defined in sr_options for old forecast
%
% Output:		sr_opt, sr_opt_old, sr_opt_cmp	- structure containing all revelant data	
%
% This function loads a data structure for one (and optionally two) forecasts specification(s) like: 
% output data, labels, dates, file names etc.

% @ last revision ZH feb-2015

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


% Paths
path_opt.indata_dir       = '..\database\Input-data';
path_opt.outdata_dir      = '..\database\Output-data';
path_opt.outreport_dir    = '..\database\Output-reports';
path_opt.outgraph_dir     = '..\database\Output-graphs';
paths_ = fieldnames(path_opt);


%% READ INPUT

default = { ...
		'old_id',		'', ...
		'extend',		'', ...
		'extend_old',	'', ...
			};
options = passopt(default,varargin{:});

% Load options - prefer outdata
if ischar(sr_ID)
    if isequal(exist([path_opt.outdata_dir '\' sr_ID '-sr_opt.mat'], 'file'), 2)
        load([path_opt.outdata_dir '\' sr_ID '-sr_opt.mat']);
        sr_opt = sr_ID; clear sr_ID;
        sr_opt.ID_name = [path_opt.outdata_dir '\' sr_opt.report_prefix '-sr_opt.mat'];
    elseif isequal(exist([path_opt.indata_dir '\' sr_ID '-sr_opt.mat'], 'file'), 2)
        load([path_opt.indata_dir '\' sr_ID '-sr_opt.mat']);
        sr_opt = sr_ID; clear sr_ID;
        sr_opt.ID_name = [path_opt.indata_dir '\' sr_opt.report_prefix '-sr_opt.mat'];
    else
        error(['load_sr_options: Basic options for ' sr_ID ' not found!']);
    end
else
    disp('load_sr_options: First input is not a string!');
end

% Add paths
for i = 1:length(paths_)
	sr_opt.(paths_{i}) = path_opt.(paths_{i});
end

% Find second sr_ID (optional) 
if ~isempty(options.old_id)
	if ischar(options.old_id)
		sr_opt.oldfrc_prefix = options.old_id;
	else
		disp('load_sr_options: Second input is not a string!');
	end
end
if  isfield(sr_opt,'oldfrc_prefix')
	old_ID = sr_opt.oldfrc_prefix;
end

% Load old options - prefer indata (optional)
if exist('old_ID','var')
	if isequal(exist([path_opt.indata_dir '\' old_ID '-sr_opt.mat'], 'file'), 2)
		load([path_opt.indata_dir '\' old_ID '-sr_opt.mat']);
		sr_opt_old = sr_ID; clear sr_ID;
		sr_opt_old.ID_name = [path_opt.indata_dir '\' sr_opt_old.report_prefix '-sr_opt.mat'];
	elseif isequal(exist([path_opt.outdata_dir '\' old_ID '-sr_opt.mat'], 'file'), 2)
		load([path_opt.outdata_dir '\' old_ID '-sr_opt.mat']);
		sr_opt_old = sr_ID; clear sr_ID;
		sr_opt_old.ID_name = [path_opt.outdata_dir '\' sr_opt_old.report_prefix '-sr_opt.mat'];
	else
		warning(['Basic options for ' old_ID ' not found!']);
		clear old_ID;
	end

	% Add paths
	for i = 1:length(paths_)
		sr_opt_old.(paths_{i}) = path_opt.(paths_{i});
	end

end

% Analyse extend (optional)
if ~isempty(options.extend) && isfield(sr_opt,'extend')
	if isfield(sr_opt.extend,options.extend)
		suffix = options.extend;
	else 
		error(['load_sr_options: Unknown prefix extension ', options.extend, '!']);
	end
elseif ~isempty(options.extend)
	error(['load_sr_options: sr_options for extension ', options.extend, ' not found!']);
else
	suffix  = '';
end

% Analyse old extend (optional)
if ~isempty(options.extend_old) && isfield(sr_opt_old,'extend')
	if isfield(sr_opt_old.extend,options.extend_old)
		suffix_old = options.extend_old;
	else 
		error(['load_sr_options: Unknown old prefix extension ', options.extend_old, '!']);
	end
elseif ~isempty(options.extend_old)
	error(['load_sr_options: old sr_options for extension ', options.extend_old, ' not found!']);
else
	suffix_old  = '';
end


%% NEW

% find special name files:
special_name = { ...
	'histcore_name'
	'histcore_name_pbs'
	'adhoc_data'
				};

for ix = 1:length(special_name)
	if isfield(sr_opt, special_name{ix})
		if isequal(exist([sr_opt.indata_dir '\' sr_opt.(special_name{ix})], 'file'), 2)
				sr_opt.(special_name{ix}) = [sr_opt.indata_dir '\' sr_opt.(special_name{ix})];
		elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.(special_name{ix})], 'file'), 2)
				sr_opt.(special_name{ix}) = [sr_opt.outdata_dir '\' sr_opt.(special_name{ix})];
		else
			warning(['New ' special_name{ix} ' file not found!']);
		end
	else
		warning([special_name{ix} ' not defined in new sr_opt!']);
	end
end

% find standard name files:
sr_opt_name = { ...
% 	'NAME'							'-FILE_EXTENTION'
	'filter_name'					'-filter.csv'
	'filterdata_name'				'-filterdata.mat'
	'prefilter_name'				'-pre-filter.csv'
	'data_name'						'-forecast.csv'
	'data_name_pure'				'-forecast_pure.csv'
	'book_name'						'-forecast-book.csv'
	'model_name'					'-model.mat'
	'kalman_name'					'-kalman.mat'
	'ss_name'						'-SS.mat'
	'plan_name'						'-plan.mat'
	'plan_surprise_name'			'-plan_surprise.mat'
	'tunes_name'					'-tunes.mat'
	'postbaseline_name'				'-postbaseline.csv'
	'postbaselineY_name'			'-postbaselineY.csv'
	'yy_data_name'					'-yearly-forecast.csv'
	'expectations_name'				'-expectations.mat'
				};

for ix = 1:length(sr_opt_name)
	if ~isempty(suffix)
		if isequal(exist([sr_opt.indata_dir '\' sr_opt.extend.(suffix) sr_opt_name{ix,2}], 'file'), 2)
			sr_opt.(sr_opt_name{ix,1}) = [sr_opt.indata_dir '\' sr_opt.extend.(suffix) sr_opt_name{ix,2}];
		elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.extend.(suffix) sr_opt_name{ix,2}], 'file'), 2)
			sr_opt.(sr_opt_name{ix,1}) = [sr_opt.outdata_dir '\' sr_opt.extend.(suffix) sr_opt_name{ix,2}];
		elseif isequal(exist([sr_opt.indata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}], 'file'), 2)
			sr_opt.(sr_opt_name{ix,1}) = [sr_opt.indata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}];
		elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}], 'file'), 2)
			sr_opt.(sr_opt_name{ix,1}) = [sr_opt.outdata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}];
		else
			warning(['New ' sr_opt_name{ix,1} ' file not found!']);
		end
	else
		if isequal(exist([sr_opt.indata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}], 'file'), 2)
			sr_opt.(sr_opt_name{ix,1}) = [sr_opt.indata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}];
		elseif isequal(exist([sr_opt.outdata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}], 'file'), 2)
			sr_opt.(sr_opt_name{ix,1}) = [sr_opt.outdata_dir '\' sr_opt.report_prefix sr_opt_name{ix,2}];
		else
			warning(['New ' sr_opt_name{ix,1} ' file not found!']);
		end
	end
end

sr_opt.headline_prefix = sr_opt.report_prefix;
if ~isempty(suffix)
	sr_opt.report_prefix = sr_opt.extend.(suffix);
end


%% OLD

if exist('old_ID','var')
	
	% find special name files:
	special_name = { ...
		'histcore_name'
		'histcore_name_pbs'
		'adhoc_data'
					};

	for ix = 1:length(special_name)
		if isfield(sr_opt_old, special_name{ix})
			if isequal(exist([sr_opt_old.indata_dir '\' sr_opt_old.(special_name{ix})], 'file'), 2)
					sr_opt_old.(special_name{ix}) = [sr_opt_old.indata_dir '\' sr_opt_old.(special_name{ix})];
			elseif isequal(exist([sr_opt_old.outdata_dir '\' sr_opt_old.(special_name{ix})], 'file'), 2)
					sr_opt_old.(special_name{ix}) = [sr_opt_old.outdata_dir '\' sr_opt_old.(special_name{ix})];
			else
				warning(['New ' special_name{ix} ' file not found!']);
			end
		else
			warning([special_name{ix} ' not defined in old sr_opt!']);
		end
	end

	% find standard name files:
	sr_opt_name = { ...
	% 	'NAME'							'-FILE_EXTENTION'
		'filter_name'					'-filter.csv'
		'filterdata_name'				'-filterdata.mat'
		'prefilter_name'				'-pre-filter.csv'
		'data_name'						'-forecast.csv'
		'data_name_pure'				'-forecast_pure.csv'
		'book_name'						'-forecast-book.csv'
		'model_name'					'-model.mat'
		'kalman_name'					'-kalman.mat'
		'ss_name'						'-SS.mat'
		'plan_name'						'-plan.mat'
		'plan_surprise_name'			'-plan_surprise.mat'
		'tunes_name'					'-tunes.mat'
		'postbaseline_name'				'-postbaseline.csv'
		'postbaselineY_name'			'-postbaselineY.csv'
		'yy_data_name'					'-yearly-forecast.csv'
		'expectations_name'				'-expectations.mat'
				};

	for ix = 1:length(sr_opt_name)
		if ~isempty(suffix_old)
			if isequal(exist([sr_opt_old.indata_dir '\' sr_opt_old.extend.(suffix_old) sr_opt_name{ix,2}], 'file'), 2)
				sr_opt_old.(sr_opt_name{ix,1}) = [sr_opt_old.indata_dir '\' sr_opt_old.extend.(suffix_old) sr_opt_name{ix,2}];
			elseif isequal(exist([sr_opt_old.outdata_dir '\' sr_opt_old.extend.(suffix_old) sr_opt_name{ix,2}], 'file'), 2)
				sr_opt_old.(sr_opt_name{ix,1}) = [sr_opt_old.outdata_dir '\' sr_opt_old.extend.(suffix_old) sr_opt_name{ix,2}];
			elseif isequal(exist([sr_opt_old.indata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}], 'file'), 2)
				sr_opt_old.(sr_opt_name{ix,1}) = [sr_opt_old.indata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}];
			elseif isequal(exist([sr_opt_old.outdata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}], 'file'), 2)
				sr_opt_old.(sr_opt_name{ix,1}) = [sr_opt_old.outdata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}];
			else
				warning(['Old ' sr_opt_name{ix,1} ' file not found!']);
			end
		else
			if isequal(exist([sr_opt_old.indata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}], 'file'), 2)
					sr_opt_old.(sr_opt_name{ix,1}) = [sr_opt_old.indata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}];
			elseif isequal(exist([sr_opt_old.outdata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}], 'file'), 2)
					sr_opt_old.(sr_opt_name{ix,1}) = [sr_opt_old.outdata_dir '\' sr_opt_old.report_prefix sr_opt_name{ix,2}];
			else
				warning(['Old ' sr_opt_name{ix,1} ' file not found!']);
			end
		end
	end
	
	sr_opt_old.headline_prefix = sr_opt_old.report_prefix;
	sr_opt.oldhead_prefix = sr_opt_old.headline_prefix;
	if ~isempty(suffix_old)
		sr_opt_old.report_prefix = sr_opt_old.extend.(suffix_old);
		sr_opt.oldfrc_prefix = sr_opt_old.report_prefix;
	end
	
end


%% CMP

if exist('old_ID','var')

	sr_opt_cmp.cmpreport_prefix = [sr_opt.report_prefix '-v-' sr_opt_old.report_prefix]; % define compare report prefix
	sr_opt_cmp.cmpheadline_prefix = [sr_opt.headline_prefix '-v-' sr_opt_old.headline_prefix];
	
	sr_opt_cmp.end_pred			= sr_opt.end_pred;

	sr_opt_cmp.hist_rng			= sr_opt_old.hrng;
	sr_opt_cmp.trans_rng		= sr_opt_old.start_pred:sr_opt.ehist;
	sr_opt_cmp.fut_rng			= sr_opt.comprng;

	% add paths 
	for i = 1:length(paths_)
		sr_opt_cmp.(paths_{i}) = path_opt.(paths_{i});
	end

end


%% OPTIONAL OUTPUT

if nargout > 1
	varargout = cell(1,nargout-1);
	if exist('old_ID','var')
		varargout{1} = sr_opt_old;
		varargout{2} = sr_opt_cmp;
	end
end 

end % of main function
