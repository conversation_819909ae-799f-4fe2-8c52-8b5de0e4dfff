!log_variables
!allbut

!if filtering == true
% IS
'Observed Real Foreign Demand (100*log)'                obs_Y_STAR

!if ~FB_gdp_decomp
    'Observed Real Foreign Output Gap (100*)'           obs_Y_STAR_GAP
    'Observed Real Foreign GDP Trend (100*log)'         obs_Y_STAR_TREND
    'Observed Real Foreign GDP Shift Trend (QoQ)'       obs_DOT_Y_STAR_TREND_SHIFT
!end

!if ~FB_prem_decomp
    'Observed USD/EUR Risk Premium (percent p.a.)'      obs_PREM_USDEUR
!end

% MP
'Observed 3-M Foreign Rates (percent p.a.)'             obs_I_STAR
'Observed 3-M Neutral Foreign Rates (percent p.a.)'     obs_I_STAR_EQ
'Observed Effective 3-M Foreign Rates (percent p.a.)'   obs_I_STAR_EU    
'Observed 3-M Foreign (US) Rates (percent p.a.)'        obs_I_STAR_US

% UIP
'Observed Exchange Rate EUR/USD (100*log)'              obs_USDEUR   

% PC
'Observed Energy Price Level (EUR) (100*log)'           obs_PSTAR_ENERGY_TILDE
'Observed Non-Energy Price Level (EUR) (100*log)'       obs_PSTAR_OTHER_TILDE
'Observed Foreing Price Level (EUR) (100*log)'          obs_PSTAR_TILDE
'Observed Consurmer Price Index (100*log)'              obs_CPI_STAR_TILDE  

% IS
'Measured Real Foreign Demand (100*log)'                mes_Y_STAR

!if ~FB_gdp_decomp
    'Measured Foreign Output Gap (100*log)'             mes_Y_STAR_GAP
    'Measured Real Foreign GDP Trend (100*log)'         mes_Y_STAR_TREND
    'Measured Real Foreign GDP Shift Trend (QoQ)'       mes_DOT_Y_STAR_TREND_SHIFT
!end

!if ~FB_prem_decomp
    'Observed USD/EUR Risk Premium (percent p.a.)'      mes_PREM_USDEUR
!end

% MP
'Measured 3-M Foreign Rates (percent p.a.)'             mes_I_STAR
'Measured neutral 3-M Foreign Rates (percent p.a.)'     mes_I_STAR_EQ
'Measured Effective 3-M Foreign Rates (percent p.a.)'   mes_I_STAR_EU  
'Measured 3-M Foreign (US) Rates (percent p.a.)'        mes_I_STAR_US

% UIP
'Measured Exchange Rate EUR/USD (100*log)'              mes_USDEUR

% PC
'Measured Energy Price Level (EUR) (100*log)'           mes_PSTAR_ENERGY_TILDE
'Measured Non-Energy Price Level (EUR) (100*log)'       mes_PSTAR_OTHER_TILDE
'Measured Foreign Price Level (EUR) (100*log)'          mes_PSTAR_TILDE 
'Measured Consurmer Price Index (100*log)'              mes_CPI_STAR_TILDE  

!end

% <end>