%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% DRIVER_DECOMPOSITION
% @ last revision: FK,ZA CNB, Sep 2012
%
% Decompose two forecast exercises using the decomposition tool
%
% INPUT:
% See comments in INPUT section
% 
% OUTPUT:
% 'decomp'		- a structure: necessary input and output of ORIGINAL decomposition
% 'contrib'		- a structure: necessary input and output of GROUPED decomposition
% 'sr_opt'		- a structure: situation report options of both forecasts, 
%					and options for compare
% 'groups'		- a structure: grouped factors of decomposition
% 'groups_nms'	- a structure: labels of groups
% 
% Optional output:
% - Matlab graphs
% - PDF report 
% - XLS table
% - GRIP graph
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% TO DO: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
% - check and adjust sr_options (load)

% Clear the work space
close all;
clear all;
disp([sprintf('\n'), 'DRIVER_DECOMPOSITION']);


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% INPUT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%% main options

new_ID              = '2019sz01-shadow-new_exp2';				% name of the new sz (all - forecast, filter)
% old_ID              = '2016sz05';				% name of the old sz
old_ID              = 'SS';
decomp_range		= qq(2019,1):qq(2021,4);	% range of matrix
plan_type			= 0;						%  --> see STANDARD_FCAST_PLANS.M
												%  1 - classic FORECAST decomposition
												% -1 - classic GRIP decomposition
												% 11 - basic FULFILLMENT decomposition 
												%  0 - individual setting of forecast plans
detail_level        = 1;						% 0 - do just with respect to vars, don't decompose into quarters,
												% 1 - separate NTF obs and fixes on forecast into quarters,
												% 2 - decompose into quarters all variables
ini2obs				= 0;						% 1 - decompose to obs; 0 - decompose just to ini_vars
obs_free			= 0;						% 1 - no forecast plan on obs and tunes decomposition, 0 - standard plan
just_filter			= 0;						% 1 - skipFcast; 0 - decompose with respect to plot_range
limit				= 1e-08;					% computation error limit
recompute           = 1;						% 0 - try to find already computed items, 1 - recompute all

rls2shocks			= 0;						% 1 - release decomposed with respect to shocks
												% 0 - standard decomposition with respect to observations
obs2shocks          = 0;                        % 1 - history decomposed with respect to shocks
                                                % 0 - standard decomposition with respect to observations

%% report options

% define the list of variables to decompose & report:
% if you want to see also decompositions of expected endogenous vars
% (without the effects of unexpected fixes and shocks), add '_exp' suffix to the name
% for all final endogenous variables write 'all'
% for all including those without the effects of unexpected fixes and shocks write 'all_exp'
decomp_list = {...
% 		'all'
% 		'all_exp'
    	'dot_cpi4'
%       'K'
		'i'
%     	'dot_p'
%     	'dot_s'
% 		'dot_w'
% 		'rmcc'
% 		'rmcy'
%     	'dot_c'
% 		'dot_j'
% 		'dot_x'
% 		'dot_n'
%     	'dot_pREG'
% 		'dot_g'
% 		'dot_pG'
%     	'i_star'
%     	'dot_n_star'
% 		'dot_p_star_tilde'
% 		'dot_Z'
%     	'dot_A'
% 		'dot_aX'
% 		'dot_aO'
% 		'dot_aQ'
% 		'dot_aL'
%     	'dot_pC'
% 		'dot_pJ'
% 		'dot_pX'
% 		'dot_pN'
% 		'dot_pG'
    };

grouping_type       = 1;						% 1 - Zuzka - READ_GROUPS.M 
												% 0 - Franta - SET_GROUPS.M
autogroups			= 'FORECASTINI_01';	% 0, [] - no grouping into group of factors
                                                % 'Factors' - group into factors no time...
												% first part (?_X_X): Forecast, ForecastINI, Grip, Fulfillment ie 'GRIP_01_??'
                                                % 'GRIP_01_??'  - first part is usually defined with respect to
                                                % decomp.type, second is detail level, third some special case
                                                
firstgroups			= 0;						% if # groups is specified, for each var from decomp_list only # of sorted factors is reported

doXLS				= 0;						% export contributions in .xls format
doReport			= 0;						% report in PDF
doGraphs			= 1;						% MATLAB graphs
doGRIP				= 0;						% GRIP picture !!! Check code!!!
Grip_graph_format   = 'htm';                    % set output format of grip figure,  htm, doc, ppt
Grip_graph_lims     = 1;                        % sets the size of axis in graph
output_ID			= '';						% this string is added to saved report and xls name; could be also empty string:
												% '2text' use this ID for official version exported to xls (graph in text)

language			= 'EN';						% language version EN/CZ
plot_range          = qq(2019,1):qq(2022,4);	% range which will be depicted
show_modelchange	= 1;						% shows contribution of model changes in graphs and reports
zz_transform		= 1;						% transformation to %

graph_style         = 1;                        % 1 - new, 0 - old;


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% RUN THE DECOMPOSITION
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


[decomp_tuneold, ...
decomp_tunenew, ...
decomp_tunerev, ...         
decomp_rev, ...
decomp_rls, ...
decomp_fcastold, ...
decomp_fcastnew, ...
decomp_ini, ...
decomp_type, ...
d_pure_new, ...
d_pure_old, ...
options, sr_opt] = run_decomposition(new_ID, old_ID, ...
	'decomp_range',decomp_range,'plan_type',plan_type,'detail_level',detail_level,...
	'ini2obs',ini2obs,'obs_free',obs_free,'just_filter',just_filter,...
	'rls2shocks',rls2shocks,'obs2shocks',obs2shocks,'recompute',recompute,...
	'limit',limit,'show_modelchange',show_modelchange);
                            
tic;                             
decomp = merge_decomposition(old_ID, new_ID, options.decomp_range, sr_opt, ...
	decomp_tuneold, decomp_tunenew, decomp_tunerev, decomp_rev, decomp_rls, ...
	decomp_fcastold, decomp_fcastnew, decomp_ini, d_pure_new, d_pure_old, ...
	plan_type, decomp_type, detail_level, limit, show_modelchange);
toc                         
tic;                         
report_decomposition(decomp, sr_opt, plot_range, ...
	'decomp_list',decomp_list, ...
	'grouping_type',grouping_type, ...
	'autogroups',autogroups, ...
	'firstgroups',firstgroups,...
	'xls',doXLS, ...
	'report',doReport, ...
	'graphs',doGraphs, ...
	'grip',doGRIP,...
	'output_id',output_ID, ...
	'language',language, ...
	'zz_transform',zz_transform, ...
    'graph_style', graph_style, ...
    'grip_type', Grip_graph_format, ...
    'grip_lim', Grip_graph_lims);
toc
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


