function [out_dbase] = dbcompare2(in_dbase1,in_dbase2,in_digit,histdir,varargin)
    % DBCOMPARE     Subtracts in_dbase1 from in_dbase2 element-by-element,
    % and saves non-zero differences rounded at number of digits given in
    % in_digit to a .csv file. Last argument specifies the last date of history
    % and is optional. If the optional argument is specified, the screen output
    % of each variable name with nonzero differences is extended by 'H', 'P' or
    % 'HP', saying at what time period the differences are: history, forecast or both.
    %
    % Use this function to summarize the differences in data and search for
    % changes.
    %
    % [out_dbase] = dbcompare(in_dbase1,in_dbase2,in_digit,varargin)
    % dbase       = dbcompare(dbase,dbase,positive integer,date)
    %
    % Last revision: FB, Nov 2011
    
    if ~isa(in_digit,'numeric') || in_digit<0 || (round(in_digit)~=in_digit)
        error(['Invalid option "in_digit".']);
        return
    end;
    
    if length(varargin)>4
        error(['Too many options. Maximum is 4.']);
        return
    end;
    
    dbase1     = dbload(in_dbase1);
    dbase2     = dbload(in_dbase2);
    diff       = dbfun(@(x,y) [x-y],dbase2,dbase1);
    
    varlist = dbobjects(diff);
    m = length(varlist);
    
    i=0;
    found=0;
    while found==0 & i<m
        i=i+1;
        sdate = get(eval(['diff.',varlist{i}]),'start');
        edate = get(eval(['diff.',varlist{i}]),'end');
        if ~(varargin{1}>edate | varargin{1}<sdate)
            found=1;
        end;
    end;
    if found==0
        error(['Wrong date for end of history.']);
        return
    end;
    % Round differences to [in_digit] digits:
    for i=1:m
        range = get(eval(['diff.',varlist{i},]),'range');
        eval(['tmp = diff.',varlist{i},'(range);']);
        tmp = tmp.*10^in_digit;
        tmp = round(tmp);
        tmp = tmp.*10^(-in_digit);
        diff.(char(varlist{i})) = tseries(range,tmp);
    end;
    
    
    % Identify tseries where the rounded difference is non-zero:
    j=0;
    nonzero=0;
    if length(varargin)==0
        for i=1:m
            if any(eval(['diff.',varlist{i}])) & isa(eval(['diff.',varlist{i}]),'tseries')
                j=j+1;
                nonzero=1;
                eval(['out_dbase.',varlist{i},' = diff.',varlist{i},';']);
            end;
        end;
        
    elseif length(varargin)==1
        for i=1:m
            if any(eval(['diff.',varlist{i}])) & isa(eval(['diff.',varlist{i}]),'tseries')
                j=j+1;
                nonzero=1;
                eval(['out_dbase.',varlist{i},' = diff.',varlist{i},';']);
                sdate   = get(eval(['diff.',varlist{i}]),'start');
                edate   = get(eval(['diff.',varlist{i}]),'end');
                if any(resize(eval(['diff.',varlist{i}]),sdate:varargin{1}))
                    textin='H';
                    eval(['hp.',varlist{i},'= textin;']);
                    if any(resize(eval(['diff.',varlist{i}]),varargin{1}+1:edate))
                        textin='HP';
                        eval(['hp.',varlist{i},'= textin;']);
                    end;
                elseif any(resize(eval(['diff.',varlist{i}]),varargin{1}+1:edate))
                    textin='P';
                    eval(['hp.',varlist{i},'= textin;']);
                end;
            end;
        end;
    end;
    
    % Quit if no differences:
    if nonzero==0
        str1='No differences up to ';
        str2=' digits';
        dig=num2str(in_digit);
        disp([str1 dig str2]);
        return
    end;
    
    % Cut off earliest periods with zeros for all tseries in out_dbase:
    varlist = dbobjects(out_dbase);
    for i=1:length(varlist)
        sdate = get(eval(['out_dbase.',varlist{i}]),'start');
        edate = get(eval(['out_dbase.',varlist{i}]),'end');
        j=sdate;
        
        while  abs(eval(['out_dbase.',varlist{i},'(j)'])) <eps & j<edate
            j=j+1;
        end
        MINDATES(i,1)=j;
        MAXDATES(i,1)=edate;
    end;
    firstdate=min(MINDATES);
    lastdate=max(MAXDATES);
    out_dbase = dbunop(out_dbase,Inf,Inf,'resize',firstdate:lastdate);
    
    %% Reporting results:
    str1='There are differences in the following variables (up to ';
    dig=num2str(in_digit);
    str2=' digits):';
    disp(' ');
    disp([str1 dig str2]);
    varlist2=dbobjects(out_dbase);
    switch length(varargin)
        case 0
            for i=1:length(varlist2)
                disp([varlist2{i}]);
            end;
        case 1
            for i=1:length(varlist2)
                eval(['disp([varlist2{i} blanks(20-length(varlist2{i})) hp.',varlist2{i},']);']);
            end;
    end;
    disp(' ');
    disp(' ');
    
    %% Saving results:
    dbsave(out_dbase,strcat([histdir 'differences_'],date,'.csv'),Inf,'format','%.16e');
    disp('Non-zero differences of time series saved in file: differences.csv');
    disp(' ');
