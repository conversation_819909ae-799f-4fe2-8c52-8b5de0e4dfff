function tableupdate_excelmacro(Tablesfile)

% Updates basic tables for fulfillment in TablesforText_XXXXszXX.xls

% %Open a COM server on Matlab
% excel = actxserver('Excel.Application');
% excel.Visible = 1;
% 
% % Otevrit excel tabulku odkud se bude kopirovat
% disp([sprintf('\n'),'<PERSON> se otevira ''.xls'' pro tabulky na plneni, ']);
% disp(['pokud to dlouho trva, tak je soubor nejspis otevreny ']);
% disp(['a je potreba ho nejprve zavrit!!!']);
% 
% [~] = invoke(excel.Workbooks.Open(filespec));
% excel.Run('Update_Tabulka');
% excel.ActiveWorkbook.Close(true);
% excel.Quit;

startup_xls_session;
excel.Run('Update_Tabulka');
drop_xls_session;