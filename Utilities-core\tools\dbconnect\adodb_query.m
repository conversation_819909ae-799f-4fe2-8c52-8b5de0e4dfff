function [Struct, Table] = adodb_query(ado_connection, sql, Pref)
%% FUNCTION NAME:
%   [Struct, Table] = adodb_query(connection, sqlstring,additional_preference)
%
% DESCRIPTION:
%   Executes the sql statement against the connection  ado_connection
%
% INPUT:
%   ado_connection - (handle) open connection to ADO OLEDB ActiveX Data Source Control
%   sql            - (string) SQL statement to be executed
%   Pref           - (sturcture) preferentes allowing customization of the function
%     Pref.MultipleQuery - default false - Allow support for multiple queries.
%       If true than results will be stored as a cell array with one call
%       for each nonempty recordset. If false than only first recordset
%       will be returned.
%     DPref.UseTrans - default false - use "Begin Transaction" & "Commit
%       Transaction" to bracket the querry and use Rollback in case of an
%       error. Use this if using multiple queries in a single statement and
%       want to rollback results of an early query in case latter query
%       fails.
%     DPref.NumTries - default 1 - Number of tries to use in case of an
%       error. Works only if DPref.UseTrans = false. pauses for 10 sec in
%       between tries. Usefull in situations with unreliable connection to
%       the database.
%
% OUTPUT:
%   Struct         - query results in struct (or array of structs) format
%   Table          - query results table (or array of tables) format
%
% ASSUMPTIONS AND LIMITATIONS:
%   
% EXAMPLE:
%  [~, Table] = adodb_query(DB,'SELECT  sest.id sest_id , TO_DATE(data.obdobi,''YYYYMMDD'') obdobi, MAX(DECODE(c_uka,1,hodnota)) uk1 FROM ( SELECT  s.c_uka,TO_CHAR (d.obdobi,''YYYYMMDD'') obdobi,SUM(d.hodnota*r.znam*s.znam*POWER(10,-r.nasob)) hodnotaD,SUM(d.hodnota * r.znam * s.znam * Power(10, -r.nasob)) As hodnota,d.conf FROM arady.TP_IRUNOQ d, (SELECT u.id as c_uka,i.cislo AS radek,i.znamenko znam,u.mena_data mena,u.nasobek nasob,u.desetiny dec,u.conf FROM arady.p_ukars i,arady.p_ukazatele u WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id=937 AND  SYSDATE BETWEEN plati_od AND plati_do) AND u.u_id=i.uka_uid AND u.zdroj_id=''IRUNOQ'' AND i.rs=1) r, (SELECT u.id as c_uka,i.cislo AS sloupec,i.znamenko znam FROM arady.p_ukars i,arady.p_ukazatele u WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id=937 AND  SYSDATE BETWEEN plati_od AND plati_do) AND u.u_id=i.uka_uid AND u.zdroj_id=''IRUNOQ'' AND i.rs=2) s WHERE r.c_uka = s.c_uka And d.radek = r.radek And d.sloupec = s.sloupec AND s.c_uka IN (1) AND d.mena=r.mena GROUP BY s.c_uka,d.obdobi,d.conf) data, arady.p_ukazatele uka, arady.p_sestavy sest WHERE sest.u_id = uka.sest_uid AND sest.id=937 AND data.c_uka=uka.id AND data.obdobi >= ''19960101''  AND SYSDATE BETWEEN sest.plati_od AND sest.plati_do GROUP BY data.obdobi, sest.id ORDER BY 2');
% REVISION HISTORY:
%   $Revision: R2013b$
%   $Author: F. Brazdik$
%   $Date: April 30, 2020$
% REFERENCE
%   Jaroslaw Tuszynski (2020). adodb_tools (https://www.mathworks.com/matlabcentral/fileexchange/29615-adodb_tools), MATLAB Central File Exchange. Retrieved April 30, 2020.
%

%% Handle Properties argument
DPref.MultipleQuery = false;
DPref.UseTrans      = false;
DPref.NumTries      = 1;
if (nargin>2)
    if (isfield(Pref, 'MultipleQuery' )), DPref.MultipleQuery = Pref.MultipleQuery;  end
    if (isfield(Pref, 'UseTrans'      )), DPref.UseTrans      = Pref.UseTrans;       end
    if (isfield(Pref, 'NumTries'      )), DPref.NumTries      = Pref.NumTries;       end
end

%% Run Querry
if (DPref.UseTrans)        % Use Transaction for your queries
    ado_connection.BeginTrans;
    try
        ado_recordset = ado_connection.Execute(sql);
        ado_connection.CommitTrans;
    catch ME
        ado_connection.RollbackTrans;
        rethrow(ME);           % After rollback throw an error
    end
elseif (DPref.NumTries==1) % No transactions single try
    ado_recordset = ado_connection.Execute(sql);
else                       % No transactions multiple tries
    OK = 0;                  % Initialize operation as failed
    for iTry = 1:DPref.NumTries
        try
            ado_recordset = ado_connection.Execute(sql);
            OK = 1;              % Mark operation as succesfull ...
            break                % ... and stop trying
        catch ME               % Catch errors
            pause(10);           % 10 second pause and try again
        end
    end
    if (~OK),
        rethrow(ME);           % If operation did not succedded than throw an error
    end
end

%% Parse Recordset
iSet = 1;
Struct{iSet} = [];                   % initialize space for output
Table {iSet} = [];
while (~isempty(ado_recordset))      % loop through all ado_recordsets
    if (ado_recordset.State && ado_recordset.RecordCount>0)
        table = ado_recordset.GetRows';  % retrieve data from recordset
        result = [];
        Fields = ado_recordset.Fields;   % retrieve all Fields with column names
        for col = 1:Fields.Count         % loop through all columns
            ColumnName = Fields.Item(col-1).Name; % get column name
            name = genvarname(lower(ColumnName)); % convert it to a valid MATLAB field name
            ColumnName = regexprep(ColumnName, 'Expr\d\d\d\d', ''); % MS Access uses Expr1000 etc. when column name can not be deduced
            if (isempty(ColumnName)),
                name = char('A'-1+col);      % if column without name than use A, B, C, ... as column names
            end
            if (size(table,1)==1)          % is table a vector ?
                Res = table{col};
                if (numel(Res)==0 || strcmpi(Res, 'N/A') || isnan(all(Res))), Res=[]; end
            else                           % is table a matrix?
                Res = table(:,col);
            end
            result.(name) = Res;
        end
        Struct{iSet} = result;
        Table {iSet} = table;
        iSet = iSet+1;
    end
    try
        ado_recordset = ado_recordset.NextRecordset(); % go to the next recordsat
    catch  %#ok<CTCH> % some DB do not support NextRecordset
        break;
    end
end

%% In single query mode return only the first nonempty recordset
if (~DPref.MultipleQuery),
    Struct = Struct{1};
    Table  = Table{1};
end

