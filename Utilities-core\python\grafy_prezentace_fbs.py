# -*- coding: utf-8 -*-
"""
Created on Wed Apr 22 12:52:15 2015
Modified for foreign block model on Sep 13 2018
@author: U04483, U06431
"""
import sys
import numpy as np
import pandas as pd
import datetime as dt
import matplotlib.pyplot as plt
from matplotlib import cm
import h5py
import matplotlib.font_manager as fm
import matplotlib as mpl

import nettlefgrafy
import Database410_module as db410

if __name__ == "__main__":  
    
    # IMPORTANT SETTINGS / NEEDS TO BE UPDATED MANUALLY
    
    sz_label = '2018sz05_fbs' #  current forecast name
    
    curr_sz_label = '5.SZ 2018'     #  legend of current forecast
    last_sz_label = '5.SZ 2018 old' #  legend of previous forecast    
    
    ehist = '15.05.2018'         #  end of historical data, begins the shaded area / fcast horizon
    end_hist_old = '15.02.2018'  #  end of historical data in previous forecast
        
    start_date = '2015-01' # start of the interval depicted in graphs
    #start_date = '1998-01' #     long history interval
    end_date = '2020-12'   # end of the interval depicted in graphs
    #end_date = '2018-06' # no forecast interval => IS presentation
    
    # OTHER SETTINGS     
    
    picdir = 'grafpics\\' # output folder for figures
    
    zkraceni_minule = False # True, False, optional truncation of previous forecast data
    zkraceni_date = '2020-01'   
    
    #    colorscheme = 'cnbtableau'
    colorscheme = 'cnbmanual'
    fontColor = (26/255.,33/255.,85/255.)
    export2file = True
    insert2ppt = False
    save2ppt = False
    
    pad = 23.
    
#    barcolors_sparta = [(26/255.,33/255.,85/255.),(214/255., 39/255., 40/255.),(255/255., 193/255., 86/255.)]
#    barcolors_bohemka = [(44/255., 160/255., 44/255.),(255/255., 193/255., 86/255.),(26/255.,33/255.,85/255.)]
#    barcolors_nhdd_pom = [(0, 107, 164), (255, 128, 14),(171, 171, 171),
#                      (89, 89, 89),(95, 158, 209), (200, 82, 0)]
    barcolors_nhdd_pom = [(214, 39, 40),(31, 119, 180),(255, 127, 14),(44, 160, 44),
                         (65, 68, 81),(165, 172, 175)]
    
    barcolors_sparta = [(8/255.,81/255.,156/255.),(214/255., 39/255., 40/255.),(255/255., 193/255., 86/255.)]
    barcolors_bohemka = [(50/255., 162/255., 81/255.),(255/255., 193/255., 86/255.),(8/255.,81/255.,156/255.)]

    barcolors_bohemka = [(103/255., 191/255., 192/255.),(237/255., 102/255., 93/255.),(114/255.,158/255.,206/255.)]
    barcolors_bohemka = [(64/255., 160/255., 64/255.),(181/255., 182/255., 179/255.),(8/255.,81/255.,156/255.)]

    linecolor_inflcil = (255/255.,152/255.,150/255.) #(237/255.,102/255.,93/255.)


    barcolors_nhdd = []
    for color in barcolors_nhdd_pom:
        barcolors_nhdd.append((color[0]/255.,color[1]/255.,color[2]/255.))
    
    import scipy.io
    mat = scipy.io.loadmat(r'..\..\database\Output-data\\' + sz_label + '-settings.mat')

    legend = [curr_sz_label, last_sz_label]     
    lastsz = mat['ID'][0][0][1][0]    
    suffix = ''
    suffix_old = ''

    data_name = r'..\..\database\Output-data' + '\\' + mat['ID'][0][0][0][0] + suffix +'-filter-fbs.csv'  
#    old_data_name = r'..\..\database\Input-data' + '\\' + mat['ID'][0][0][1][0] + suffix_old +'-fcast-fbs.csv' 
    old_data_name = data_name
    histcore_name = r'..\..\database\Input-data' + '\\' + mat['ID'][0][0][2][0]
    
    dfcurr = pd.read_csv(data_name,skiprows=range(1,3))
    dflast = pd.read_csv(old_data_name,skiprows=range(1,3))
    hist = pd.read_csv(histcore_name,skiprows=range(1,3))
    
    start = dt.date(1995,3,1)
    end = dt.date(2024,12,1)
    daterange = pd.date_range(start, end, freq='QS', normalize = True)
    dfcurr.set_index(daterange,inplace = True)
    end = dt.date(2024,12,1)
    daterange = pd.date_range(start, end, freq='QS', normalize = True)
    dflast.set_index(daterange,inplace = True)
    
    start = dt.date(1993,1,1)
    end = dt.date(2030,12,31)
    daterange = pd.date_range(start, end, freq='QS', normalize = True)    
    hist.set_index(daterange,inplace = True)
    
    dfcurr = dfcurr.apply(pd.to_numeric,errors='corece')
    
#==============================================================================
#     Slide 01 - vnejsi prostredi
#==============================================================================  
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    
    ax1 = plt.subplot(221)
    ax2 = plt.subplot(222)
    ax3 = plt.subplot(223)
    ax4 = plt.subplot(224)  
    
    
    cols = ['ne_zz_dot_n_star4','ne_zz_dot_p_star_tilde4','ne_zz_i_star','ne_zz_usdeur']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    df['ne_zz_dot_n_star4'] = df['ne_zz_dot_n_star4']/4.
    df['ne_zz_dot_n_star4_last'] = df['ne_zz_dot_n_star4_last']/4.

    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None



    columns = [u'ne_zz_dot_n_star4',
               u'ne_zz_dot_n_star4'+ '_last']
    figtitle = u'Efektivní HDP eurozóny (%, mzr.)'
    nettlefgrafy.plotg(df,columns = columns,ax = ax1,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                       linemarker = 'o',
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=end_hist_old,colorscheme=colorscheme)
                        
    columns = [u'ne_zz_dot_p_star_tilde4',
               u'ne_zz_dot_p_star_tilde4'+ '_last']   
    figtitle = u'Efektivní PPI eurozóny (%, mzr.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax2,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'ne_zz_i_star',
               u'ne_zz_i_star'+ '_last']           
    figtitle = u'3M Euribor (%, p.a.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax3,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'ne_zz_usdeur',
               u'ne_zz_usdeur'+ '_last']           
    labels = legend
    figtitle = u'USD/EUR'  
    ylabels = [0.9,1.1,1.3,1.5]
    ylabels2axis = [-0.4,-0.2,0,0.2]
    f,ax,ax2nd = nettlefgrafy.plotg(df,columns = columns,labels = labels,ylabels=ylabels,ylabels2axis=ylabels2axis,
                        ax = ax4,diffbars = [0,1], diff2axis = True,
                       ticksDecPoints=0,legend = 'best',legpos = (-0.05, -0.095),
                        ylim2nd_min = -0.4,ylim2nd_max = 0.2,fontColor=fontColor,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme) 
                        
    ax2nd.tick_params(pad=pad)                         
                        
    #plt.tight_layout(pad=0.4, w_pad=0.5, h_pad=2.0)                        
    plt.tight_layout(pad = 2., h_pad=2.0)

    if export2file:
        nettlefgrafy.export(name = picdir+'slide01_PredpokladyOZahrVyvoji.png') 
    if insert2ppt:
        nettlefgrafy.insert2ppt(picdir+'slide01_PredpokladyOZahrVyvoji.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Předpoklady o zahr. vývoji (%, mzr.)',ppttype = 'cnb')

#==============================================================================
#     slide 02  - sazby + stínové sazby 
#==============================================================================
    cols = ['ne_zz_i_star_shadow','ne_zz_i_star']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None    
    
    columns = [u'ne_zz_i_star_shadow',
               u'ne_zz_i_star_shadow'+ '_last',
               u'ne_zz_i_star',
               u'ne_zz_i_star'+ '_last' ]                
    labels_tmp = [u'Stínová sazba '+ legend[0],
              u'Stínová sazba '+ legend[1],
              u'3M Euribor '+ legend[0],
              u'3M Euribor '+ legend[1]]
    line_styles = [u'-',u'-',u'--',u'--']              
    figtitle = u'Zahraniční sazby (%, p.a.)'
    f,ax,ax2nd =nettlefgrafy.plotg(df,columns = columns,
                       ticksDecPoints=0,labels = labels_tmp,fontColor=fontColor,
#                       linemarker = 'o',
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, 
#                        yticks_number = 5,
                        ehist=ehist,colorscheme=colorscheme,
                        legcols=2,
                        linesty=line_styles)
    #ax.get_children()[4].set_linestyle('--')                    
    #ax.get_children()[5].set_linestyle('--')    
    
    if export2file:
        nettlefgrafy.export(name = picdir+'slide02_stinovasazba.png') 
    if insert2ppt:
        nettlefgrafy.insert2ppt(picdir+'slide02_stinovasazba.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Stínová zahraniční sazba',ppttype = 'cnb')   
        
#==============================================================================
#     Slide 03 - zahraniční PPI - komponenty
#==============================================================================  
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    
    ax1 = plt.subplot(221)
    ax2 = plt.subplot(222)
    ax3 = plt.subplot(223)
    ax4 = plt.subplot(224)  
    
    
    cols = ['ne_zz_dot_p_star_tilde4','ne_zz_dot_p_other_tilde4','ne_zz_dot_p_brent_tilde4','ne_zz_dot_p_ener_ex_oil_tilde4']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None



    columns = [u'ne_zz_dot_p_star_tilde4',
               u'ne_zz_dot_p_star_tilde4'+ '_last']
    figtitle = u'Zahraniční PPI (%, mzr.)'
    nettlefgrafy.plotg(df,columns = columns,ax = ax1,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                       linemarker = 'o',
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=end_hist_old,colorscheme=colorscheme)
                        
    columns = [u'ne_zz_dot_p_other_tilde4',
               u'ne_zz_dot_p_other_tilde4'+ '_last']   
    figtitle = u'Jádrová PPI inflace (%, mzr.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax2,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'ne_zz_dot_p_brent_tilde4',
               u'ne_zz_dot_p_brent_tilde4'+ '_last']           
    figtitle = u'Cena ropy Brent (%, mzr.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax3,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'ne_zz_dot_p_ener_ex_oil_tilde4',
               u'ne_zz_dot_p_ener_ex_oil_tilde4'+ '_last']           
    labels = legend
    figtitle = u'Ceny neropných energií (%, mzr.)'  
    ylabels = [0.9,1.1,1.3,1.5]
    ylabels2axis = [-0.4,-0.2,0,0.2]
    nettlefgrafy.plotg(df,columns = columns,ax = ax4,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme)                         
                        
    #plt.tight_layout(pad=0.4, w_pad=0.5, h_pad=2.0)                        
    plt.tight_layout(pad = 2., h_pad=2.0)

    if export2file:
        nettlefgrafy.export(name = picdir+'slide03_komponenty_PPI.png') 
    if insert2ppt:
        nettlefgrafy.insert2ppt(picdir+'slide03_komponenty_PPI.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Komponenty zahraničního PPI (%, mzr.)',ppttype = 'cnb')        
        
#==============================================================================
#     slide 04 - PPI inflace - rozklad data, mzk
#==============================================================================
    cols = ['ne_zz_dot_p_star_tilde']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None  
                
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    
    ax1 = plt.subplot(211)
    ax2 = plt.subplot(212)
               
    cols = ['ne_zz_dot_p_star_tilde',
            u'ne_zz_dot_p_star_tilde_contrib_p_other_tilde',
            u'ne_zz_dot_p_star_tilde_contrib_p_brent_tilde',
            u'ne_zz_dot_p_star_tilde_contrib_p_ener_ex_oil_tilde']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    columns = [u'ne_zz_dot_p_star_tilde']    
    labels = [u'Zahraninční PPI (%, mzk.)']
    bars = [u'ne_zz_dot_p_star_tilde_contrib_p_other_tilde',
            u'ne_zz_dot_p_star_tilde_contrib_p_brent_tilde',
            u'ne_zz_dot_p_star_tilde_contrib_p_ener_ex_oil_tilde']
    
    barlabels = [u'Jádrová PPI inflace',
               u'Ropa Brent (EUR)',
               u'Neropné energie']                      
    figtitle = u'Příspěvky k růstu zahraninčního PPI (mzk., p.b.)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax1,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=4,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])    
           
    cols = ['ne_zz_dot_p_star_tilde',
            u'zz_dot_p_star_tilde_contrib_nonener',
            u'zz_dot_p_star_tilde_contrib_oil',
            u'zz_dot_p_star_tilde_contrib_enerother',
            u'zz_dot_p_star_tilde_contrib_resid']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    columns = [u'ne_zz_dot_p_star_tilde']    
    labels = [u'Zahraninční PPI (%, mzk.)']
    bars = [u'zz_dot_p_star_tilde_contrib_nonener',
            u'zz_dot_p_star_tilde_contrib_oil',
            u'zz_dot_p_star_tilde_contrib_enerother',
            u'zz_dot_p_star_tilde_contrib_resid',
            ]
    
    barlabels = [u'Jádrová PPI inflace',
               u'Ropa Brent (EUR)',
               u'Neropné energie',
               u'PPI šok',
               ]                      
    figtitle = u'Příspěvky k růstu zahraninčního PPI - model (mzk., p.b.)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax2,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=4,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])                        
    plt.tight_layout(pad = 2., h_pad=5.0)
    
    if export2file:
        nettlefgrafy.export(name = picdir+'slide04_Zahraniční PPI_QoQ.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Zahraninční PPI, QoQ',ppttype = 'cnb')
        
#==============================================================================
#     slide 05 - PPI inflace - rozklad data, mzr
#==============================================================================
    cols = ['ne_zz_dot_p_star_tilde4']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None  
                
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    
    ax1 = plt.subplot(211)
    ax2 = plt.subplot(212)
               
    cols = ['ne_zz_dot_p_star_tilde4',
            u'ne_zz_dot_p_star_tilde_contrib_p_other_tilde4',
            u'ne_zz_dot_p_star_tilde_contrib_p_brent_tilde4',
            u'ne_zz_dot_p_star_tilde_contrib_p_ener_ex_oil_tilde4']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    columns = [u'ne_zz_dot_p_star_tilde4']    
    labels = [u'Zahraninční PPI (%, mzr.)']
    bars = [u'ne_zz_dot_p_star_tilde_contrib_p_other_tilde4',
            u'ne_zz_dot_p_star_tilde_contrib_p_brent_tilde4',
            u'ne_zz_dot_p_star_tilde_contrib_p_ener_ex_oil_tilde4']
    
    barlabels = [u'Jádrová PPI inflace',
               u'Ropa Brent (EUR)',
               u'Neropné energie']                      
    figtitle = u'Příspěvky k růstu zahraninčního PPI - data (mzr., p.b.)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax1,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=4,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])            
           
    cols = ['ne_zz_dot_p_star_tilde4',
            u'zz_dot_p_star_tilde_contrib_nonener4',
            u'zz_dot_p_star_tilde_contrib_oil4',
            u'zz_dot_p_star_tilde_contrib_enerother4',
            u'zz_dot_p_star_tilde_contrib_resid4']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    columns = [u'ne_zz_dot_p_star_tilde4']    
    labels = [u'Zahraninční PPI (%, mzr.)']
    bars = [u'zz_dot_p_star_tilde_contrib_nonener4',
            u'zz_dot_p_star_tilde_contrib_oil4',
            u'zz_dot_p_star_tilde_contrib_enerother4',
            u'zz_dot_p_star_tilde_contrib_resid4',
            ]
    
    barlabels = [u'Jádrová PPI inflace',
               u'Ropa Brent (EUR)',
               u'Neropné energie',
               u'PPI šok',
               ]                      
    figtitle = u'Příspěvky k růstu zahraninčního PPI - model (mzr., p.b.)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax2,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=4,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])                        
    plt.tight_layout(pad = 2., h_pad=5.0)
    
    if export2file:
        nettlefgrafy.export(name = picdir+'slide05_Zahraniční PPI_YoY.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Zahraninční PPI, YoY',ppttype = 'cnb')
         
#==============================================================================
#     Slide 06 - zahraniční výstup, gap a trend
#==============================================================================  
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    
    ax1 = plt.subplot(221)
    ax2 = plt.subplot(222)
    ax3 = plt.subplot(223)
    ax4 = plt.subplot(224)  
    
    
    cols = ['ne_zz_dot_n_star4','ne_zz_dot_y_star_trend','ne_zz_y_star_gap']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    df['ne_zz_dot_n_star4_']=((1+df.ne_zz_dot_n_star4/100)**(1/4)-1)*100 # transform the dot_n_star from foreign demand for our exports to foreign output / parameter 4
    df['ne_zz_dot_n_star4__last']=((1+df.ne_zz_dot_n_star4_last/100)**(1/4)-1)*100 # transform the dot_n_star from foreign demand for our exports to foreign output / parameter 4

    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None



    columns = [u'ne_zz_dot_n_star4',
               u'ne_zz_dot_n_star4'+ '_last']
    figtitle = u'Efektivní zahraniční poptávka (%, mzr.)'
    nettlefgrafy.plotg(df,columns = columns,ax = ax1,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                       linemarker = 'o',
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=end_hist_old,colorscheme=colorscheme)
                        
    columns = [u'ne_zz_dot_n_star4_',
               u'ne_zz_dot_n_star4_'+ '_last']   
    figtitle = u'Zahraniční výstup (%, mzr.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax2,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'ne_zz_dot_y_star_trend',
               u'ne_zz_dot_y_star_trend'+ '_last']           
    figtitle = u'Trend zahraničního výstupu (%, mzr.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax3,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'ne_zz_y_star_gap',
               u'ne_zz_y_star_gap'+ '_last']           
    labels = legend
    figtitle = u'Mezera zahraničního výstupu (%)'  
    nettlefgrafy.plotg(df,columns = columns,ax = ax4,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme)                        
                        
    #plt.tight_layout(pad=0.4, w_pad=0.5, h_pad=2.0)                        
    plt.tight_layout(pad = 2., h_pad=2.0)

    if export2file:
        nettlefgrafy.export(name = picdir+'slide06_HDP_gap_trend.png') 
    if insert2ppt:
        nettlefgrafy.insert2ppt(picdir+'slide06_HDP_gap_trend.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Zahraniční poptávka a výstup (%, mzr.)',ppttype = 'cnb')   

#==============================================================================
#     slide 07 - zahraniční výstup - rozklad na gap a trend
#==============================================================================
    cols = ['ne_zz_dot_n_star']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None  
                
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    
    ax1 = plt.subplot(211)
    ax2 = plt.subplot(212)
               
    cols = [u'ne_zz_dot_n_star',
            u'zz_dot_n_star_contrib_gap',
            u'zz_dot_n_star_contrib_trend']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    df['ne_zz_dot_n_star_']=((1+df.ne_zz_dot_n_star/100)**(1/4)-1)*100 # transform the dot_n_star from foreign demand for our exports to foreign output / parameter 4
    
    columns = [u'ne_zz_dot_n_star_']    
    labels = [u'Zahraninční výstup (%, mzk.)']
    bars = [u'zz_dot_n_star_contrib_gap',
            u'zz_dot_n_star_contrib_trend']
    
    barlabels = [u'Mezera výstupu',
               u'Trend']                      
    figtitle = u'Rozklad zahraninčního výstupu na gap a trend (mzk., p.b.)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax1,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=4,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])            
           
    cols = [u'ne_zz_dot_n_star4',
            u'zz_dot_n_star_contrib_gap4',
            u'zz_dot_n_star_contrib_trend4']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    df['ne_zz_dot_n_star4_']=((1+df.ne_zz_dot_n_star4/100)**(1/4)-1)*100 # transform the dot_n_star from foreign demand for our exports to foreign output / parameter 4
          
    columns = [u'ne_zz_dot_n_star4_']    
    labels = [u'Zahraninční výstup (%, mzr.)']
    bars = [u'zz_dot_n_star_contrib_gap4',
            u'zz_dot_n_star_contrib_trend4']
    
    barlabels = [u'Mezera výstupu',
               u'Trend']                      
    figtitle = u'Rozklad zahraninčního výstupu na gap a trend (mzr., p.b.)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax2,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=4,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])                        
    plt.tight_layout(pad = 2., h_pad=5.0)
    
    if export2file:
        nettlefgrafy.export(name = picdir+'slide07_rozklad_zahranicniho_vystupu.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Rozklad zahraničního výstupu na gap a trend',ppttype = 'cnb')
         
#==============================================================================

#==============================================================================
#     slide 08 - mezera zahraničního výstupu - modelový rozklad
#==============================================================================
    cols = ['zz_y_star_gap']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None  
                
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    ax1 = plt.subplot(111)
    cols = [u'zz_y_star_gap',
            u'zz_y_star_gap_contrib_lag',
            u'zz_y_star_gap_contrib_rmci',
            u'zz_y_star_gap_contrib_oil',
            u'zz_y_star_gap_contrib_usdeur',
            u'zz_y_star_gap_contrib_resid']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
        
    columns = [u'zz_y_star_gap']    
    labels = [u'Mezera zahraninčního výstupu (%)']
    bars = [u'zz_y_star_gap_contrib_lag',
            u'zz_y_star_gap_contrib_rmci',
            u'zz_y_star_gap_contrib_oil',
            u'zz_y_star_gap_contrib_usdeur',
            u'zz_y_star_gap_contrib_resid']
    
    barlabels = [u'Smoothing',
               u'RMCI',
               u'Cena Ropy Brent (EUR)',
               u'USD/EUR',
               u'Šok v mezeře výstupu']                      
    figtitle = u'Modelový rozklad mezery zahraninčního výstupu (%)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax1,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=3,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])            
     
    if export2file:
        nettlefgrafy.export(name = picdir+'slide08_Zahraniční_Output_Gap.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Zahraninční Output Gap',ppttype = 'cnb')
         
#==============================================================================

#==============================================================================
#     slide 09 - Zahraniční sazby - modelový rozklad
#==============================================================================
    cols = ['zz_i_star']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
    
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None  
                
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    ax1 = plt.subplot(111)
    cols = [u'zz_i_star',
            u'zz_i_star_contrib_ss',
            u'zz_i_star_contrib_smoothing',
            u'zz_i_star_contrib_ogap',
            u'zz_i_star_contrib_inflexpdev',
            u'zz_i_star_contrib_resid']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
        
    columns = [u'zz_i_star']    
    labels = [u'Zahraniční sazby (p.b., p.a.)']
    bars = [u'zz_i_star_contrib_ss',
            u'zz_i_star_contrib_smoothing',
            u'zz_i_star_contrib_ogap',
            u'zz_i_star_contrib_inflexpdev',
            u'zz_i_star_contrib_resid']
    
    barlabels = [u'Steady state',
               u'Smoothing',  
               u'Mezera výstupu',
               u'Infl. oček.',
               u'MP šok']                      
    figtitle = u'Modelový rozklad zahraničních sazeb (p.b., p.a.)'
    
    nettlefgrafy.plotg(df,columns = columns,bars = bars,barlabels = barlabels,
                       barcolors = barcolors_nhdd,ax=ax1,figtitle = figtitle,
                       ticksDecPoints=0,labels = labels,
                        ehist=ehist,legpushdown = -0.08,legcols=3,
                        colorscheme=colorscheme,linecolors=[(214/255., 39/255., 40/255.)])            
     
    if export2file:
        nettlefgrafy.export(name = picdir+'slide09_Zahraniční_Sazby.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Zahraninční Sazby',ppttype = 'cnb')
         
#==============================================================================

#==============================================================================
#     Slide 10 - ceny ropy
#==============================================================================  
    f = plt.figure()
    figsize = (10,5.25)
    f.set_size_inches(figsize)
    
    ax1 = plt.subplot(221)
    ax2 = plt.subplot(222)
    ax3 = plt.subplot(223)
    ax4 = plt.subplot(224)  
    
    
    cols = ['ne_zz_dot_p_BrentUSD_tilde4','ne_zz_dot_p_brent_tilde4','ne_zz_dot_usdeur4','zz_dot_pEtrend_tilde4']
    df = dfcurr[cols].join(dflast[cols], lsuffix='', rsuffix='_last')
    df = df[start_date:end_date]
 
    if zkraceni_minule:
        for sloupec in df.columns:
            if '_last' in sloupec:
                df[sloupec][zkraceni_date:end_date]=None


    columns = [u'ne_zz_dot_p_BrentUSD_tilde4',
               u'ne_zz_dot_p_BrentUSD_tilde4'+ '_last']
    figtitle = u'Cena ropy Brent USD (%, mzr.)'
    nettlefgrafy.plotg(df,columns = columns,ax = ax1,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                       linemarker = 'o',
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=end_hist_old,colorscheme=colorscheme)
                        
    columns = [u'ne_zz_dot_p_brent_tilde4',
               u'ne_zz_dot_p_brent_tilde4'+ '_last']   
    figtitle = u'Cena ropy Brent EUR (%, mzr.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax2,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle, yticks_number = 6,xlabels = {'2000':'nic'},
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'ne_zz_dot_usdeur4',
               u'ne_zz_dot_usdeur4'+ '_last']           
    figtitle = u'Depreciace USD/EUR (%, mzr.)'               
    nettlefgrafy.plotg(df,columns = columns,ax = ax3,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme)

    columns = [u'zz_dot_pEtrend_tilde4',
               u'zz_dot_pEtrend_tilde4'+ '_last']           
    labels = legend
    figtitle = u'Trend v ceně ropy Brent USD (%, mzr.)'  
    nettlefgrafy.plotg(df,columns = columns,ax = ax4,diffbars = [0,1], diff2axis = False,
                       ticksDecPoints=0,legend = '',fontColor=fontColor,
#                        ylim2nd_min = 0,ylim2nd_max = 0.8,
                        figtitle = figtitle,  yticks_number = 5,                      
                        ehist=ehist,colorscheme=colorscheme)                        
                        
    #plt.tight_layout(pad=0.4, w_pad=0.5, h_pad=2.0)                        
    plt.tight_layout(pad = 2., h_pad=2.0)

    if export2file:
        nettlefgrafy.export(name = picdir+'slide10_ceny_ropy.png') 
    if insert2ppt:
        nettlefgrafy.insert2ppt(picdir+'slide10_ceny_ropy.png')                                               
    if save2ppt:
        nettlefgrafy.saveppt(stitle=u'Ceny ropy (%, mzr.)',ppttype = 'cnb')   