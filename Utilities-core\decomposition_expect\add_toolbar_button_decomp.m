function add_toolbar_button_decomp(hFig, decomp, decomp_names, precision)

    % Load the data_tip_cursor icon
    aux_path  = which('add_toolbar_button_decomp');
    icon_path = aux_path(1:end-length('add_toolbar_button_decomp.m'));
    icon = fullfile(icon_path,'\custom_tool_data_cursor.gif');
    [cdata,map] = imread(icon);
    cdata(:,1)=5;
    cdata(1,:)=5;
    % Convert white pixels into a transparent background
    map(find(map(:,1)+map(:,2)+map(:,3)==3)) = NaN;

    % Convert into 3D RGB-space
    cdatabutton = ind2rgb(cdata,map);
    
    hToolbar = findall(hFig,'tag','FigureToolBar');

    hCDTButton = uipushtool(hToolbar,'cdata',cdatabutton, 'tooltip','custom decomp datatips', 'ClickedCallback','update_callbacks_decomp(gcf)');

%     hButtons = findall(hToolbar);
%     set(hToolbar,'children',hButtons([2:end,1]));
%     set(hCDTButton,'Separator','on');
 end