function [fcast_plan fcast_plan_surprise] = standard_fcast_plans(type,m,sr_opt)

% Prepares forecast plans for simulations
% Note: these plans must be set only on future range, where both
% forecasts are projected!  

switch type
	
    case 0  % MANUAL
		
        fcast_plan = plan(m, sr_opt.comprng);
		
%         fcast_plan  = exogenize( fcast_plan, 'dot_p_star_tilde', sr_opt.pistarrng);
%         fcast_plan  = endogenize(fcast_plan, 'eps_Pstar',        sr_opt.pistarrng);
%         
%         fcast_plan  = exogenize( fcast_plan, 'dot_n_star',       sr_opt.nstarrng);
%         fcast_plan  = endogenize(fcast_plan, 'eps_Nstar',        sr_opt.nstarrng);
%         
%         fcast_plan  = exogenize( fcast_plan, 'i_star',           sr_opt.istarrng);
%         fcast_plan  = endogenize(fcast_plan, 'eps_Istar',        sr_opt.istarrng);
%         
%         fcast_plan  = exogenize( fcast_plan,  'target',           sr_opt.targetrng);
%         fcast_plan  = endogenize(fcast_plan, 'eps_target',       sr_opt.targetrng);
        
        fcast_plan_surprise = plan(m, sr_opt.comprng);
        
%         fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_p_star_tilde', sr_opt.pistarrng);
%         fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Pstar',        sr_opt.pistarrng);
%         
%         fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_n_star',       sr_opt.nstarrng);
%         fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Nstar',        sr_opt.nstarrng);
%         
%         fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'i_star',           sr_opt.istarrng);
%         fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Istar',        sr_opt.istarrng);
%         
%         fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_pREG',         sr_opt.regrng);
%         fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_pREG',         sr_opt.regrng);
%         
%         fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_g',            sr_opt.dotgrng);
%         fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_gov',          sr_opt.dotgrng);
%         
%         fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_gp',           sr_opt.dotgprng);
%         fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_costpushG',    sr_opt.dotgprng);

        case 1  % new old on g3+
		
            fcast_plan = plan(m, sr_opt.comprng);
            
            fcast_plan  = exogenize( fcast_plan, 'usdeur',           sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_USDEUR',       sr_opt.start_pred:sr_opt.end_pred);
            
            fcast_plan  = exogenize( fcast_plan, 'dot_pstar_other_tilde',sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_pstar_other_tilde',      sr_opt.start_pred:sr_opt.end_pred);
            
            fcast_plan  = exogenize( fcast_plan, 'dot_pstar_energy_tilde',sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_pstar_energy_tilde',      sr_opt.start_pred:sr_opt.end_pred);
            %
            fcast_plan  = exogenize( fcast_plan, 'dot_cpi_star_tilde',sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_dot_cpi_star_tilde',      sr_opt.start_pred:sr_opt.end_pred);
            %
            fcast_plan  = exogenize( fcast_plan, 'y_star_gap',       sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_y_star_gap',        sr_opt.start_pred:sr_opt.end_pred);
            %
            fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend_fund',       sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_dot_y_star_trend_fund',        sr_opt.start_pred:sr_opt.end_pred);
            %
            fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend_shift',       sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_dot_y_star_trend_shift',        sr_opt.start_pred:sr_opt.end_pred);
            %
            fcast_plan  = exogenize( fcast_plan, 'i_star_eq',           sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_i_star_eq',       sr_opt.start_pred:sr_opt.end_pred);
            %
            fcast_plan  = exogenize( fcast_plan, 'i_star_eu',           sr_opt.start_pred:sr_opt.end_pred);
            fcast_plan  = endogenize(fcast_plan, 'eps_Istar',           sr_opt.start_pred:sr_opt.end_pred);
           
                 
		
    case -1  % GRIP on g3+
		
        fcast_plan = plan(m, sr_opt.comprng);
		
        fcast_plan  = exogenize( fcast_plan, 'dot_p_star_tilde', sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_Pstar',        sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'dot_n_star',       sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_Nstar',        sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'i_star',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_Istar',        sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan  = exogenize( fcast_plan,  'target',          sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_target',       sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'dot_s',            sr_opt.start_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_uip',          sr_opt.start_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'dot_p',            sr_opt.start_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_costpushC',    sr_opt.start_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'i',                sr_opt.start_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_mpolicy',      sr_opt.start_pred);
        
        fcast_plan_surprise = plan(m, sr_opt.comprng);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_p_star_tilde', sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Pstar',        sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_n_star',       sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Nstar',        sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'i_star',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Istar',        sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_pREG',         sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_pREG',         sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_g',            sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_gov',          sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_gp',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_costpushG',    sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_s',            sr_opt.start_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_uip',          sr_opt.start_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_p',            sr_opt.start_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_costpushC',    sr_opt.start_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'i',                sr_opt.start_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_mpolicy',      sr_opt.start_pred);
        
        case -2  % GRIP
		
        fcast_plan = plan(m, sr_opt.comprng);
		
        fcast_plan  = exogenize( fcast_plan, 'usdeur',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_USDEUR',       sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan  = exogenize( fcast_plan, 'dot_p_BrentUSD_tilde', sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_p_BrentUSD',       sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan  = exogenize( fcast_plan, 'dot_p_other_tilde',sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_p_other',      sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan  = exogenize( fcast_plan, 'dot_p_star_tilde' ,sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_p_ener_ex_oil_tilde'        ,sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan  = exogenize( fcast_plan, 'dot_n_star',       sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_Nstar',        sr_opt.start_pred:sr_opt.end_pred);
    %     
    %     fcast_plan  = exogenize( fcast_plan, 'y_star_gap',       sr_opt.nstarrng);
    %     fcast_plan  = endogenize(fcast_plan, 'eps_Nstar',        sr_opt.nstarrng);

    %     fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend',       sr_opt.nstarrng);
    %     fcast_plan  = endogenize(fcast_plan, 'eps_y_star_trend',        sr_opt.nstarrng);

        fcast_plan  = exogenize( fcast_plan, 'i_star',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_Istar',        sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan  = exogenize( fcast_plan, 'i_star_eq',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_i_star_eq',       sr_opt.start_pred:sr_opt.end_pred);    
            
        fcast_plan  = exogenize( fcast_plan,  'target',          sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_target',       sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'dot_s',            sr_opt.start_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_uip',          sr_opt.start_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'dot_p',            sr_opt.start_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_costpushC',    sr_opt.start_pred);
        
        fcast_plan  = exogenize( fcast_plan, 'i',                sr_opt.start_pred);
        fcast_plan  = endogenize(fcast_plan, 'eps_mpolicy',      sr_opt.start_pred);
        
        fcast_plan_surprise = plan(m, sr_opt.comprng);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'usdeur',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_USDEUR',       sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_p_BrentUSD_tilde', sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_p_BrentUSD',       sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_p_other_tilde',sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_p_other',      sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_cpi_star_tilde',sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_dot_cpi_star_tilde',      sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_p_star_tilde', sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_p_ener_ex_oil_tilde',        sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_n_star',       sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Nstar',        sr_opt.start_pred:sr_opt.end_pred);

    %     fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'y_star_gap',       sr_opt.nstarrng);
    %     fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Nstar',        sr_opt.nstarrng);

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_y_star_trend',       sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_y_star_trend',        sr_opt.start_pred:sr_opt.end_pred);   

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'i_star',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_Istar',        sr_opt.start_pred:sr_opt.end_pred);

        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'i_star_eq',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_i_star_eq',       sr_opt.start_pred:sr_opt.end_pred);       
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_pREG',         sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_pREG',         sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_g',            sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_gov',          sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_gp',           sr_opt.start_pred:sr_opt.end_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_costpushG',    sr_opt.start_pred:sr_opt.end_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_s',            sr_opt.start_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_uip',          sr_opt.start_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'dot_p',            sr_opt.start_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_costpushC',    sr_opt.start_pred);
        
        fcast_plan_surprise  = exogenize( fcast_plan_surprise, 'i',                sr_opt.start_pred);
        fcast_plan_surprise  = endogenize(fcast_plan_surprise, 'eps_mpolicy',      sr_opt.start_pred);
        
  		
    case 2  % load orig fcast plans
		
        load(sr_opt.plan_name);
        load(sr_opt.plan_surprise_name);
        
    case 3  % load orig fcast plans and remove NTF
		
        load(sr_opt.plan_name);
        load(sr_opt.plan_surprise_name);
        % dodelat ze stareho driver_decomp_vse
        
        exog =  get_exogenized(fcast_plan);
        endog = dbextend(get_endogenized(fcast_plan),get_endogenized(fcast_plan,'flag','imag'));
        exog_names=dbnames(exog);
        endog_names=dbnames(endog);
        
        for xx = 1:length(exog_names)
            range = exog.(exog_names{xx}).range;
            if range(end) == sr_opt.start_pred
                fcast_plan  = endogenize( fcast_plan, exog_names{xx}, range);
            end
            range = endog.(endog_names{xx}).range;
            if range(end) == sr_opt.start_pred
                fcast_plan  = exogenize( fcast_plan, endog_names{xx}, range);
            end
            
        end
        
        svars = get_exogenized(fcast_plan_surprise);
        sshocks = get_endogenized(fcast_plan_surprise);
        svars_names = fieldnames(svars);
        sshocks_names = fieldnames(sshocks);
        
        for xx = 1:length(svars_names)
            range = svars.(svars_names{xx}).range;
            if range(end) == sr_opt.start_pred
                fcast_plan_surprise  = endogenize( fcast_plan_surprisep, svars_names{xx}, range);
            end
            
            range = sshocks.(sshocks_names{xx}).range;
            if range(end) == sr_opt.new.start_pred
                fcast_plan_surprise  = exogenize( fcast_plan_surprise, sshocks_names{xx}, range);
            end
            
        end
        
     case 11  % FULFILLMENT - empty plans
		
        fcast_plan = plan(m, sr_opt.comprng);
		fcast_plan_surprise = plan(m, sr_opt.comprng);
		
	case 12  % FULFILLMENT INTERVENTIONS - empty plans except anticipated interest rate 
		
		fcast_plan = plan(m, sr_opt.comprng);
		fcast_plan = exogenize(fcast_plan, 'i',			 sr_opt.comprng(1):qq(2014,4));
		fcast_plan = endogenize(fcast_plan,'eps_mpolicy',sr_opt.comprng(1):qq(2014,4));
        
end