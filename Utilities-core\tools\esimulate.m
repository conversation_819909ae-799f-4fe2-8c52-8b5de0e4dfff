% simulate the model with the part of variables perfectly foresighted and
% part unanticipated; using new iris functions - it is compatible with new
% simulate
%
% options:
%   dbdata: structure same as output from new filter, ie f.mean, f.std
%
%   shocks: if anticipate option then real parts are anticipated imaginary
%   unanticipated
%
%   variables: if anticipate option then real parts are anticipated values
%   imaginary unanticipated values
%
%   if anticipation==limited then the fixes of anticipated variables
%   are translated into particular shocks without taking into account the
%   unanticipated shocks and variables - in this sense every variable may
%   have two values - anticipated and unanticipated
%
%   if anticipation==absolute then the fixes of anticipated variables are
%   translated into particular shocks with taking into account of all other
%   even unanticipated shocks and variables - in this sense every variable
%   may have only one value - either anticipated or unanticipated

function dataout = esimulate_classic(m, dbdata, range, varargin)

% get struct of options
orig_opts = cell2struct(varargin(2:2:end),varargin(1:2:end),2);
% get unanticipated variables and paired shocks

if ~isfield(orig_opts,'anticipation')
    orig_opts.anticipation='limited';
   %   orig_opts.anticipation='absolute';
end

dataout = dbempty();

exog_names={};
endog_names={};
svars_names = {};
sshocks_names = {};

% fixum v ocekavanych promennych by mely odpovidat ocekavane soky....
if isfield(orig_opts,'plan')
    exog =  get_exogenized(orig_opts.plan);
    endog = dbextend(get_endogenized(orig_opts.plan),get_endogenized(orig_opts.plan,'flag','imag'));
    exog_names=dbnames(exog);
    endog_names=dbnames(endog);
else    
    orig_opts.plan=plan(m, range);
end    

% fixy neocekavanych promennych a jim odpovidajici neocekavane soky
if isfield(orig_opts,'surprise_plan')
    svars = get_exogenized(orig_opts.surprise_plan);
    sshocks = get_endogenized(orig_opts.surprise_plan);
    svars_names = fieldnames(svars);
    sshocks_names = fieldnames(sshocks);
else
    orig_opts.surprise_plan=plan(m, range);
end    
    
% at first erase all unexpected shocks and variables
db_real = dbfun(@real,dbdata);
db_imag = dbfun(@imag,dbdata);

if strcmp(orig_opts.anticipation,'limited')  
    p = orig_opts.plan;
    p_surp = orig_opts.surprise_plan;
      
    % make options without the plan, surprise_plan, anticipate
    new_opts = rmfield(orig_opts,'anticipation');
    new_opts = rmfield(new_opts, 'plan');
    new_opts = rmfield(new_opts, 'surprise_plan');
    new_opts.anticipate = true;
    % convert options to cell array
    opts = cell(1, 2*length(fieldnames(new_opts)));
    opts(1:2:end) = fieldnames(new_opts);
    opts(2:2:end) = struct2cell(new_opts);
    
    db = db_real;
    dbtmp = simulate(m, db, range, 'plan', p, opts{:});
    db_expected=dbtmp;    
    
    new_opts.anticipate = false;
    % convert options to cell array
    opts = cell(1, 2*length(fieldnames(new_opts)));
    opts(1:2:end) = fieldnames(new_opts);
    opts(2:2:end) = struct2cell(new_opts);
    
    eps_names = dbnames(dbtmp,'nameFilter','^eps_\w*');
    db_pom = dbfun(@(x,y) real(y)+real(x)*1i,db_expected*eps_names,db_imag*eps_names);
    db = dbextend(db_imag,db_pom);

    dbtmp = simulate(m, db, range, 'plan', p_surp, opts{:});
    db_unexpected=dbtmp;
    
    dataout = dbfun(@(x,y) real(x)+real(y)*1i,db_expected,db_unexpected);
    dataout = dbfun(@(x,y) comment(x,comment(y)),dataout,dbdata);
end