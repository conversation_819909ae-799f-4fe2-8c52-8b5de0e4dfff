function [xsa,xsfactor,xtrend,m] = seasf_na_nove_iris(x,varargin)
%
% SEASF  Quarterly seasonal filter.
%
% Syntax:
%   [xsa,xsfactor,xtrend,s] = seasf(x,...)
% Arguments:
%   xsa,xsfactor,xtrend,x tseries; m model
% Options:
%   'log' logical (false)
%   'stdseason' numeric (1)
%   'stdnoise' numeric (1)
%
% IRIS Toolbox 2006/10/26 

default = {...
  'log',false,...
  'stdseason',1,...
  'stdnoise',1,...
};
options = passopt(default,varargin{:});

% function body ---------------------------------------------------------------------------------------------

if options.log == true, x = log(x); end

[nper,nx] = size(x);

m = loadstruct('private/seasf.mat');
m = assign(m,'epsilon',options.stdnoise,'std_omega',1,'std_theta',options.stdseason);

xsfactor = nan(size(x));
xtrend = nan(size(x));
for i = 1 : nx
  data = {transpose(x(:,i)),nan([5,nper]),nan([3,nper]),1:nper};
  [mlogl,se2,pred,smooth] = kalmanf(m,data);
  % tnd seas drift seas{-1} seas{-2}
  xtrend(:,i) = transpose(smooth.mean{2}(1,:));
  xsfactor(:,i) = transpose(smooth.mean{2}(2,:));
end
xsa = x - xsfactor;

if options.log == true
  xsa = exp(xsa);
  xsfactor = exp(xsfactor);
  xtrend = exp(xtrend);
end

end % of primary function -----------------------------------------------------------------------------------