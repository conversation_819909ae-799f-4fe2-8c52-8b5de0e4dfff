function R = expectations_design(R,elist)
%
% Manipulation of expectations
% 

% keyboard;

if true % Switch ON/OFF
    %% Counts

    % # of model shocks (structural, ignoring measurement errors)
    ne = length(elist);

    % Maximum degree of forward-lookingness
    fmax = size(R,2)/ne-1;% '-1' because the first set of multipliers corresponds to the block of current (time t) shocks
    if fmax~=floor(fmax)
        error('Something went terribly wrong...');
    end

    %% Standard state space solution with forward-looking expansion <to be modified in this function>
    %  ksi{t} = F*ksi{t-1} + R{0}*eta{t} + R{1}*eta{t+1} + R{2}*eta{t+2} + ...

    %% [1] Reduce visibility (degree of forward-lookingness)
    % -> Here we impose truncated state space solution given fixed # of visible periods
    max_forw_pers = 5;
    if max_forw_pers>=fmax
        disp(' --> Degree of forward-lookingness has NOT been truncated...');
        max_forw_pers = fmax;
    else
        R(:,(max_forw_pers+1)*ne+1:end) = 0;
    end
    
    %% [2] Reduce (discount) the impact of shock multipliers
    % -> Slight state space modification (using a vector of weights 'w'): 
    %       ksi{t} = F*ksi{t-1} + R{0}*eta{t} + w{1}*R{1}*eta{t+1} + ... + w{max}*R{max}*eta{t+max}
    w = [1 0.8 0.6 0.4 0.2];%linspace(1,0.1,max_forw_pers);
	if length(w)>max_forw_pers
		w = w(1:max_forw_pers);
	end
    for iper = 1:length(w)
        indnow = (iper*ne+1):(iper*ne+ne);% t=0 is skipped
        R(:,indnow) = w(iper) * R(:,indnow);
    end

    %% [3] Impose blindness for selected model shocks
    % -> We can arbitrarily neglect from responses to certain anticipated shocks

%     anticip_list = {'eps_Nstar','eps_Pstar','eps_USDEUR','eps_pBrent','eps_p_other','eps_Istar','eps_target','eps_uip','eps_mpolicy'};
    anticip_list = {'eps_Nstar','eps_Pstar','eps_Istar','eps_target','eps_uip','eps_mpolicy','eps_pREG'};
    inds = 1:ne;
    inds = inds(:);
    [~,where] = ismember(anticip_list(:),elist);
    if any(where==0)
       error('Shock not found among model shocks'); 
    end
    inds(where) = [];

    % Modify all visible shock multipliers
    % Rules: * current time multipliers stay untouched (unexpectated model responses)
    %        * {t+1} multipliers stay untouched (effects of potential frontloading)
    %        * {t+2}:{t+max} multipliers are turned off for most shocks, except those from 'anticip_list'
    nblocks = (2:max_forw_pers+1)*ne;
    zeroinds = repmat(nblocks,length(inds),1)+repmat(inds,1,length(nblocks));
    R(:,sort(zeroinds(:))) = 0;
    
end

end %<eof>