%**************************************************************************
function OUT = ssmodel_FB(P, varargin)
%**************************************************************************

settings = varargin{1,1};

%% Pass some parameters
V.dot_pstar_energy_tilde_ = P.dot_pstar_energy_tilde_;
V.dot_pstar_tilde_        = P.dot_pstar_tilde_;
V.dot_cpi_star_tilde_     = P.dot_cpi_star_tilde_;
V.i_star_                 = P.i_star_;
V.i_star_us_              = P.i_star_us_;
V.usdeur_                 = P.usdeur_;
V.dot_usdeur_             = P.dot_usdeur_;


%% STEADY STATE
V.dot_y_star_trend_         = P.foreign_output_growth;   
V.dot_y_star_trend_fund     = V.dot_y_star_trend_;
V.dot_y_star_trend          = V.dot_y_star_trend_;
V.dot_y_star                = V.dot_y_star_trend_;
V.dot_y_star_trend_shift    = V.dot_y_star_trend/V.dot_y_star_trend_fund;
V.y_star_gap                = 1;

V.dot_cpi_star_tilde        = P.dot_cpi_star_tilde_;
V.dot_cpi_star_tilde4       = V.dot_cpi_star_tilde^4;
V.e4_dot_cpi_star_tilde4    = V.dot_cpi_star_tilde^4;

V.dot_pstar_tilde           = P.dot_pstar_tilde_;
V.dot_pstar_tilde4          = P.dot_pstar_tilde_^4;
V.e4_dot_pstar_tilde4       = V.dot_pstar_tilde4; 

V.dot_pstar_energy_tilde    = P.dot_pstar_energy_tilde_;
V.dot_pstar_energy_tilde4   = P.dot_pstar_energy_tilde_^4; 
V.energy_share_ppi_star_gap = 1;
V.dot_pstar_RP_tilde        = 1;

V.dot_pstar_other_tilde     = (V.dot_pstar_tilde/(V.dot_pstar_energy_tilde/V.dot_pstar_RP_tilde)^(P.energy_share_ppi_star*V.energy_share_ppi_star_gap))^(1/(1-P.energy_share_ppi_star*V.energy_share_ppi_star_gap));
P.dot_pstar_other_tilde_    = V.dot_pstar_other_tilde;
V.dot_pstar_other_tilde4    = P.dot_pstar_other_tilde_^4;
V.e_dot_pstar_other_tilde   = P.dot_pstar_other_tilde_;

V.usdeur                    = P.usdeur_;
V.dot_usdeur                = P.dot_usdeur_;
V.dot_usdeur4               = P.dot_usdeur_^4;
V.e_dot_usdeur              = P.dot_usdeur_;
V.prem_usdeur               = P.i_star_us_/P.i_star_;
V.prem_usdeur_gap           = 1;
V.z_gap                     = 1;

V.shadow_rate_gap           = 1;
V.r_star_gap                = 1;
V.i_star                    = P.i_star_;
V.i_star_eq                 = P.i_star_;
V.i_star_eu                 = P.i_star_;
V.i_star_us                 = P.i_star_us_;
V.dot_z_eq                  = 1/(P.dot_usdeur_*V.dot_pstar_tilde);
V.dot_z_eq_                 = V.dot_z_eq;

% Measurement block

if P.filtering
    % Measurements
    M.mes_Y_STAR                    = 1 + 1i*100*log(V.dot_y_star_trend);
    
    if ~settings.FB_gdp_decomp
        M.mes_Y_STAR_GAP                = 100*log(V.y_star_gap);
        M.mes_Y_STAR_TREND              = 1 + 1i*100*log(V.dot_y_star_trend);
        M.mes_DOT_Y_STAR_TREND_SHIFT	= 400*(V.dot_y_star_trend_shift - 1);          
        

    end
    
    
    if ~settings.FB_prem_decomp
        M.mes_PREM_USDEUR               = 400*(V.prem_usdeur-1);            
        

    end
        
    M.mes_PSTAR_OTHER_TILDE         = 1 + 1i*100*log(V.dot_pstar_other_tilde);
    M.mes_PSTAR_ENERGY_TILDE        = 1 + 1i*100*log(V.dot_pstar_energy_tilde);
    M.mes_PSTAR_TILDE               = 1 + 1i*100*log(V.dot_pstar_tilde);
    M.mes_CPI_STAR_TILDE            = 1 + 1i*100*log(V.dot_cpi_star_tilde);
    
    M.mes_I_STAR                    = 400*(V.i_star-1);
    M.mes_I_STAR_EQ                 = 400*(V.i_star_eq-1);
    M.mes_I_STAR_EU                 = 400*(V.i_star_eu-1);  
    M.mes_I_STAR_US                 = 400*(V.i_star_us-1);
    
    M.mes_USDEUR                    = 100*log(V.usdeur);
    
    % Observations
    M.obs_Y_STAR                    = M.mes_Y_STAR;

    if ~settings.FB_gdp_decomp
        M.obs_Y_STAR_GAP                =  M.mes_Y_STAR_GAP;
        M.obs_Y_STAR_TREND              =  M.mes_Y_STAR_TREND;
        M.obs_DOT_Y_STAR_TREND_SHIFT    =  M.mes_DOT_Y_STAR_TREND_SHIFT;
    end
    
    if ~settings.FB_prem_decomp
        M.obs_PREM_USDEUR               =  M.mes_PREM_USDEUR;
    end
    
    M.obs_PSTAR_OTHER_TILDE         = M.mes_PSTAR_OTHER_TILDE;
    M.obs_PSTAR_ENERGY_TILDE        = M.mes_PSTAR_ENERGY_TILDE;   
    M.obs_PSTAR_TILDE               = M.mes_PSTAR_TILDE;
    M.obs_CPI_STAR_TILDE            = M.mes_CPI_STAR_TILDE;
    
    M.obs_I_STAR                    = M.mes_I_STAR;
    M.obs_I_STAR_EQ                 = M.mes_I_STAR_EQ;
    M.obs_I_STAR_EU                 = M.mes_I_STAR_EU;
    M.obs_I_STAR_US                 = M.mes_I_STAR_US;
    
    M.obs_USDEUR                    = M.mes_USDEUR;  
end

%% Passing params, vars and measurements OUT
OUT = P;

f = fieldnames(V);
for ii = 1:length(f)
    OUT.(f{ii}) = V.(f{ii});
end

if P.filtering
    f = fieldnames(M);
    for ii = 1:length(f)
        OUT.(f{ii}) = M.(f{ii});
    end
end

% <end>