function [dout, varargout] = make_outofcore_FB(d, p, settings)
% FUNCTION NAME:
%   make_outofcore
%
% DESCRIPTION:
%  This function adds some noncore model variables into database, based on
%  the model, for reporting and additional computations
%
% INPUT:
%   d - database
%   p - parameters
%   settings - options
%
% OUTPUT:
%   dout - (struct) database with extra vars
%   vargout - export islog structure
%
% ASSUMPTIONS AND LIMITATIONS:
%   None
%
% REVISION HISTORY:
%   05/08/2018 - FB,
%       * Initial implementation
%   14/02/2023 - FB,
%       * update for new version, levels added
%   22/03/2023 - FB
%       * added forecast database
%   21/12/2023 - JZ
%       * usdeur vs zz_usdeur discrepancy
%
islog = dbempty();

if ~p.filtering
    sfore = settings.start_pred;
    efore = settings.end_comp;
    ehist = settings.ehist;
else
    sfore = NaN;
    ehist = settings.ehist;
end

% outofcore on historical range
[d, islog_] = mk_outofcore_FB_history(d, p, settings);
islog = dbmerge(islog, islog_);


%% Making extra transformations on fcast of foreign variables that are later used for reporting
% outofcore on forecast range
if ~isnan(sfore)
    namelist_ = {'y_star',...
        'usdeur',...
        'pstar_tilde','pstar_other_tilde','pstar_energy_tilde','cpi_star_tilde'};
    commentlist_ = {'Real Foreign Demand (100*log)',...
        'Exchange Rate (EUR/USD) (100*log)',...
        'Foreign Price Level (USD) (100*log)','Non-Energy Price Level (USD) (100*log)','Energy Price Level (USD) (100*log)','Consurmer Price Index (100*log)'};
        
    for ii = 1 : length(namelist_)
        if strcmp(namelist_{ii},'usdeur') % dot_usdeur vs usdeur discrepancy due to error-correction term: dots from the model level
            dot_aux = d.([namelist_{ii}])(sfore:efore)./d.([namelist_{ii}]){-1}(sfore:efore);
            tmp_ = cumprod(dot_aux);
            d.(['zz_' namelist_{ii}]) = d.(['zz_' namelist_{ii}]);
            d.(['zz_' namelist_{ii}])(sfore:efore) = d.(['zz_' namelist_{ii}])(sfore-1) .* tmp_;
            islog.(['zz_' namelist_{ii}]) = false;
        else
            tmp_ = cumprod(d.(['dot_' namelist_{ii}])(sfore:efore));
            d.(['zz_' namelist_{ii}]) = d.(['zz_' namelist_{ii}]);
            d.(['zz_' namelist_{ii}])(sfore:efore) = d.(['zz_' namelist_{ii}])(sfore-1) .* tmp_;
            islog.(['zz_' namelist_{ii}]) = false;
        end
    end
    for ii = 1 : length(namelist_)
        d.(['zz_' namelist_{ii}]).Comment = commentlist_(ii);
    end
end
% output
dout = d;
if nargout > 1
    varargout{1} = islog;
end

%% SubFunctions

%% MODEL VARIABLES - HISTORY
function [dout, varargout] = mk_outofcore_FB_history(d, p, settings)

%% Initialize


dout = d;
islog = dbempty();


%% Levels in ZZ vars
% this is extra, consistency with g3 naming

% keep lists in same order
namelist_ = {'y_star',...
    'i_star','i_star_eu','i_star_eq',...
    'usdeur',...
    'pstar_tilde','pstar_other_tilde','pstar_energy_tilde','cpi_star_tilde'};
commentlist_ = {'Real Foreign Demand (100*log)',...
    '3-M Foreign Rates (percent p.a.)','3-M Neutral Foreign Rates (percent p.a.)','Effective 3-M Foreign Rates (percent p.a.)',...
    'Exchange Rate (EUR/USD) (100*log)',...
    'Foreign Price Level (USD) (100*log)','Non-Energy Price Level (USD) (100*log)','Energy Price Level (USD) (100*log)','Consurmer Price Index (100*log)'};

for ii = 1 : length(namelist_)
    dout.(['zz_' namelist_{ii}]) = exp(d.(['mes_' upper(namelist_{ii})])/100);
    islog.(['zz_' namelist_{ii}]) = false;
end
for ii = 1 : length(namelist_)
    dout.(['zz_' namelist_{ii}]).Comment = commentlist_(ii);
end

%% SS series

rng_       =  settings.shist:settings.end_comp;
SSvarslist = {	'dot_y_star_trend','dot_y_star',...
    'i_star','i_star_eu',...
    'dot_usdeur4','dot_usdeur','usdeur','prem_usdeur','e_dot_usdeur',...
    'dot_pstar_tilde4','dot_pstar_tilde','dot_pstar_other_tilde4','dot_pstar_other_tilde',...
    'dot_pstar_energy_tilde4','dot_pstar_energy_tilde','energy_share_ppi_star_gap','dot_pstar_RP_tilde',...
    'dot_cpi_star_tilde',...
    'dot_z_eq',...
    'e_dot_pstar_other_tilde','e4_dot_pstar_tilde4'};

for ii = 1:length(SSvarslist)
    name        = [SSvarslist{ii} '_ss'];
    dout.(name) = tseries(rng_, p.(SSvarslist{ii}));
    islog.(name)= false;
    dout.(name) = comment(dout.(name),[char(get(d.(SSvarslist{ii}),'comment')),' SS']);
end


%% Real ex. rate

d.dot_z = 1/(d.dot_usdeur*dout.dot_pstar_tilde);
d.dot_z = comment(d.dot_z,'RER EUR/USD Trend (QoQ)');

dout.dot_z = d.dot_z;

%% add moving averages as yoy data

% here are only those not in model, otherwise conflict!
add_names = {   'dot_y_star','dot_y_star_trend','y_star_gap', ...
    'i_star','i_star_eu',...
    'energy_share_ppi_star_gap',...
    'dot_pstar_RP_tilde',...
    'e_dot_usdeur',...
    'e_dot_pstar_other_tilde', ...
    'dot_z',...
    };

dout = dbbatch(dout, '$04', 'd.$0*d.$0{-1}*d.$0{-2}*d.$0{-3}', 'nameList', add_names);
islog = dbmerge(islog,dbbatch(dout, '$04', 'false', 'nameList', add_names,'fresh',true));

%--add legend to Y-o-Y growts--%
for i = 1:length(add_names)
    name        = [add_names{i}, '4'];
    dout.(name) = comment(dout.(name), strrep(get(dout.(name(1:end-1)),'comment'), 'QoQ', 'YoY'));
end

% if nargout > 1
%     varargout{1} = islog;
% end


%% Calculate contributions in selected model equations

% add contributions in equation dot_pstar_tilde
dout.dot_pstar_tilde_contrib_energy     = exp(p.energy_share_ppi_star*dout.energy_share_ppi_star_gap*log(dout.dot_pstar_energy_tilde/dout.dot_pstar_RP_tilde));
dout.dot_pstar_tilde_contrib_nonener    = exp((1-p.energy_share_ppi_star*dout.energy_share_ppi_star_gap)*log(dout.dot_pstar_other_tilde));
dout.dot_pstar_tilde_contrib_resid      = exp(dout.eps_pstar_tilde);

dout.dot_pstar_tilde_contrib_energy     = comment(dout.dot_pstar_tilde_contrib_energy,[char(get(d.dot_pstar_tilde,'comment')),': Energy Prices Contribution']);
dout.dot_pstar_tilde_contrib_nonener    = comment(dout.dot_pstar_tilde_contrib_nonener,[char(get(d.dot_pstar_tilde,'comment')),': Core Prices Contribution']);
dout.dot_pstar_tilde_contrib_resid      = comment(dout.dot_pstar_tilde_contrib_resid,[char(get(d.dot_pstar_tilde,'comment')),': Residual Contribution']);

islog.dot_pstar_tilde_contrib_energy	= true;
islog.dot_pstar_tilde_contrib_nonener   = true;
islog.dot_pstar_tilde_contrib_resid     = true;

% add contributions in equation dot_pstar_other_tilde

dout.dot_pstar_other_tilde_contrib_ogap      = exp(p.a_pstar_ystar*log(dout.y_star_gap));
dout.dot_pstar_other_tilde_contrib_lagdev    = exp(p.a_pstar_AR*(log(dout.dot_pstar_other_tilde{-1})));
dout.dot_pstar_other_tilde_contrib_expdev    = exp(1*(1 - p.a_pstar_AR)*(log(dout.e_dot_pstar_other_tilde)));
% dout.dot_pstar_other_tilde_contrib_energap   = exp(-p.a_pstar_RER*p.energy_share_ppi_star*(log(dout.pstar_energy_tilde_gap)));
dout.dot_pstar_other_tilde_contrib_usdeur    = exp(p.a_pstar_RER*log(dout.z_gap));
dout.dot_pstar_other_tilde_contrib_resid     = exp(dout.eps_pstar_other_tilde);

dout.dot_pstar_other_tilde_contrib_ogap    = comment(dout.dot_pstar_other_tilde_contrib_ogap,[char(get(d.dot_pstar_other_tilde,'comment')),': O. Gap Contribution']);
dout.dot_pstar_other_tilde_contrib_lagdev  = comment(dout.dot_pstar_other_tilde_contrib_lagdev,[char(get(d.dot_pstar_other_tilde,'comment')),': Lag Deviation Contribution']);
dout.dot_pstar_other_tilde_contrib_expdev  = comment(dout.dot_pstar_other_tilde_contrib_expdev,[char(get(d.dot_pstar_other_tilde,'comment')),': Exp. Deviation Contribution']);
% dout.dot_pstar_other_tilde_contrib_energap = comment(dout.dot_pstar_other_tilde_contrib_energap,[char(get(d.dot_pstar_other_tilde,'comment')),': energy gap Contribution']);
dout.dot_pstar_other_tilde_contrib_usdeur  = comment(dout.dot_pstar_other_tilde_contrib_usdeur,[char(get(d.dot_pstar_other_tilde,'comment')),': USD/EUR Contribution']);
dout.dot_pstar_other_tilde_contrib_resid   = comment(dout.dot_pstar_other_tilde_contrib_resid,[char(get(d.dot_pstar_other_tilde,'comment')),': Residual Contribution']);

islog.dot_pstar_other_tilde_contrib_ogap       = true;
islog.dot_pstar_other_tilde_contrib_lagdev     = true;
islog.dot_pstar_other_tilde_contrib_expdev     = true;
% islog.dot_pstar_other_tilde_contrib_energap    = true;
islog.dot_pstar_other_tilde_contrib_usdeur     = true;
islog.dot_pstar_other_tilde_contrib_resid      = true;

% GDP decomposition to trend and gap
dout.dot_y_star_contrib_gap         = ((dout.y_star_gap/dout.y_star_gap{-1}));
dout.dot_y_star_contrib_trend_fund  = (dout.dot_y_star_trend_fund);
dout.dot_y_star_contrib_trend_shift	= (dout.dot_y_star_trend_shift);

dout.dot_y_star_contrib_gap         = comment(dout.dot_y_star_contrib_gap,[char(get(d.dot_y_star,'comment')),': O. Gap Contribution']);
dout.dot_y_star_contrib_trend_fund 	= comment(dout.dot_y_star_contrib_trend_fund,[char(get(d.dot_y_star,'comment')),': Fundament Trend Contribution']);
dout.dot_y_star_contrib_trend_shift = comment(dout.dot_y_star_contrib_trend_shift,[char(get(d.dot_y_star,'comment')),': One-off Trend Contribution']);

islog.dot_y_star_contrib_gap        = true;
islog.dot_y_star_contrib_trend_fund	= true;

% y_star_gap decomposition
dout.y_star_gap_contrib_lag     = exp(p.a_ystar_AR*log(dout.y_star_gap{-1}));
dout.y_star_gap_contrib_rmci    = exp(-p.a_ystar_IR*(log(dout.r_star_gap{-1})));
dout.y_star_gap_contrib_usdeur  = exp(p.a_ystar_RER*(log(dout.z_gap)));
% dout.y_star_gap_contrib_energap = exp(-p.a_ystar_RER*p.energy_share_ppi_star*(log(dout.pstar_energy_tilde_gap)));
dout.y_star_gap_contrib_resid   = exp(dout.eps_y_star_gap);

dout.y_star_gap_contrib_lag     = comment(dout.y_star_gap_contrib_lag,[char(get(d.y_star_gap,'comment')),': O. Gap Lag Contribution']);
dout.y_star_gap_contrib_rmci    = comment(dout.y_star_gap_contrib_rmci,[char(get(d.y_star_gap,'comment')),': RMCI Contribution']);
dout.y_star_gap_contrib_usdeur  = comment(dout.y_star_gap_contrib_usdeur,[char(get(d.y_star_gap,'comment')),': USD/EUR exchange rate Contribution']);
% dout.y_star_gap_contrib_energap = comment(dout.y_star_gap_contrib_energap,[char(get(d.y_star_gap,'comment')),': energy gap Contribution']);
dout.y_star_gap_contrib_resid   = comment(dout.y_star_gap_contrib_resid,[char(get(d.y_star_gap,'comment')),': Residual Contribution']);

islog.y_star_gap_contrib_lag    = true;
islog.y_star_gap_contrib_rmci   = true;
islog.y_star_gap_contrib_usdeur = true;
% islog.y_star_gap_contrib_energap = true;
islog.y_star_gap_contrib_resid  = true;

% i_star decomposition
dout.i_star_contrib_ogap       = exp((1-p.a_istar_AR)*p.a_istar_ystar*log(dout.y_star_gap));
%dout.i_star_contrib_inflexpdev2 = exp((1-p.a_istar_AR)*p.a_istar_pi*((log(dout.e4_dot_p_star4_tilde))/4-log(p.dot_pstar_other_tilde_))); % ?
dout.i_star_contrib_inflexpdev = exp((1-p.a_istar_AR)*p.a_istar_pi*((log(dout.e4_dot_cpi_star_tilde4))-log(p.dot_cpi_star_tilde_^4)));
dout.i_star_contrib_smoothing  = exp(p.a_istar_AR*log((dout.i_star_eu{-1}))); %% change bcs of shadow rates
dout.i_star_contrib_ss         = exp((1-p.a_istar_AR)*(log(dout.i_star_eq)));
dout.i_star_contrib_resid      = exp(dout.eps_Istar);

dout.i_star_contrib_ogap       = comment(dout.i_star_contrib_ogap,[char(get(d.i_star,'comment')),': O. Gap Contribution']);
dout.i_star_contrib_inflexpdev = comment(dout.i_star_contrib_inflexpdev,[char(get(d.i_star,'comment')),': Exp. Inflation Deviation Contribution']);
dout.i_star_contrib_smoothing  = comment(dout.i_star_contrib_smoothing,[char(get(d.i_star,'comment')),': Smoothing Contribution']);
dout.i_star_contrib_ss         = comment(dout.i_star_contrib_ss,[char(get(d.i_star,'comment')),': S. State Contribution']);
dout.i_star_contrib_resid      = comment(dout.i_star_contrib_resid,[char(get(d.i_star,'comment')),': Residual Contribution']);

islog.i_star_contrib_ogap       = true;
islog.i_star_contrib_inflexpdev = true;
islog.i_star_contrib_smoothing  = true;
islog.i_star_contrib_ss         = true;
islog.i_star_contrib_resid      = true;

% cpi decomposition
dout.dot_cpi_star_tilde_contrib_pstartilde = exp(p.a_cpistar_pstar*log(dout.dot_pstar_tilde));
dout.dot_cpi_star_tilde_contrib_cpi_ss = exp((1-p.a_cpistar_pstar)*log(dout.dot_cpi_star_tilde_ss));
dout.dot_cpi_star_tilde_contrib_resid = exp(dout.eps_dot_cpi_star_tilde);

dout.dot_cpi_star_tilde_contrib_pstartilde = comment(dout.dot_cpi_star_tilde_contrib_pstartilde,[char(get(d.dot_cpi_star_tilde,'comment')),': Foreing PPI Contribution']);
dout.dot_cpi_star_tilde_contrib_cpi_ss = comment(dout.dot_cpi_star_tilde_contrib_cpi_ss,[char(get(d.dot_cpi_star_tilde,'comment')),': CPI Target Contribution']);
dout.dot_cpi_star_tilde_contrib_resid = comment(dout.dot_cpi_star_tilde_contrib_resid,[char(get(d.dot_cpi_star_tilde,'comment')),': Residual Contribution']);

islog.dot_cpi_star_tilde_contrib_pstartilde = true;
islog.dot_cpi_star_tilde_contrib_cpi_ss = true;
islog.dot_cpi_star_tilde_contrib_resid = true;

%% Output
if nargout > 1
    varargout{1} = islog;
end

