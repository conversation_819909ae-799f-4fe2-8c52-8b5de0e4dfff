function [db_add] = changedata_FB(db_orig, settings, SS)
%*************************************************************************
% CHANGEDATA
%    - loads and changes (optional) official data
%    - changed data are reported in official reports!!!
%    - you must change tseries defined in histcore database :-(
%    - the same code for filtering and forecasting - keep consistency!!!
%
% @ last revision: JZ, March 2023 
%*************************************************************************
%%

db_add = db_orig;

%% Equilibrium/neutral foreign interest rate
db_add.equilibrium_i_star = tseries(range(db_add.net4_euribor3m), 3.5);
db_add.equilibrium_i_star(qq(2000, 1):qq(2018, 2)) = linspace(3.5, 2.5, length(qq(2000, 1):qq(2018, 2)));
db_add.equilibrium_i_star(qq(2018, 3):end) = 2.5;

%% Weight for PPI energy growth
db_add.weight_dot_pstar_energy_tilde	= 1 - db_orig.weight_ppi_emu_other * db_orig.ppi_emu_other / db_orig.ppi_emu;
db_add.weight_pstar_energy_tilde      	= 1 - db_orig.weight_ppi_emu_other;

%% Additional data for reporting
db_add.p_brentUSD_tilde	= db_orig.brent; %[USD denomination]
db_add.p_brent_tilde	= db_orig.brent / db_orig.usdeur;  %[EUR denomination]
db_add.p_brent_tilde	= comment(db_add.p_brent_tilde,'Brent Oil Price (EUR)');

end

%<eof>