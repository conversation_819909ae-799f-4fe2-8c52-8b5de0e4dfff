function [dout,varargout] = make_transformations_FB(d, p, tuneflag)

%   make_transformations_FB
%
% DESCRIPTION:
%   Enrich the given database d with useful variable transformations prefixed
%   with zz_. If the tuneflag is true, then also variables of the form
%   tune_<varname> are transformed (in the same way as varname) and stored
%   as zz_tune_<varname>
%   In islog, the information similar to get(model, 'log') is returned
%   about all newly added variables
%
% INPUT:
%   d - database
%   p - parameters
%   tuneflag - tranforming tunes -filter/forecast
%
% OUTPUT:
%   dout - (struct) database with extra vars
%   vargout - export islog structure
%
% ASSUMPTIONS AND LIMITATIONS:
%   None
%
% REVISION HISTORY:
%   05/08/2018 - FB,
%       * Initial implementation
%   14/02/2023 - TP,
%       * update for new version, levels added
%   22/03/2023 - FB,TP
%       * added forecast database
%   11/04/2023 - FB,TP
%       * editing lists of transformations


islog = dbempty();

% the following will be transformed as 400*(d.$0-1)
trans0_names = {'dot_pstar_tilde_contrib_energy','dot_pstar_tilde_contrib_nonener','dot_pstar_tilde_contrib_resid',...
    'dot_pstar_other_tilde_contrib_ogap','dot_pstar_other_tilde_contrib_lagdev','dot_pstar_other_tilde_contrib_expdev',...
    'dot_pstar_other_tilde_contrib_usdeur','dot_pstar_other_tilde_contrib_resid',...
    'dot_y_star_contrib_gap','dot_y_star_contrib_trend_fund','dot_y_star_contrib_trend_shift',...
    'dot_y_star_trend_fund','dot_y_star_trend_shift','dot_y_star_trend',...
    'i_star_contrib_ogap','i_star_contrib_inflexpdev','i_star_contrib_smoothing','i_star_contrib_ss','i_star_contrib_resid',...
    'dot_pstar_energy_tilde','dot_cpi_star_tilde_contrib_pstartilde','dot_cpi_star_tilde_contrib_cpi_ss','dot_cpi_star_tilde_contrib_resid'};


% the following will be transformed as 100*(variable^4-1)
trans1_names = {'i_star_eu','i_star_eq','i_star_shadow','i_star_eonia','i_star','i_star_us',...
    'dot_pstar_other_tilde','dot_pstar_energy_tilde','dot_pstar_tilde','dot_cpi_star_tilde','dot_pstar_RP_tilde','e_dot_pstar_other_tilde',...
    'dot_y_star','dot_y_star_trend_fund','dot_y_star_trend_shift','dot_y_star_trend', ...
    'dot_usdeur','dot_z_eq','dot_z','e_dot_usdeur','prem_usdeur'};

% the following will be transformed as 100*log(variable/variable_ss)
trans2_names = {};

% the following will be transformed as 100*(variable) (but are still in logs)
trans3_names = {};

% the following will be transformed as 100*variable (are not in logs)
eList = dbnames(d,'nameFilter','^eps_\w*');
omegaList = dbnames(d,'nameFilter','^omega_\w*');
trans4_names = [eList, omegaList];

% the following will be be transformed as 100*(variable-1)
trans6_names = {'y_star_gap','z_gap','energy_share_ppi_star_gap','r_star_gap', 'kappa_usdeur','e4_dot_pstar4_tilde',...
    'y_star_gap_contrib_lag','y_star_gap_contrib_rmci','y_star_gap_contrib_usdeur',...
    'y_star_gap_contrib_resid'};

% the following will be transformed as
% 100*(variable*variable{-1}*variable{-2}*variable{-3}-1) and appeding 4 at
% the end of variable name
trans7_names = {'dot_pstar_other_tilde','dot_y_star','dot_y_star_trend', 'dot_y_star_trend_shift', ...
    'dot_pstar_energy_tilde','dot_usdeur','dot_pstar_tilde','dot_cpi_star_tilde', ...
    'dot_pstar_RP_tilde','dot_z_eq'...
    'dot_usdeur','e_dot_pstar_other_tilde','e_dot_usdeur',...
    'y_star_gap','dot_y_star_trend_shift','dot_y_star_trend_fund',...
    'dot_pstar_tilde_contrib_energy','dot_pstar_tilde_contrib_nonener','dot_pstar_tilde_contrib_resid',...
    'dot_pstar_other_tilde_contrib_ogap','dot_pstar_other_tilde_contrib_lagdev','dot_pstar_other_tilde_contrib_expdev',...
    'dot_pstar_other_tilde_contrib_usdeur','dot_pstar_other_tilde_contrib_resid',...
    'dot_y_star_contrib_gap','dot_y_star_contrib_trend_fund','dot_y_star_contrib_trend_shift',...
    'dot_pstar_energy_tilde','dot_cpi_star_tilde_contrib_pstartilde','dot_cpi_star_tilde_contrib_cpi_ss','dot_cpi_star_tilde_contrib_resid',...
    'dot_z'};

% the following will be be transformed as 100*log(variable)
trans8_names = {''};

% the following will be transformed as variable*variable{-1}*variable{-2}*variable{-3}
trans9_names = {'dot_pstar_other_tilde','dot_y_star','dot_y_star_trend_fund', ...
    'dot_pstar_energy_tilde','dot_usdeur','dot_pstar_tilde','dot_cpi_star_tilde', ...
    'dot_pstar_RP_tilde', 'dot_z_eq' ...
    'dot_usdeur','e_dot_pstar_other_tilde',...%,'y_star_gap','i_star','i_star_eq' ...
    };

% the following will be  transformed as exp(variable/100)
if p.filtering
    trans10_names = {'obs_Y_STAR','obs_Y_STAR_TREND',...
        'obs_I_STAR_EU','obs_I_STAR','obs_I_STAR_EQ','obs_I_STAR_US','obs_USDEUR',...
        'obs_PSTAR_ENERGY_TILDE','obs_PSTAR_OTHER_TILDE','obs_PSTAR_TILDE',...
        'obs_CPISTAR_TILDE','obs_PREM_USDEUR','mes_PREM_USDEUR',...
        'mes_Y_STAR','mes_Y_STAR_TREND',...
        'mes_I_STAR_EU','mes_I_STAR','mes_I_STAR_EQ','mes_I_STAR_US','mes_USDEUR',...
        'mes_PSTAR_ENERGY_TILDE','mes_PSTAR_OTHER_TILDE','mes_PSTAR_TILDE',...
        'mes_CPI_STAR_TILDE'
        };
    trans11_names = {'mes_Y_STAR_GAP','obs_Y_STAR_GAP'}; % not transformed, only add zz_
else
    trans10_names ={};
    trans11_names ={};
end;
%% Transformations
dout = d;

%0:  400*(d.$0-1)
dout = dbbatch(dout, 'zz_$0', '100*(d.$0^4-1)', 'nameList', trans0_names);

islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans0_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', '400*(d.tune_$0-1)', ...
        'nameList', trans0_names);
    islog = dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans0_names,'fresh',true));
end


%1:  100*(d.$0^4-1)
dout = dbbatch(dout, 'zz_$0', '100*(d.$0^4-1)', 'nameList', trans1_names);

islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans1_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', '400*(d.tune_$0-1)', ...
        'nameList', trans1_names);
    islog = dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans1_names,'fresh',true));
end

%2: 100*log(d.$0/p.$0)
dout = dbbatch(dout, 'zz_$0', '100*log(d.$0/p.$0)', 'nameList', trans2_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans2_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', '100*log(d.tune_$0/p.$0)', ...
        'nameList', trans2_names);
    islog =  dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans2_names,'fresh',true));
end

%3: 100*(variable) (but are still in logs)
dout = dbbatch(dout, 'zz_$0', '100*d.$0', 'nameList', trans3_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans3_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', '100*d.tune_$0', ...
        'nameList', trans3_names);
    islog =  dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans3_names,'fresh',true));
end

%4: 100*(variable) (but not in logs)
dout = dbbatch(dout, 'zz_$0', '100*d.$0', 'nameList', trans4_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans4_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', '100*d.tune_$0', ...
        'nameList', trans4_names);
    islog =  dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans4_names,'fresh',true));
end
%6: 100*(variable-1)
dout = dbbatch(dout, 'zz_$0', '100*(d.$0-1)', 'nameList', trans6_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans6_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', '100*(d.tune_$0-1)', ...
        'nameList', trans6_names);
    islog =  dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans6_names,'fresh',true));
end

%7: 100*(variable*variable{-1}*variable{-2}*variable{-3}-1), append 4 on
%the name
dout = dbbatch(dout, 'zz_$04', '100*(d.$0*d.$0{-1}*d.$0{-2}*d.$0{-3}-1)', 'nameList', trans7_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$04', 'false', 'nameList', trans7_names,'fresh',true));
%--add legend to Y-o-Y growts--%
for i = 1:length(trans7_names)
    name        = ['zz_', trans7_names{i}, '4'];
    dout.(name) = comment(dout.(name), strrep(get(dout.(name(1:end-1)),'comment'), 'QoQ', 'YoY'));
end

%8: 100*log(var)
dout = dbbatch(dout, 'zz_$0', '100*log(d.$0)', 'nameList', trans8_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans8_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', '100*log(d.tune_$0)', ...
        'nameList', trans8_names);
    islog =  dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans8_names,'fresh',true));
end

%9: 100*(variable*variable{-1}*variable{-2}*variable{-3}), append 4 on
%the name
dout = dbbatch(dout, '$04', 'd.$0*d.$0{-1}*d.$0{-2}*d.$0{-3}', 'nameList', trans9_names);
islog = dbmerge(islog,dbbatch(dout, '$04', 'false', 'nameList', trans9_names,'fresh',true));
%--add legend to Y-o-Y growts--%
for i = 1:length(trans9_names)
    name        = [trans9_names{i}, '4'];
    dout.(name) = comment(dout.(name), strrep(get(dout.(name(1:end-1)),'comment'), 'QoQ', 'YoY'));
end

%10: exp(d.$0/100)
dout = dbbatch(dout, 'zz_$0', 'exp(d.$0/100)', 'nameList', trans10_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans10_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', 'exp(d.tune_$0/100)', ...
        'nameList', trans10_names);
    islog =  dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans10_names,'fresh',true));
end

%11: d.$0
dout = dbbatch(dout, 'zz_$0', 'd.$0', 'nameList', trans11_names);
islog = dbmerge(islog,dbbatch(dout, 'zz_$0', 'false', 'nameList', trans11_names,'fresh',true));
if tuneflag
    dout = dbbatch(dout, 'zz_tune_$0', 'd.tune_$0', ...
        'nameList', trans11_names);
    islog =  dbmerge(islog,dbbatch(dout, 'zz_tune_$0', 'false', 'nameList', trans11_names,'fresh',true));
end

if nargout > 1
    varargout{1} = islog;
end
