%**************************************************************************
%% DRIVER_SETTINGS
%**************************************************************************
close all
clear all
disp([sprintf('\n'), 'DRIVER_SETTINGS']);

%% ACTUAL FORECAST label
% prefix 
settings.report_prefix	= '2025sz01_fbs_new_fixed';
% optional prefix extension(s)
settings.prefix_ext = { ...	
%	'NAME'				'PREFIX EXTENSION'
					};
                
%% OLD FORECAST label (optional)
% prefix 
settings.oldfrc_prefix	= '2025sz01_fbs_standa';%2022sz05

%% Databases 
% Baseline data
settings.histcore_name	=  'histcore-2025-02-18.csv';%'histcore-2024-12-11_test.csv'; %'histcore-2025-02-10.csv';%
% Adhoc data
settings.adhoc_data_fb	= '';

%% Dates
settings.shist			= qq(1996,1);		% start of history in the data set
settings.ehist			= qq(2024,4);		% end of history in the histcore database for filtering
settings.end_pred		= qq(2026,4);		% end of prediction range 
settings.end_pred_long  = qq(2031,4);		% end of prediction range 
settings.end_comp		= qq(2050,4);		% end of computations of prediction
settings.start_pred		= settings.ehist+1;

%% Model Toggles
settings.pouzij_odhad	= 0;				% on/off - estimate parameters
%settings.RERlevel		= 1;				% RER version: 1 - ER level, 0 - change in ER
settings.FB_gdp_decomp  = 0;                % 1 - foreign GDP decomposed endogenously, 0 - foreign GDP decomposition as observations

%% Forecast expectation scheme
settings.expectations_scheme = 'expscheme_fb';

%% Pre-computed ranges
settings.hrng			= settings.shist:settings.ehist;                % filtering
settings.fcastrng		= settings.start_pred:settings.end_pred;		% forecast
settings.comprng		= settings.start_pred:settings.end_comp;		% computation of prediction

settings.nstarrng     = settings.start_pred:settings.end_pred_long;     % foreign demand
settings.istarrng     = settings.start_pred:settings.end_pred_long;     % foreign interest rate
settings.pstarrng	  = settings.start_pred:settings.end_pred_long;     % foreign inflation rate
settings.potherrng	  = settings.start_pred:settings.end_pred_long;

settings.usdeurrng    = settings.start_pred:settings.end_pred_long;     % USDEUR 
settings.brentrng     = settings.start_pred:settings.end_pred_long;     % Brent USD price

%% Optional prefix extensions
if isfield(settings,'prefix_ext')
    for ix = 1: size(settings.prefix_ext,1);
        settings.extend.(settings.prefix_ext{ix,1}) = [settings.report_prefix settings.prefix_ext{ix,2}];
        disp(['Prefix ' settings.prefix_ext{ix,1} ' for ' [settings.report_prefix settings.prefix_ext{ix,2}] ' was created.']);
    end
    settings = rmfield(settings,'prefix_ext');
end

%% Path
settings.path = '..\database';				% location of folder database for input and output

%% Save Settings
FB_ID = settings;
save([settings.path '\Output-data\' settings.report_prefix '-settings.mat'], 'FB_ID');
FB_ID = settings.report_prefix;
disp(['FB_ID: ', settings.report_prefix]);
save('GLOBALSETTINGS.mat', 'FB_ID');