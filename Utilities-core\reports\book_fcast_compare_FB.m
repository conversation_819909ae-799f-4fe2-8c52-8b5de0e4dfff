function dbout = book_fcast_compare_FB(d, m, SS, d0, fcast_plan, settings, settings_old, heading, basename)

% d            actual forecast 
% m            model object
% SS           steady state
% d0           previous forecast database
% fcast_plan   plan object
% sdettings    setup from driver settings
% heading     the running heading
% basename    basename of the output file 
% @ last revision: T. Pokorny, CNB, 2023/3

table_rng = settings.ehist-3:settings.end_pred;
plot_rng = settings.shist+72:settings.end_pred;
measure_rng =  settings.shist:settings.ehist-10;

param = get(m,'parameters');
eList = dbnames(d,'nameFilter','^eps_\w*'); 
omegaList = dbnames(d,'nameFilter','^omega_\w*'); 
eList_d0 = dbnames(d0,'nameFilter','^eps_\w*'); 
omegaList_d0 = dbnames(d0,'nameFilter','^omega_\w*'); 

x = report.new(heading);

disp('Creating Analytical Forecast Report');
% TODO (graphs + data):  pGDP, pGDP4, nom GDP, nom GDP4
%% 
sdate = settings.ehist+1;
erng_g = plot_rng;
ewhole_rng = plot_rng;

list = {'ne_zz_dot_pstar_tilde4', 'ne_zz_dot_pstar_tilde', 'ne_zz_dot_pstar_other_tilde4', 'ne_zz_dot_pstar_other_tilde',...
         'zz_dot_cpi_star_tilde4', 'zz_dot_cpi_star_tilde', 'dot_pstar_RP_tilde',...
         'ne_zz_dot_usdeur4', 'ne_zz_dot_usdeur', 'zz_z_gap',...
         'zz_dot_y_star_trend4', 'zz_dot_y_star_trend', 'zz_dot_pstar_tilde4', 'zz_dot_pstar_tilde', 'zz_dot_pstar_other_tilde4', ...
         'zz_dot_pstar_other_tilde', 'ne_zz_dot_cpi_star_tilde4', 'zz_y_star_gap', 'ne_zz_y_star', 'zz_y_star_trend_total', ...
         'zz_dot_y_star_trend_total', 'zz_dot_y_star_shift', 'ne_zz_dot_y_star', ...
         'zz_dot_y_star', 'ne_zz_dot_y_star4', 'ne_zz_i_star', 'ne_zz_i_star_shadow', 'zz_r_star_gap', 'ne_zz_usdeur',...
         'eps_y_star_gap'};

dbout = dbbatch(d, '$0', 'resize(d.$0, erng_g);', 'namelist', list);
dbout = dbout*list;

dbout_ = dbbatch(d0, '$0', 'resize(d0.$0, erng_g);', 'namelist', list);
dbout_ = dbout_*list;

dbout  = dbmerge(dbout, dbout_); 



%% Forecast Summary (YoY) - page 1

x.table('Forecast Summary (YoY)','range',table_rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);

x.subheading('');
x.subheading(char(d.ne_zz_dot_pstar_tilde4.comment));
x.series(settings.report_prefix, d.ne_zz_dot_pstar_tilde4,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_pstar_tilde4,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_pstar_tilde4,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_pstar_tilde4,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.ne_zz_dot_pstar_other_tilde4.comment));
x.series(settings.report_prefix, d.ne_zz_dot_pstar_other_tilde4,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_pstar_other_tilde4,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_pstar_other_tilde4,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_pstar_other_tilde4,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_dot_cpi_star_tilde4.comment));
x.series(settings.report_prefix, d.ne_zz_dot_cpi_star_tilde4,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_cpi_star_tilde4,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_cpi_star_tilde4,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_cpi_star_tilde4,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_dot_pstar_RP_tilde4.comment));
x.series(settings.report_prefix, d.zz_dot_pstar_RP_tilde4,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.zz_dot_pstar_RP_tilde4,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_energy_share_ppi_star_gap.comment));
x.series(settings.report_prefix, d.zz_energy_share_ppi_star_gap,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.zz_energy_share_ppi_star_gap,'format','%.1f','units','%pa');

x.subheading('');
x.subheading('F. Weight of Energy PPI');
x.series(settings.report_prefix, d.weight_dot_pstar_energy_tilde,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.weight_dot_pstar_energy_tilde,'format','%.1f','units','%pa');

%% Forecast Summary (YoY) - page 2
x.pagebreak();
x.table('Forecast Summary (YoY)','range',table_rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);

x.subheading('');
x.subheading(char(d.zz_dot_y_star_trend4.comment));
% x.series(settings.report_prefix, d.ne_zz_dot_y_star_trend4,'format','%.1f','units','%pa');
% x.series(settings.oldfrc_prefix, d0.ne_zz_dot_y_star_trend4,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' Total g3+'], d.zz_dot_y_star_trend4,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' Total g3+'], d0.zz_dot_y_star_trend4,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' Fundamental g3+'], d.zz_dot_y_star_trend_fund4,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' Fundamental g3+'], d0.zz_dot_y_star_trend_fund4,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' One-off Shift g3+'], d.zz_dot_y_star_trend_shift4,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' One-off Shift g3+'], d0.zz_dot_y_star_trend_shift4,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.ne_zz_dot_y_star4.comment));
x.series(settings.report_prefix, d.ne_zz_dot_y_star4,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_y_star4,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_y_star4,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_y_star4,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.ne_zz_dot_usdeur4.comment));
x.series(settings.report_prefix, d.ne_zz_dot_usdeur4,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_usdeur4,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.ne_zz_i_star.comment));
x.series(settings.report_prefix, d.ne_zz_i_star,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_i_star,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char('Foreign Shadow Policy Rate'));
x.series(settings.report_prefix, d.ne_zz_i_star_shadow,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_i_star_shadow,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_r_star_gap.comment));
x.series(settings.report_prefix, d.zz_r_star_gap,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.zz_r_star_gap,'format','%.1f','units','%pa');


%% Forecast Summary (QoQ) - page 3
x.pagebreak();
x.table('Forecast Summary (QoQ)','range',table_rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);

x.subheading('');
x.subheading(char(d.ne_zz_dot_pstar_tilde.comment));
x.series(settings.report_prefix, d.ne_zz_dot_pstar_tilde,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_pstar_tilde,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_pstar_tilde,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_pstar_tilde,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.ne_zz_dot_pstar_other_tilde.comment));
x.series(settings.report_prefix, d.ne_zz_dot_pstar_other_tilde,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_pstar_other_tilde,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_pstar_other_tilde,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_pstar_other_tilde,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_dot_cpi_star_tilde.comment));
x.series(settings.report_prefix, d.ne_zz_dot_cpi_star_tilde,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_cpi_star_tilde,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_cpi_star_tilde,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_cpi_star_tilde,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_dot_pstar_RP_tilde.comment));
x.series(settings.report_prefix, d.zz_dot_pstar_RP_tilde,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.zz_dot_pstar_RP_tilde,'format','%.1f','units','%pa');

%% Forecast Summary (QoQ) - page 4
x.pagebreak();
x.table('Forecast Summary (QoQ)','range',table_rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);

x.subheading('');
x.subheading(char(d.ne_zz_dot_y_star.comment));
x.series(settings.report_prefix, d.ne_zz_dot_y_star,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_y_star,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_dot_y_star,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_y_star,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_dot_y_star_trend.comment));
% x.series(settings.report_prefix, d.ne_zz_dot_y_star_trend,'format','%.1f','units','%pa');
% x.series(settings.oldfrc_prefix, d0.ne_zz_dot_y_star_trend,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' Total g3+'], d.zz_dot_y_star_trend,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' Total g3+'], d0.zz_dot_y_star_trend,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' Fundamental g3+'], d.zz_dot_y_star_trend_fund,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' Fundamental g3+'], d0.zz_dot_y_star_trend_fund,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' One-off Shift g3+'], d.zz_dot_y_star_trend_shift,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' One-off Shift g3+'], d0.zz_dot_y_star_trend_shift,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_y_star_gap.comment));
% x.series(settings.report_prefix, d.ne_zz_y_star_gap,'format','%.1f','units','%pa');
% x.series(settings.oldfrc_prefix, d0.ne_zz_y_star_gap,'format','%.1f','units','%pa');
x.series([settings.report_prefix ' g3+'], d.zz_y_star_gap,'format','%.1f','units','%pa');
x.series([settings.oldfrc_prefix ' g3+'], d0.zz_y_star_gap,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.ne_zz_usdeur.comment));
x.series(settings.report_prefix, d.ne_zz_usdeur,'format','%.2f');
x.series(settings.oldfrc_prefix, d0.ne_zz_usdeur,'format','%.2f');

x.subheading('');
x.subheading(char(d.ne_zz_dot_usdeur.comment));
x.series(settings.report_prefix, d.ne_zz_dot_usdeur,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.ne_zz_dot_usdeur,'format','%.1f','units','%pa');

x.subheading('');
x.subheading(char(d.zz_z_gap.comment));
x.series(settings.report_prefix, d.zz_z_gap,'format','%.1f','units','%pa');
x.series(settings.oldfrc_prefix, d0.zz_z_gap,'format','%.1f','units','%pa');


%% Figures setting
%%%%%%%%%%%%%%%%%%%%%%%
%%%    Figures      %%%
%%%%%%%%%%%%%%%%%%%%%%%

sty = struct(); 
sty.line.lineWidth = 2;
sty.line.lineStyle = '-';
sty.line.Color = {[0 0 1],[0 0.5 0],[1 0 0],[0.5273    0.8047    0.9792]};
% sty.line.Color = {[0 0 1],[0 0.5 0],[1 0 0],[1.0000    0.8398         0]};
sty.highlight.faceColor = {[0.92,0.92,0.92],[0.85,0.85,0.85]};
sty.legend.location = 'SouthOutside';
sty.legend.Orientation = 'Horizontal';
sty.axes.ylim=[ ...
                    '!! ylim = get(H,''ylim'');', ...
                    'k = 0.05*(ylim(2)-ylim(1));', ...
                    'SET = [ylim(1)-k,ylim(2)+k];'];
post = [ ...
    'leg = findobj(gcf,''Location'',''SouthOutside'');'...
    'set(leg,''location'',''none'');',...
    'posleg = get(leg,''position'');', ...
    'pos = get(H,''position'');', ...
    'k = 0.0073;', ...
    'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
    'k = 0.062;', ...
    'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];


%% Summary QoQ - page 5
x.pagebreak();

x.figure('Forecast Summary','range', erng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',erng_g(1):4:erng_g(end));
 x.graph(get(d.ne_zz_dot_pstar_tilde, 'comment'),'legend',true,'tight',true,'postprocess',post);
   x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_pstar_tilde);
   x.series(settings.oldfrc_prefix, d0.ne_zz_dot_pstar_tilde);
   x.series([settings.report_prefix ' g3+'], d.zz_dot_pstar_tilde);
   x.series(settings.report_prefix, d.ne_zz_dot_pstar_tilde);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_dot_pstar_other_tilde, 'comment'),'tight',true);
   x.series('', d0.zz_dot_pstar_other_tilde);
   x.series('', d0.ne_zz_dot_pstar_other_tilde);
   x.series('', d.zz_dot_pstar_other_tilde);
   x.series('', d.ne_zz_dot_pstar_other_tilde);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_dot_cpi_star_tilde, 'comment'),'tight',true);
   x.series('', d0.zz_dot_cpi_star_tilde);
   x.series('', d0.ne_zz_dot_cpi_star_tilde);
   x.series('', d.zz_dot_cpi_star_tilde);
   x.series('', d.ne_zz_dot_cpi_star_tilde);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.zz_dot_pstar_RP_tilde, 'comment'),'tight',true,'legend',true, 'postprocess',post);
   x.series(settings.oldfrc_prefix, d0.zz_dot_pstar_RP_tilde);
   x.series(settings.report_prefix, d.zz_dot_pstar_RP_tilde);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp); 
  
%% Summary QoQ - page 6
x.pagebreak();     

x.figure('Forecast Summary','range', erng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',erng_g(1):4:erng_g(end));
 x.graph(get(d.ne_zz_dot_y_star, 'comment'),'legend',true,'tight',true,'postprocess',post);
   x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_y_star);
   x.series(settings.oldfrc_prefix, d0.ne_zz_dot_y_star);
   x.series([settings.report_prefix ' g3+'], d.zz_dot_y_star);
   x.series(settings.report_prefix, d.ne_zz_dot_y_star);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_dot_y_star4, 'comment'),'tight',true);
   x.series('', d0.zz_dot_y_star4);
   x.series('', d0.ne_zz_dot_y_star4);
   x.series('', d.zz_dot_y_star4);
   x.series('', d.ne_zz_dot_y_star4);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.zz_dot_y_star_trend, 'comment'),'tight',true); 
   x.series('', d0.zz_dot_y_star_trend);
%    x.series('', d0.ne_zz_dot_y_star_trend);
   x.series('', d.zz_dot_y_star_trend);
%    x.series('', d.ne_zz_dot_y_star_trend);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.zz_dot_y_star_trend4, 'comment'),'tight',true);
   x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_y_star_trend4);
%    x.series(settings.oldfrc_prefix, d0.ne_zz_dot_y_star_trend4);
   x.series([settings.report_prefix ' g3+'], d.zz_dot_y_star_trend4);
%    x.series(settings.report_prefix, d.ne_zz_dot_y_star_trend4);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.zz_y_star_gap, 'comment'),'tight',true);
   x.series([settings.oldfrc_prefix ' g3+'], d0.zz_y_star_gap);
%    x.series(settings.oldfrc_prefix, d0.);
   x.series([settings.report_prefix ' g3+'], d.zz_y_star_gap);
%    x.series(settings.report_prefix, d.ne_zz_y_star_gap);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_y_star, 'comment'),'legend',true,'tight',true,'postprocess',post);
   x.series([settings.oldfrc_prefix ' Data'], d0.ne_zz_y_star);
   x.series([settings.oldfrc_prefix ' Trend g3+'], d0.zz_y_star_trend);
   x.series([settings.report_prefix ' Data'], d.ne_zz_y_star);
   x.series([settings.report_prefix ' Trend g3+'], d.zz_y_star_trend);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
%% Summary Levels - page 7
x.pagebreak();   
 
x.figure('Forecast Summary','range', erng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',erng_g(1):4:erng_g(end));
 x.graph(get(d.ne_zz_usdeur, 'comment'),'legend',true,'tight',true,'postprocess',post);
   x.series(settings.oldfrc_prefix, d0.ne_zz_usdeur);
   x.series(settings.report_prefix, d.ne_zz_usdeur);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_i_star, 'comment'),'tight',true);
   x.series(settings.oldfrc_prefix, d0.ne_zz_i_star);
   x.series(settings.report_prefix, d.ne_zz_i_star);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.zz_z_gap, 'comment'),'tight',true);
   x.series(settings.oldfrc_prefix, d0.zz_z_gap);
   x.series(settings.report_prefix, d.zz_z_gap);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph('Foreign Shadow Policy Rate','tight',true);
   x.series(settings.oldfrc_prefix, d0.ne_zz_i_star_shadow);
   x.series(settings.report_prefix, d.ne_zz_i_star_shadow);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_dot_usdeur4, 'comment'),'tight',true);
   x.series(settings.oldfrc_prefix, d0.ne_zz_dot_usdeur4);
   x.series(settings.report_prefix, d.ne_zz_dot_usdeur4);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);   
 x.graph(get(d.zz_r_star_gap, 'comment'),'tight',true);
   x.series('', d0.zz_r_star_gap);
   x.series('', d.zz_r_star_gap);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);

 %% Summary QoQ - page 8
x.pagebreak();

x.figure('Forecast Summary','range', erng_g, 'subplot',[3 2],'dateformat','YY:P','style',sty,'datetick',erng_g(1):4:erng_g(end));
 x.graph(get(d.ne_zz_dot_pstar_tilde4, 'comment'),'legend',true,'tight',true,'postprocess',post);
   x.series([settings.oldfrc_prefix ' g3+'], d0.zz_dot_pstar_tilde4);
   x.series(settings.oldfrc_prefix, d0.ne_zz_dot_pstar_tilde4);
   x.series([settings.report_prefix ' g3+'], d.zz_dot_pstar_tilde4);
   x.series(settings.report_prefix, d.ne_zz_dot_pstar_tilde4);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_dot_pstar_other_tilde4, 'comment'),'tight',true);
   x.series('', d0.zz_dot_pstar_other_tilde4);
   x.series('', d0.ne_zz_dot_pstar_other_tilde4);
   x.series('', d.zz_dot_pstar_other_tilde4);
   x.series('', d.ne_zz_dot_pstar_other_tilde4);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.ne_zz_dot_cpi_star_tilde4, 'comment'),'tight',true);
   x.series('', d0.zz_dot_cpi_star_tilde4);
   x.series('', d0.ne_zz_dot_cpi_star_tilde4);
   x.series('', d.zz_dot_cpi_star_tilde4);
   x.series('', d.ne_zz_dot_cpi_star_tilde);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
 x.graph(get(d.zz_dot_pstar_RP_tilde4, 'comment'),'tight',true,'legend',true, 'postprocess',post);
   x.series(settings.oldfrc_prefix, d0.zz_dot_pstar_RP_tilde4);
   x.series(settings.report_prefix, d.zz_dot_pstar_RP_tilde4);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);        
 x.graph(get(d.zz_energy_share_ppi_star_gap, 'comment'),'legend',true,'postprocess',post,'style',sty,'zeroLine=',true);
    x.series([settings.oldfrc_prefix],d0.zz_energy_share_ppi_star_gap);   
    x.series([settings.report_prefix],d.zz_energy_share_ppi_star_gap);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);    
 x.graph('F. Weight of Energy PPI','legend',true,'postprocess',post,'style',sty,'zeroLine=',true); 
    x.series([settings.oldfrc_prefix],d0.weight_dot_pstar_energy_tilde);
    x.series([settings.report_prefix],d.weight_dot_pstar_energy_tilde);
 x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);    ;
    
%% RMC Decompositions

stybar=struct();
stybar.figure.colormap=[0 0 0.95;1 0 0; 1 1 0];
stybar.line.lineWidth = {3,2};
stybar.line.Color = {[1 1 1],[0 0 0]};
stybar.legend.location = 'SouthOutside';
stybar.legend.Orientation = 'Horizontal';
stybar.highlight.faceColor = {[0.92,0.92,0.92],[0.85,0.85,0.85]};
stybar.axes.xgrid = 'off';

post = [ ...
    'leg = findobj(gcf,''Location'',''SouthOutside'');'...
    'set(leg,''location'',''none'');',...
    'posleg = get(leg,''position'');', ...
    'pos = get(H,''position'');', ...
    'k = 0.014;', ...
    'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
    'k = 0.061;', ...
    'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];


%% Residuals and Tunes - page 10 and following

% Page 9
x.pagebreak();

x.figure('Residuals','range', erng_g, 'subplot',[4 3],'dateformat','YY:P','style',sty,'datetick',erng_g(1):12:erng_g(end));
graph_list={'eps_y_star_gap', 'eps_dot_y_star_trend_fund','eps_pstar_tilde', 'eps_pstar_other_tilde','eps_dot_cpi_star_tilde',...
            'eps_Istar', 'eps_i_star_eq', 'eps_shadow_rate', 'eps_USDEUR', 'eps_prem_usdeur', 'eps_pstar_RP_tilde'};
for ii=1:length(graph_list),
    if ii==1,
        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);
    else
        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);
    end;
        x.series(settings.oldfrc_prefix,d0.(['zz_' graph_list{ii}]));
        x.series(settings.report_prefix,d.(['zz_' graph_list{ii}]));       
        x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
end;

% Page 10
x.pagebreak();

x.figure('Residuals - Expected Shocks','range', erng_g, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',erng_g(1):12:erng_g(end));
graph_list={'eps_exp_y_star_gap', 'eps_exp_dot_y_star_trend_fund','eps_exp_pstar_tilde',...
            'eps_exp_pstar_other_tilde','eps_exp_dot_cpi_star_tilde','eps_exp_Istar','eps_exp_i_star_eq', 'eps_exp_USDEUR','eps_exp_prem_usdeur'};
for ii=1:length(graph_list),
    if ii==1,
        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);
    else
        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);
    end;
        x.series(settings.oldfrc_prefix,d0.(['zz_' graph_list{ii}]));
        x.series(settings.report_prefix,d.(['zz_' graph_list{ii}]));       
        x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
end;

% Page 11

x.figure('Measurement errors','range', measure_rng, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',measure_rng(1):12:measure_rng(end));
graph_list={'omega_Y_STAR','omega_PSTAR_TILDE','omega_PSTAR_OTHER_TILDE','omega_CPI_STAR_TILDE','omega_I_STAR','omega_I_STAR_EU',...
            'omega_I_STAR_EQ','omega_USDEUR'};
for ii=1:length(graph_list),
    if ii==1,
        x.graph(graph_list{ii},'legend',true,'tight',true,'postprocess',post,'zeroline',true);
    else
        x.graph(graph_list{ii},'tight',true,'zeroline',true);
    end;
        x.series(settings.oldfrc_prefix,d0.(['zz_' graph_list{ii}]));
        x.series(settings.report_prefix,d.(['zz_' graph_list{ii}]));                  
        x.highlight('',settings_old.FB_ID.start_pred:settings.start_pred); x.highlight('',settings.start_pred:settings.end_comp);
end;

%d.(graph_list{ii})

% for ix = 1 : length(eList)
% 	if mod(ix,9)==1
% 		x.figure('Residuals','range', erng_g, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',erng_g(1):12:erng_g(end));
% %     else
% %         x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);   
%     end
%     if mod(ix,9)==1
%     x.graph(eList{ix},'style',sty,'zeroLine=',true);
%     end
% 	x.series(get(d.(['zz_' eList{ix}]), 'comment'),d.(['zz_' eList{ix}]));
%     x.series(get(d0.(['zz_' eList_d0{ix}]), 'comment'),d0.(['zz_' eList_d0{ix}]));
%     x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end)); 
% end
% 
% for ix = 1 : length(omegaList)
% 	if mod(ix,9)==1
% 		x.figure('Measurement errors','range', erng_g, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',erng_g(1):12:erng_g(end));
% 	end
% 	x.graph(omegaList{ix},'style',sty,'zeroLine=',true);
% 	x.series(get(d.(['zz_' omegaList{ix}]), 'comment'),d.(omegaList{ix}));
% end






% x.table('Residuals and Tunes','range',table_rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);
% 
% x.subheading('');
% x.subheading('Tunes - Expected');
% tunes = get_exogenized(fcast_plan);
% list_ = dbobjects(tunes);

% for i = 1:length(list_)
%     series_text=get(d.(list_{i}), 'comment');
%     x.series(series_text{1}, tunes.(list_{i}),'format','%.1f','units','Tune');
% end
% 
% x.subheading('');
% x.subheading('Non Zero Residuals (not annualized) - Expected');   
% enames = dbnames(d,'nameFilter','^eps_\w*');
% for i = 1:length(enames);
%     if sum(abs(real(d_pure.(enames{i})(sdate:table_rng(end)))))~=0
%         series_text=get(d.(enames{i}), 'comment');
%         x.series(series_text{1}, 100*real(d_pure.(enames{i})),'format','%.1f','units','%'); % {1}
%     end
% end
% x.subheading('');
% 
% x.pagebreak();
% 
% x.table('Residuals and Tunes','range',table_rng,'vline',sdate-1,'dateformat','YY:R','colWidth',3.15);
% 
% x.subheading('');
% x.subheading('Tunes - Unexpected');
% list_ = dbobjects(tunes);
% for i = 1:length(list_)
%     series_text=get(d.(list_{i}), 'comment');
%     x.series(series_text{1}, tunes.(list_{i}),'format','%.1f','units','Tune');
% end
% 
% x.subheading('');
% x.subheading('Non Zero Residuals (not annualized) - Unexpected');   
% enames = dbnames(d,'nameFilter','^eps_\w*');
% for i = 1:length(enames);
%     if sum(abs(imag(d_pure.(enames{i})(sdate:table_rng(end)))))~=0
%         series_text=get(d.(enames{i}), 'comment');
%         x.series(series_text{1}, 100*imag(d_pure.(enames{i})),'format','%.1f','units','%'); % {1}
%     end
% end
% x.subheading('');




% %% Shocks
% %--Graph--%
% x.pagebreak();
% 
% post = [ ...
%     'leg = findobj(gcf,''Location'',''SouthOutside'');'...
%     'set(leg,''location'',''none'');',...
%     'posleg = get(leg,''position'');', ...
%     'pos = get(H,''position'');', ...
%     'k = 0.0073;', ...
%     'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
%     'k = 0.062;', ...
%     'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];
% 
% x.figure('Measurement Errors in Level','range',  settings.hrng, 'subplot',[3 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:settings.hrng(end));
% graph_list={'omega_P_OIL','omega_CPI_EU','omega_PPI_EU','omega_I_EU','omega_GDP_EU',...
%     'omega_CPI_EF','omega_PPI_EF','omega_GDP_EF','omega_USDEUR'};
% for ii=1:9
%    if ii==9,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,d0.(graph_list{ii}));
%    x.series(settings.report_prefix,d.(graph_list{ii}));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end
% 
% x.figure('Measurement Errors in Level','range',  settings.hrng, 'subplot',[3 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:settings.hrng(end));
% graph_list={'omega_CPI_US','omega_PPI_US','omega_I_US','omega_GDP_US'};
% for ii=1:4
%    if ii==4,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,d0.(graph_list{ii}));
%    x.series(settings.report_prefix,d.(graph_list{ii}));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end
% 
% enames = get(m,'enames');
% %--Graph--%
% x.pagebreak();
% 
% x.figure('Structural Shocks - Expected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(1:12);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,real(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,real(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% x.figure('Structural Shocks - Expected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(13:24);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,real(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,real(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% x.figure('Structural Shocks - Expected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(25:36);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,real(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,real(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% x.figure('Structural Shocks - Expected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(37:44);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,real(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,real(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% x.figure('Structural Shocks - Unexpected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(1:12);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,imag(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,imag(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% x.figure('Structural Shocks - Unexpected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(13:24);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,imag(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,imag(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% x.figure('Structural Shocks - Unexpected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(25:36);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,imag(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,imag(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% x.figure('Structural Shocks - Unexpected','range',  ewhole_rng, 'subplot',[4 3],'dateformat','YY:P', 'style', sty,'datetick',settings.hrng(1):16:erng_g(end));
% graph_list=enames(37:44);
% for ii=1:length(graph_list),
%    if ii==12,
%        x.graph(get(d.(graph_list{ii}), 'comment'),'legend',true,'tight',true,'postprocess',post,'zeroline',true);    
%    else
%        x.graph(get(d.(graph_list{ii}), 'comment'),'tight',true,'zeroline',true);    
%    end;
%    x.series(settings.oldfrc_prefix,imag(d0_pure.(graph_list{ii})));
%    x.series(settings.report_prefix,imag(d_pure.(graph_list{ii})));
% 	x.highlight('',sdate:sdate);x.highlight('',sdate:erng_g(end));
% end;
% % %% Nominal Shares
% % x.pagebreak();
% % 
% % x.figure('Nominal Shares on GDP (this IS a problem due to OPENNES trend)','range', erng_g, 'subplot',[3 2],'dateformat','YY:P', 'style', sty,'datetick',erng_g(1):4:erng_g(end));
% %  x.graph('Consumption-to-GDP');
% %    x.series(settings.cmp.lgnd{2},d0.pC_c__gdpn);
% %    x.series(settings.cmp.lgnd{1},d.pC_c__gdpn);
% % x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);   
% %  x.graph('Investment-to-GDP');
% %    x.series('',d0.pJ_j__gdpn);
% %    x.series('',d.pJ_j__gdpn);
% % x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);   
% %  x.graph('GovtCons-to-GDP');
% %    x.series('',d0.pG_g__gdpn);
% %    x.series('',d.pG_g__gdpn); 
% % x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);   
% %  x.graph('Exports-to-GDP','legend',true,'tight',true,'postprocess',post);
% %    x.series(settings.cmp.lgnd{2},d0.pX_x__gdpn);
% %    x.series(settings.cmp.lgnd{1},d.pX_x__gdpn);   
% % x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);   
% %  x.graph('Imports-to-GDP');
% %     x.series('',d0.pN_n__gdpn);
% %     x.series('',d.pN_n__gdpn);
% %  x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred); 
% % 
% % %% Relative prices of components
% % x.pagebreak();
% % 
% % x.figure('Relative Prices (normed)','range', erng_g, 'subplot',[2 2],'dateformat','YY:P', 'style', sty,'datetick',erng_g(1):4:erng_g(end));
% %  x.graph('Rel. price of Investment to Consumption');
% %     x.series('',d0.pJ__pC);
% %     x.series('',d.pJ__pC);
% %  x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);    
% %  x.graph('Rel.  price of Govt Cons to Consumption');
% %     x.series('',d0.pG__pC);
% %     x.series('',d.pG__pC);
% %  x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);
% %  x.graph('Rel. price of Exports to Consumption');
% %     x.series('',d0.pX__pC);
% %     x.series('',d.pX__pC);
% %  x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);    
% %  x.graph('Rel. price of Imports to Consumption','legend',true,'tight',true,'postprocess',post);
% %     x.series(settings.cmp.lgnd{2},d0.pN__pC);
% %     x.series(settings.cmp.lgnd{1},d.pN__pC);    
% %  x.highlight('',settings.cmp.trans_rng);x.highlight('',settings.cmp.fut_rng(1):settings.new.end_pred);   



x.pagebreak();

x.publish([basename '.pdf'],'paperSize','a4paper','display',false); 


