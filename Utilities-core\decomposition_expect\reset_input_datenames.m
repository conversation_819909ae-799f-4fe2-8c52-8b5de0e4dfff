function [decomp] = reset_input_datenames(decomp, type, varargin)

default = {...
    'sr_opt',                 [], ...			%- sr options                         
    };
%--parse options using IRIS --%
options = passopt(default, varargin{1:end});

switch type
	case 'fcastold'
        decomp.input_vect(decomp.input_vect==2) = 15;
        decomp.input_vect(decomp.input_vect==3) = 17;
        decomp.input_vect(decomp.input_vect==5) = 16;
        decomp.input_vect(decomp.input_vect==6) = 18;
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixold__'); 
        decomp.input_datenames = strrep(decomp.input_datenames,'__res__','__resold__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurold__');
%         decomp.input_datenames =strrep(decomp.input_datenames,'__resunexp__','__ressurold__');	
	case 'fcastnew'
        decomp.input_vect(decomp.input_vect==2) = 9;
        decomp.input_vect(decomp.input_vect==3) = 11;
        decomp.input_vect(decomp.input_vect==5) = 10;
        decomp.input_vect(decomp.input_vect==6) = 12;      
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixnew__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__res__','__resnew__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurnew__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__resunexp__','__ressurnew__');
	case 'rev'
        decomp.input_vect(decomp.input_vect==2) = 23;
        decomp.input_vect(decomp.input_vect==5) = 24;
        decomp.input_vect(decomp.input_vect==6) = 1;
        decomp.input_vect(decomp.input_vect==4) = 35;
        decomp.input_vect(decomp.input_vect==7) = 36;
        decomp.input_datenames = strrep(decomp.input_datenames,'__obs__','__obsrev__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixrev__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurrev__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__res__','__obsrev__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__ini__','__inirev__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__iniunexp__','__inisurrev__'); 
	case 'rls'
        decomp.input_vect(decomp.input_vect==2) = 25;
        decomp.input_vect(decomp.input_vect==5) = 26;
        decomp.input_vect(decomp.input_vect==4) = 33; 
        decomp.input_vect(decomp.input_vect==6) = 4;
        decomp.input_vect(decomp.input_vect==7) = 34;
        decomp.input_vect(decomp.input_vect==1) = 4;
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixrls__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurrls__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__res__','__obsrls__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__ini__','__inirls__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__iniunexp__','__inisurrls__');        
        decomp.input_datenames = strrep(decomp.input_datenames,'__obs__','__obsrls__');
   	case 'tunerls'
        decomp.input_vect(decomp.input_vect==2) = 27;
        decomp.input_vect(decomp.input_vect==5) = 28;       
        decomp.input_vect(decomp.input_vect==1) = 32;
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixtnr__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurtnr__'); 
        decomp.input_datenames = strrep(decomp.input_datenames,'__obs__','__tunesrls__');        
	case 'tunenew'
        decomp.input_vect(decomp.input_vect==2) = 27;
        decomp.input_vect(decomp.input_vect==5) = 28;
        decomp.input_vect(decomp.input_vect==1) = 2;
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixtnn__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurtnn__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__obs__','__tunesnew__');
	case 'tuneold'  
        decomp.input_vect(decomp.input_vect==1) = 3;
        decomp.input_vect(decomp.input_vect==2) = 29;
        decomp.input_vect(decomp.input_vect==5) = 30;
        decomp.input_datenames = strrep(decomp.input_datenames,'__obs__','__tunesold__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixtno__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurtno__');
	case 'trans'
        decomp.input_vect(decomp.input_vect==2) = 19;
        decomp.input_vect(decomp.input_vect==3) = 21;
        decomp.input_vect(decomp.input_vect==5) = 20;
        decomp.input_vect(decomp.input_vect==6) = 22;      
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixtra__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__res__','__restra__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurtra__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__resunexp__','__ressurtra__'); 
    case 'decomp_ini'
        decomp.input_vect(decomp.input_vect==2) = 9;
        decomp.input_vect(decomp.input_vect==3) = 11;
        decomp.input_vect(decomp.input_vect==5) = 10;
        decomp.input_vect(decomp.input_vect==6) = 12;
        decomp.input_vect(decomp.input_vect==7) = 8;
        decomp.input_vect(decomp.input_vect==4) = 7; 
        decomp.input_datenames = strrep(decomp.input_datenames,'__fix__','__fixnew__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__res__','__resnew__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__fixunexp__','__fixsurnew__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__resunexp__','__ressurnew__');
        decomp.input_datenames = strrep(decomp.input_datenames,'__ini__','__ininew__');
%         decomp.input_datenames = strrep(decomp.input_datenames,'__iniunexp__','__inisurnew__');        
	otherwise
		error('Wrong type of decomposition!');
end

end % of MAIN function

function [input_datenames] = make_input_datenames_(input_names, input_vect, simrng)
%- create the vector of input_datenames, i.e. 'period__type__name'
input_datenames = cell(length(input_names),1);
for i = 1:length(input_names)
    date_ = simrng(1) - 1 + input_vect(i,1); %TODO: calculating NaNs :) not a perfect coding style :(
    variable_date = char(dat2str(date_));
    switch input_vect(i,2)
        case 1
            vartype = 'obsrev'; 
        case 2
            vartype = 'tunesnew'; 
        case 3
            vartype = 'tunesold'; 
        case 4
            vartype = 'obsrls'; 
        case 7  
            vartype = 'iniexpnew'; 
        case 8
%             vartype = 'inisurnew'; 
        case 9
            vartype = 'fixexpnew';
        case 10
%             vartype = 'fixsurnew';
        case 11
            vartype = 'resexpnew';
        case 12
%             vartype = 'ressurnew';
        case 13  
            vartype = 'iniexpold'; 
        case 14
%             vartype = 'inisurold'; 
        case 15
            vartype = 'fixexpold';
        case 16
%             vartype = 'fixsurold';
        case 17
            vartype = 'resexpold';
        case 18
%             vartype = 'ressurold';
        case 19
            vartype = 'fixexptra';
        case 20
%             vartype = 'fixsurtra';
        case 21
            vartype = 'resexptra';
        case 22
%             vartype = 'ressurtra'; 
        case 23
            vartype = 'fixexprev';
        case 24
%             vartype = 'fixsurrev';
        case 25
            vartype = 'fixexprls';
        case 26
%             vartype = 'fixsurrls';
        case 27
            vartype = 'fixexptnn';
        case 28
%             vartype = 'fixsurtnn';
        case 29
            vartype = 'fixexptno';
        case 30
%             vartype = 'fixsurtno';           
        case 31
            vartype = 'model';
        case 32
            vartype = 'tunesrls';             
        case 33
%             vartype = 'iniexprls';             
        case 34
            vartype = 'inisurrls';
        case 35
%             vartype = 'iniexprev';             
        case 36
            vartype = 'inisurrev';             
         
    end
    input_datenames{i} = sprintf('%s__%s__%s', variable_date, vartype, char(input_names(i)));
end
end %-of SUB function make_input_datenames_  ---------------------------------------%