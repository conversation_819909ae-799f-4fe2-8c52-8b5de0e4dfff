!measurement_variables

% IS
'Observed Real Foreign Demand (100*log)'                obs_Y_STAR

!if ~FB_gdp_decomp
    'Observed Foreign Output Gap (100*log)'             obs_Y_STAR_GAP
    'Observed Real Foreign GDP Trend (100*log)'         obs_Y_STAR_TREND
    'Observed Real Foreign GDP Shift Trend (QoQ)'       obs_DOT_Y_STAR_TREND_SHIFT
!end

!if ~FB_prem_decomp
    'Observed USD/EUR Risk Premium (percent p.a.)'      obs_PREM_USDEUR
!end


% MP
'Observed 3-M Foreign Rates (percent p.a.)'             obs_I_STAR
'Observed 3-M Neutral Foreign Rates (percent p.a.)'     obs_I_STAR_EQ
'Observed Effective 3-M Foreign Rates (percent p.a.)'   obs_I_STAR_EU
'Observed 3-M Foreign (US) Rates (percent p.a.)'        obs_I_STAR_US

% UIP
'Observed Exchange Rate EUR/USD (100*log)'              obs_USDEUR

% PC
'Observed Energy Price Level (USD) (100*log)'           obs_PSTAR_ENERGY_TILDE
'Observed Non-Energy Price Level (EUR) (100*log)'       obs_PSTAR_OTHER_TILDE
'Observed Foreign Price Level (EUR) (100*log)'          obs_PSTAR_TILDE
'Observed Consurmer Price Index (100*log)'              obs_CPI_STAR_TILDE


!measurement_shocks

% IS
'Measurement Error for Real Foreign Demand (100*log)'               omega_Y_STAR

!if ~FB_gdp_decomp
    'Measurement Error for obs_Y_STAR_GAP'                          omega_Y_STAR_GAP
    'Measurement Error for obs_Y_STAR_TREND'                        omega_Y_STAR_TREND
    'Measurement Error for obs_DOT_Y_STAR_TREND_SHIFT'              omega_DOT_Y_STAR_TREND_SHIFT
!end

!if ~FB_prem_decomp
    'Measurement Error for USD/EUR Risk Premium'                    omega_PREM_USDEUR
!end

% MP
'Measurement Error for 3-M Foreign Rates (percent p.a.)'          	omega_I_STAR
'Measurement Error for 3-M Neutral Foreign Rates (percent p.a.)'	omega_I_STAR_EQ
'Measurement Error for obs_I_STAR_EU    '                           omega_I_STAR_EU
'Measurement Error for obs_I_STAR_US    '                           omega_I_STAR_US

% UIP
'Measurement Error for Exchange Rate EUR/USD (100*log)'             omega_USDEUR

% PC
'Measurement Error for Energy Price Level (USD) (100*log)'         	omega_PSTAR_ENERGY_TILDE
'Measurement Error for Core Price Level (USD) (100*log)'           	omega_PSTAR_OTHER_TILDE
'Measurement Error for Foreign PPI Level (EUR) (100*log)'          	omega_PSTAR_TILDE
'Measurement Error for Foreign CPI Level (EUR) (100*log)'          	omega_CPI_STAR_TILDE

!measurement_equations

% IS
obs_Y_STAR              = mes_Y_STAR                            + omega_Y_STAR;

!if ~FB_gdp_decomp
    obs_Y_STAR_GAP              = mes_Y_STAR_GAP                + omega_Y_STAR_GAP;
    obs_Y_STAR_TREND            = mes_Y_STAR_TREND              + omega_Y_STAR_TREND;
    obs_DOT_Y_STAR_TREND_SHIFT  = mes_DOT_Y_STAR_TREND_SHIFT	+ omega_DOT_Y_STAR_TREND_SHIFT;
!end

!if ~FB_prem_decomp
    obs_PREM_USDEUR             = mes_PREM_USDEUR               + omega_PREM_USDEUR;
!end


% MP
obs_I_STAR              = mes_I_STAR                            + omega_I_STAR;
obs_I_STAR_EQ           = mes_I_STAR_EQ                         + omega_I_STAR_EQ;
obs_I_STAR_EU           = mes_I_STAR_EU                         + omega_I_STAR_EU;
obs_I_STAR_US           = mes_I_STAR_US                         + omega_I_STAR_US;

% UIP
obs_USDEUR              = mes_USDEUR                            + omega_USDEUR;

% PC
obs_PSTAR_ENERGY_TILDE  = mes_PSTAR_ENERGY_TILDE                + omega_PSTAR_ENERGY_TILDE;
obs_PSTAR_OTHER_TILDE   = mes_PSTAR_OTHER_TILDE                 + omega_PSTAR_OTHER_TILDE;
obs_PSTAR_TILDE        	= mes_PSTAR_TILDE                       + omega_PSTAR_TILDE;
obs_CPI_STAR_TILDE      = mes_CPI_STAR_TILDE                    + omega_CPI_STAR_TILDE;

% <end>