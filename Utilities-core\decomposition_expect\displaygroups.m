function displaygroups(groups,groups_nms,language)

%- MAIN FUNCTION ---------------------------------------------------------%

disp('groups_nms = {');
disp(strrep(groups_nms,'\_','_'));
disp('};');
% for ii = 1:size(groups_nms,1)
%     disp([])
% end

disp('groups = {')
if strcmp('EN',upper(language))
    col_lang = 3;
else
    col_lang = 2;
end    
    
for j=1:size(groups,2)
   pom=groups{j};
   disp(['% ' groups_nms{j,col_lang}]);
   if length(pom)>1
       disp(['{''' pom{1} ''',...']); 
       for x=2:length(pom)-1
          disp(['''' pom{x} ''',...']); 
       end
       disp(['''' pom{end} '''}...']);
   else
       if length(pom)>0
           disp(['{''' pom{1} '''}...']);
       else
           disp(['{''' '''}...']); 
       end    
   end    
   % disp(' ');
   if length(groups_nms)==j, disp('% rest uncategorized'), end;
end 
disp('};');

end %- of MAIN FUNCTION --------------------------------------------------%
