function update_word_objects(graphs, Textfile, Tablesfile)

% INPUT: 
% graphs - c<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, ktore sa budu exportovat
% (usporiadane!!!)


startup_doc_session;
startup_xls_session;


%% GRAFY DO TEXTU !!

sudy_graf = false;
graf_num = 1;

for tabulka = [2 3 4 5 6 7 8 9]
    for ii = 1:2 % levy a pravy graf
        % kopirovat graf do clipboard
        print(figure(graphs(graf_num)),'-dmeta');
        % vyber oblast na vlozeni grafu
        if sudy_graf == false
            word.ActiveDocument.Tables.Item(tabulka).Cell(2,1).Select
            sudy_graf = true;
        else
            word.ActiveDocument.Tables.Item(tabulka).Cell(2,2).Select
            sudy_graf = false;
        end
        % paste
        invoke(word.Selection,'Paste');
        % chvilka na kontrolu
%         pause(1);
        graf_num = graf_num + 1;
    end %ii
end %tabulka



%% TABULKY DO TEXTU

%% Tabulka 1 - Inflace od Davida (kapitola 1)
excel.activeWorkbook.Sheets.Item('Celkova inflace').Select;
excel.activeSheet.Range('B5:F18').Select;
invoke(excel.Selection,'CopyPicture',2);
word.activeDocument.inlineShapes.Item(1).Select;
word.activeDocument.inlineShapes.Item(1).Delete;
invoke(word.Selection,'PasteSpecial',0,0,0,0,3);
% invoke(word.Selection,'Paste');
% word.activeDocument.AcceptAllRevisions

%% Tabulka 2 - Srovnani prognozy se skutecnosti
excel.activeWorkbook.Sheets.Item('Prognoza a skutecnost').Select;
excel.activeSheet.Range('B2:J40').Select;
invoke(excel.Selection,'CopyPicture',2);
word.activeDocument.inlineShapes.Item(7).Select;
word.activeDocument.inlineShapes.Item(7).Delete
invoke(word.Selection,'PasteSpecial',0,0,0,0,3);
%invoke(word.Selection,'Paste');
% word.activeDocument.AcceptAllRevisions

%% Tabulka 3a - Rozklad inflace
excel.activeWorkbook.Sheets.Item('Prispevky faktoru').Select;
excel.activeSheet.Range('B2:H10').Select;
invoke(excel.Selection,'CopyPicture',2);
word.activeDocument.inlineShapes.Item(9).Select;
word.activeDocument.inlineShapes.Item(9).Delete
invoke(word.Selection,'PasteSpecial',0,0,0,0,3);
%invoke(word.Selection,'Paste');
% word.activeDocument.AcceptAllRevisions

%% Tabulka 3b - Rozklad sazeb
excel.activeWorkbook.Sheets.Item('Prispevky faktoru').Select;
excel.activeSheet.Range('B13:H23').Select;
invoke(excel.Selection,'CopyPicture',2);
word.activeDocument.inlineShapes.Item(11).Select;
word.activeDocument.inlineShapes.Item(11).Delete
invoke(word.Selection,'PasteSpecial',0,0,0,0,3);
%invoke(word.Selection,'Paste');
% word.activeDocument.AcceptAllRevisions

%% Tabulka 4 - Zahranici
excel.activeWorkbook.Sheets.Item('Zahranici').Select;
excel.activeSheet.Range('B2:I19').Select;
invoke(excel.Selection,'CopyPicture',2);
word.activeDocument.inlineShapes.Item(13).Select;
word.activeDocument.inlineShapes.Item(13).Delete
invoke(word.Selection,'PasteSpecial',0,0,0,0,3);
%invoke(word.Selection,'Paste');
% word.activeDocument.AcceptAllRevisions

%% Tabulka 5 - Regulovane ceny
excel.activeWorkbook.Sheets.Item('Regulovane ceny').Select;
excel.activeSheet.Range('B2:I6').Select;
invoke(excel.Selection,'CopyPicture',2);
word.activeDocument.inlineShapes.Item(15).Select;
word.activeDocument.inlineShapes.Item(15).Delete
invoke(word.Selection,'PasteSpecial',0,0,0,0,3);
%invoke(word.Selection,'Paste');
% word.activeDocument.AcceptAllRevisions


%% Close

drop_xls_session;   % <<< XLS
drop_doc_session;	% <<< DOC

disp([sprintf('\n'), '!!! Jestli se podelal update grafu/tabulek, tak ve Wordu dat ''zpet'' a zkusit znovu...']);
disp([sprintf('\n'), 'Zda se, ze update grafu/tabulek probehnul OK...']);


end