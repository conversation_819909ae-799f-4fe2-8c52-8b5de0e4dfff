%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% DRIVER_DECOMPOSITION
% @ last revision: ZH CNB, Feb 2018
%
% Decompose two forecast exercises using the decomposition tool
%
% INPUT:
% See comments in INPUT section
% 
% OUTPUT:
% 'decomp'		- a structure: necessary input and output of ORIGINAL decomposition
% 'contrib'		- a structure: necessary input and output of GROUPED decomposition
% 'sr_opt'		- a structure: situation report options of both forecasts, 
%					and options for compare
% 'groups'		- a structure: grouped factors of decomposition
% 'groups_nms'	- a structure: labels of groups
% 
% Optional output:
% - Matlab graphs
% - PDF report 
% - XLS table
% - GRIP graph
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% TO DO: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
% - check and adjust sr_options (load)

% Clear the work space
% close all;
clear all; close all;
disp([sprintf('\n'), 'DRIVER_DECOMPOSITION']);


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% INPUT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%% main options
load('GLOBALSETTINGS.mat');
settings = load_settings(FB_ID);

new_ID          = '2025sz03_fbs_pz';%settings.report_prefix;	% name of the new sz (all - forecast, filter)
ext_new			= '';			% choose simulation type (MUST BE DEFINED IN SR_OPT!): 
								% '' (empty string) - free simulation
								% 'intervene'		- intervention simulation
								% 'endog_kurz'		- endogenny kurz
old_ID          = settings.oldfrc_prefix;	% name of the old sz
% old_ID          = 'SS'; %
ext_old			= '';			% choose simulation type (MUST BE DEFINED IN SR_OPT!): 

decomp_range		= qq(2025,2):qq(2026,4);%qq(2000,1):qq(2019,4);%	% range of matrix
plan_type			= 11;						%  --> see STANDARD_FCAST_PLANS.M
												%  1 - classic FORECAST decomposition
												% -1 - classic GRIP decomposition
												% 11 - basic FULFILLMENT decomposition (empty plans) 
												%  0 - individual setting of forecast plans
detail_level        = 1;						% 0 - do just with respect to vars, don't decompose into quarters,
												% 1 - separate NTF obs and fixes on forecast into quarters,
												% 2 - decompose into quarters all variables
ini2obs				= 0;						% 1 - decompose to obs; 0 - decompose just to ini_vars
rls2shocks			= 0;						% 1 - release decomposed with respect to shocks
												% 0 - standard decomposition with respect to observations
obs2shocks          = 0;                        % 1 - history decomposed with respect to shocks
                                                % 0 - standard decomposition with respect to observations
obs_free			= 0;						% 1 - no forecast plan on obs and tunes decomposition, 0 - standard plan
just_filter			= 0;						% 1 - skipFcast; 0 - decompose with respect to plot_range

limit				= 1e-08;					% computation error limit
recompute           = 1;						% 0 - try to find already computed items, 1 - recompute all

% default: plan_type = 11, detail_level = 1, ini2obs = 1, rls2shocks = 0,
% obs2shocks = 1, obs_free = 1, just_filter = 0, SHOCKDECOMP_FB
%% report options

% define the list of variables to decompose & report:
% if you want to see also decompositions of expected endogenous vars
% (without the effects of unexpected fixes and shocks), add '_exp' suffix to the name
% for all final endogenous variables write 'all'
% for all including those without the effects of unexpected fixes and shocks write 'all_exp'
decomp_list = {...
 		'all'
% % 		'all_exp'
%          'y_star_gap'
%          'dot_y_star'
% %          'dot_y_star_trend'
% %          'dot_cpi_star_tilde'
%          'i_star'
%          'i_star_eu'
%          'dot_pstar_other_tilde'
%          'dot_pstar_tilde'
% %         'prem'
% %          'dot_usdeur'
% %          'dot_pstar_energy_tilde'
    };

grouping_type       = 1;			%1			% 1 - Zuzka - READ_GROUPS.M 
												% 0 - Franta - SET_GROUPS.M
autogroups			= 'SHOCKDECOMP_FB_N';			% 0, [] - no grouping into group of factors %FORECASTINI_01_NEW_DETAIL
                                                % 'Factors' - group into factors no time...
												% first part (?_X_X): Forecast, ForecastINI, Grip, Fulfillment ie 'GRIP_01_??'
                                                % 'GRIP_01_??'  - first part is usually defined with respect to
                                                % decomp.type, second is detail level, third some special case
                                                
automatic_groups    = 1;			% 0 - manual setting of groups, 1 - basic (previous choice), 2 - more detail (doesn't work...), 
									% 3 - even more detail, 4 - even more detail
									% 5 - no grouping

firstgroups			= 0;						% if # groups is specified, for each var from decomp_list only # of sorted factors is reported

doXLS				= 1;						% export contributions in .xls format
doReport			= 0;						% report in PDF
doGraphs			= 1;						% MATLAB graphs
doGRIP				= 0;						% GRIP picture !!! Check code!!!
Grip_graph_format   = 'htm';                    % set output format of grip figure,  htm, doc, ppt
Grip_graph_lims     = 0;                        % sets the size of axis in graph
output_ID			= '';				% this string is added to saved report and xls name; could be also empty string:
												% '2text' use this ID for official version exported to xls (graph in text)

language			= 'CZ';						% language version EN/CZ
plot_range          = decomp_range;%qq(2000,1):qq(2019,4);% % range which will be depicted
show_modelchange	= 1;						% shows contribution of model changes in graphs and reports
zz_transform		= 1;						% transformation to %

graph_style         = 0;                        % 1 - new, 0 - old;
click_legend        = 1;                        % Turn on/off onClick feature for detailed decomposition
click_legend_order  = 2;                        % 0 - original no ordering in groups#1234, 1 - order sum contribs from most positive to most negative, 2 - order of abs contribs from most significant to the least
custom_datatips     = 1;                        % Turn on/off onClick custom datatip feature


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% RUN THE DECOMPOSITION
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% load settingss of both sz
if strcmpi(old_ID,'SS')
    [settings.new,settings.old,settings.cmp] = load_settings(new_ID, ...
											'old_id',new_ID, ...
											'extend',ext_new);
    settings.cmp.cmpreport_prefix = [settings.new.report_prefix, '-vs-SS' ];
else
    [settings.new,settings.old,settings.cmp] = load_settings(new_ID, ...
											'old_id',old_ID,...
											'extend',ext_new,...
											'extend_old',ext_old);
end;
disp(['ID: ', settings.cmp.cmpreport_prefix]);

% load or compute partial decompositions
[decomp_tuneold, ...
    decomp_tunenew, ...
    decomp_tunerev, ...
    decomp_rev, ...
    decomp_rls, ...
    decomp_fcastold, ...
    decomp_fcastnew, ...
    decomp_ini, ...
    decomp_type, ...
    d_pure_new, ...
    d_pure_old, ...
    options, settings] = run_decomposition(new_ID, old_ID, settings, ...
    'decomp_range',decomp_range,'plan_type',plan_type,'detail_level',detail_level,...
    'ini2obs',ini2obs,'obs_free',obs_free,'just_filter',just_filter,...
    'rls2shocks',rls2shocks,'obs2shocks',obs2shocks,'recompute',recompute,...
    'limit',limit,'show_modelchange',show_modelchange);

%% merge partial decompositions
decomp = merge_decomposition(old_ID, new_ID, options.decomp_range, settings, ...
    decomp_tuneold, decomp_tunenew, decomp_tunerev, decomp_rev, decomp_rls, ...
    decomp_fcastold, decomp_fcastnew, decomp_ini, d_pure_new, d_pure_old, ...
    plan_type, decomp_type, detail_level, limit, show_modelchange);

%% prepare output
report_decomposition(decomp, settings, plot_range, ...
    'decomp_list',decomp_list, ...
    'grouping_type',grouping_type, ...
    'autogroups',autogroups, ...
    'automatic_groups',automatic_groups, ...
    'firstgroups',firstgroups,...
    'xls',doXLS, ...
    'report',doReport, ...
    'graphs',doGraphs, ...
    'grip',doGRIP,...
    'output_id',output_ID, ...
    'language',language, ...
    'zz_transform',zz_transform, ...
    'graph_style', graph_style, ...
    'grip_type', Grip_graph_format, ...
    'grip_lim', Grip_graph_lims, ...
    'click_legend', click_legend, ...
    'click_legend_order', click_legend_order, ...
    'custom_datatips', custom_datatips);

