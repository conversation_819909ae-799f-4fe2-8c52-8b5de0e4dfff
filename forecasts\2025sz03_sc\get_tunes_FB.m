%**************************************************************************
function [dtunes] = get_tunes_FB(SS)
%**************************************************************************

% Input:	SS		: structure of steady state values and parameters of the model
% Output:	dtunes	: database of tuned tseries 

%**************************************************************************

% Initialize database
dtunes = dbempty();
if SS.FB_gdp_decomp == 1
    %% Perma Tunes
    % tune_dot_y_star_trend_shift, SS = 1
    dtunes.tune_dot_y_star_trend_shift = tseries();

    %original
    dtunes.tune_dot_y_star_trend_shift(qq(2020,1)) = 0.98;%0.975; %0.98
    dtunes.tune_dot_y_star_trend_shift(qq(2020,2)) = 0.915;%0.91; %0.91
    dtunes.tune_dot_y_star_trend_shift(qq(2020,3)) = 1.087;%1.076; % 1.08
    dtunes.tune_dot_y_star_trend_shift(qq(2020,4)) = 1.01;
    dtunes.tune_dot_y_star_trend_shift(qq(2021,1)) = 0.995; %0.985
    dtunes.tune_dot_y_star_trend_shift(qq(2021,2)) = 1.02;

    % y_star_gap, SS = 1
    dtunes.tune_y_star_gap = tseries();

  
    dtunes.tune_y_star_gap(qq(2005,1)) = 0.99;

    dtunes.tune_y_star_gap(qq(2009,1)) = 0.962;
    dtunes.tune_y_star_gap(qq(2011,1)) = 1.00809;
    dtunes.tune_y_star_gap(qq(2011,2)) = 1.0070905;
    dtunes.tune_y_star_gap(qq(2011,3)) = 1.0074905;
    dtunes.tune_y_star_gap(qq(2011,4)) = 1.0050905;
    dtunes.tune_y_star_gap(qq(2012,1)) = 1.0040905;
    dtunes.tune_y_star_gap(qq(2021,4)) = 1.01690517;

    % tune_dot_y_star_trend_fund, SS = 1.0039515
    dtunes.tune_dot_y_star_trend_fund = tseries();

    dtunes.tune_dot_y_star_trend_fund(qq(2019,4)) = 1.003541;
    dtunes.tune_dot_y_star_trend_fund(qq(2020,2)) = 1.0001;
    dtunes.tune_dot_y_star_trend_fund(qq(2020,3)) = 0.999;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,1)) = 0.9995;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,2)) = 1.0008;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,3)) = 1.0013;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,4)) = 1.0017;
    dtunes.tune_dot_y_star_trend_fund(qq(2022,1)) = 1.0032;
    dtunes.tune_dot_y_star_trend_fund(qq(2022,2)) = 1.0046; %
    dtunes.tune_dot_y_star_trend_fund(qq(2022,3)) = 1.0049; %
    dtunes.tune_dot_y_star_trend_fund(qq(2022,4)) = 1.0043; %
    dtunes.tune_dot_y_star_trend_fund(qq(2023,1)) = 1.00388 ;%1.0037
    dtunes.tune_dot_y_star_trend_fund(qq(2023,2)) = 1.00355 ;%1.0034
    dtunes.tune_dot_y_star_trend_fund(qq(2023,3)) = 1.00348 ;% 1.0032
    %% Temp Tunes
   
%     
       dtunes.tune_dot_y_star_trend_fund(qq(2024,1)) = 1.003045;

      
    dtunes.tune_y_star_gap(qq(2025,1)) = 1.0 -0.0107101883 + 0.00016;
     
    %%%%%%----------SC Demand---------%%%%%

% dtunes.tune_y_star_gap(qq(2025,2)) = 0.989284673640973;
% dtunes.tune_y_star_gap(qq(2025,3)) = 0.9845189620741;
% dtunes.tune_y_star_gap(qq(2025,4)) = 0.981828838321581;
% dtunes.tune_y_star_gap(qq(2026,1)) = 0.981126468361108;
% dtunes.tune_y_star_gap(qq(2026,2)) = 0.98219654308787;
% dtunes.tune_y_star_gap(qq(2026,3)) = 0.985014798026458;
% dtunes.tune_y_star_gap(qq(2026,4)) = 0.988776338641153;
% dtunes.tune_y_star_gap(qq(2027,1)) = 0.992614224750789;
% dtunes.tune_y_star_gap(qq(2027,2)) = 0.996229462125463;
% dtunes.tune_y_star_gap(qq(2027,3)) = 0.998755033479358;
% dtunes.tune_y_star_gap(qq(2027,4)) = 1.00038827938553;
% dtunes.tune_y_star_gap(qq(2028,1)) = 1.00143363525663;
% dtunes.tune_y_star_gap(qq(2028,2)) = 1.00205567440205;
% dtunes.tune_y_star_gap(qq(2028,3)) = 1.00237657275634;
% dtunes.tune_y_star_gap(qq(2028,4)) = 1.00248137677761;
% dtunes.tune_y_star_gap(qq(2029,1)) = 1.00242094038895;
% dtunes.tune_y_star_gap(qq(2029,2)) = 1.00232154880242;
% dtunes.tune_y_star_gap(qq(2029,3)) = 1.00219810805584;
% dtunes.tune_y_star_gap(qq(2029,4)) = 1.0020603895307;
% dtunes.tune_y_star_gap(qq(2030,1)) = 1.00191372697208;
% dtunes.tune_y_star_gap(qq(2030,2)) = 1.00176379389375;
% dtunes.tune_y_star_gap(qq(2030,3)) = 1.00161584462977;
% dtunes.tune_y_star_gap(qq(2030,4)) = 1.00147270414778;
% dtunes.tune_y_star_gap(qq(2031,1)) = 1.00133800551958;
% dtunes.tune_y_star_gap(qq(2031,2)) = 1.00121023850265;
% dtunes.tune_y_star_gap(qq(2031,3)) = 1.00109352975642;
% dtunes.tune_y_star_gap(qq(2031,4)) = 1.00098800693192;

    
      %%%%%%----------SC Supply---------%%%%%
%dtunes.tune_y_star_gap(qq(2024,1)) = 0.996011914471255;
dtunes.tune_y_star_gap(qq(2024,2)) = 0.992449384152333;
dtunes.tune_y_star_gap(qq(2024,3)) = 0.991819888037528;
dtunes.tune_y_star_gap(qq(2024,4)) = 0.989471975271968;
dtunes.tune_dot_y_star_trend_fund(qq(2025,1)) = 1.0018;
dtunes.tune_y_star_gap(qq(2025,2)) = 0.993821424006352;
dtunes.tune_y_star_gap(qq(2025,3)) = 0.995768045788815;
dtunes.tune_y_star_gap(qq(2025,4)) = 0.9998538096678;
dtunes.tune_y_star_gap(qq(2026,1)) = 1.00248619633083;
dtunes.tune_y_star_gap(qq(2026,2)) = 1.00498965905446;
dtunes.tune_y_star_gap(qq(2026,3)) = 1.00661197761436;
dtunes.tune_y_star_gap(qq(2026,4)) = 1.00844066419537;
dtunes.tune_y_star_gap(qq(2027,1)) = 1.00995393875807;
dtunes.tune_y_star_gap(qq(2027,2)) = 1.00932732347119;
dtunes.tune_y_star_gap(qq(2027,3)) = 1.00827795531253;
dtunes.tune_y_star_gap(qq(2027,4)) = 1.00698710279829;
dtunes.tune_y_star_gap(qq(2028,1)) = 1.00567496930259;
dtunes.tune_y_star_gap(qq(2028,2)) = 1.00440116276933;
dtunes.tune_y_star_gap(qq(2028,3)) = 1.00320177428343;
dtunes.tune_y_star_gap(qq(2028,4)) = 1.00226304959647;
dtunes.tune_y_star_gap(qq(2029,1)) = 1.00155986759042;
dtunes.tune_y_star_gap(qq(2029,2)) = 1.00114188027486;
dtunes.tune_y_star_gap(qq(2029,3)) = 1.00094161511032;
dtunes.tune_y_star_gap(qq(2029,4)) = 1.00091033172237;
dtunes.tune_y_star_gap(qq(2030,1)) = 1.00099031294364;
dtunes.tune_y_star_gap(qq(2030,2)) = 1.0011398088623;
dtunes.tune_y_star_gap(qq(2030,3)) = 1.00133117531466;
dtunes.tune_y_star_gap(qq(2030,4)) = 1.00152367902196;
dtunes.tune_y_star_gap(qq(2031,1)) = 1.00170343947256;
dtunes.tune_y_star_gap(qq(2031,2)) = 1.00184810243353;
dtunes.tune_y_star_gap(qq(2031,3)) = 1.00195557549507;
dtunes.tune_y_star_gap(qq(2031,4)) = 1.00202811074584;

      
      
      
      
      
      
      
      
%     dtunes.tune_dot_y_star_trend_fund(qq(2025,2)) = 1.0015896;
%      dtunes.tune_dot_y_star_trend_fund(qq(2025,3)) = 1.0013686;%1.0012499;
%     
%      dtunes.tune_dot_y_star_trend_fund(qq(2025,4)) = 1.0013071;%1.0012499;
%      dtunes.tune_dot_y_star_trend_fund(qq(2026,4)) = 1.0031076;
%      
%        dtunes.tune_dot_y_star_trend_fund(qq(2027,4)) = 1.0041700998 ;
%        

       
       
end
end