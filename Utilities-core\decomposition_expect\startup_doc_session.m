%% DOC

%% Nove Word okno
% >>> startup a new Word session...
word = actxserver('Word.Application');
word.Visible = 1;
% <<<

%% Otevreni Word souboru

% Otevrit text plneni (.doc soubor) kam se bude vkladat
disp([sprintf('\n'),'<PERSON> se otevira text plneni v .doc,']);
disp(['...pokud to dlouho trva, tak je soubor nejspis otevreny']);
disp(['...a je potreba ho nejprve zavrit !!!']);

try 
    session_doc = invoke(word.Documents,'Open',Textfile);
    % Otevreni v rezimu oprav
    word.ActiveDocument.trackRevisions = 1;
    % Prijmout zmeny, aby sedel pocet grafu
    % v dokumentu podle toho, co Matlab ceka
    word.activeDocument.AcceptAllRevisions;
    disp([sprintf('\n'), '<PERSON><PERSON>vreni ''.doc'' souboru OK...']);
catch
    disp([sprintf('\n'), 'Nepodarilo se otevrit ''.doc'' soubor s textem plneni...']);
end
