function db_out = convert_to_realimag_structure(db, plan,range)

    svars=get_exogenized(plan,'flag','imag');
    svars_names = fieldnames(svars);
      
    db_pom = dbfun(@(x,y) x+y*1i,db,db);
    range_filter = dbrange(db_pom);
    range_filter = range_filter(1):range(1)-1;
    db_pom = dbbatch(db_pom,'$0','db_pom.$0{range_filter}','classFilter','tseries');
    db_out = dbextend(db, db_pom);
    
    for i=1:length(svars_names)
        range=get(svars.(svars_names{i}),'range');
        db_out.(svars_names{i})(range)=db.(svars_names{i})(range)*1i;    
    end  
            