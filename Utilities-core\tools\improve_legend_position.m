function improve_legend_position(varargin)
%   'min_border' minimum space around legend on graph with less than 4 axes
%   'min_border_more' minimum space around legend on graph with 4 or more axes

default = {
    'min_border',2,...
    'min_border_more',0.7,...
    'steps',50
    };

if nargin>0
    options = passopt(default,varargin);
else
    options = passopt(default);
end

%tic
legend=findobj(gcf,'type','axes','tag','legend');
if length(legend)>1
    return
end

legend_position = get(legend,'position');
legend_position_width = legend_position(3);
legend_position_height = legend_position(4);

axes = findall(gcf,'type','axes');
pocet_axes = length(axes);
max_rank = 10000;
steps = options.steps;
% pozice v rozich
priority_legend_positions = [1 steps steps*(steps-1)+1 steps*steps];

if pocet_axes>0 && pocet_axes<=4
    min_border = options.min_border; %in mm
else
    min_border = options.min_border_more; %in mm
end
figure_position = get(gcf,'position');
figure_width = figure_position(3);
figure_height = figure_position(4);
min_xdistance = min_border/10/figure_width;
min_ydistance = min_border/10/figure_height;


best_legend_position = legend_position;
best_legend_position_rank = -max_rank;
last_axes_position = 0;



for i=pocet_axes:-1:1
    axes_position = get(axes(i),'position');
    type_axes =get(axes(i),'Tag');
    
    if any(axes_position ~= legend_position) && ~strcmp(type_axes,'scribeOverlay')
        if any(axes_position ~= last_axes_position)
            if last_axes_position~=0
                
                [maximum, index]=max(possible_legend_positions_ranks);
                if maximum>best_legend_position_rank
                    best_legend_position = possible_legend_positions(index,:);
                    best_legend_position_rank = maximum;
                end
            end
            
            
            axes_position_left = axes_position(1);
            axes_position_bottom = axes_position(2);
            axes_position_width = axes_position(3);
            axes_position_height = axes_position(4);
            Xstep = (axes_position_width - legend_position_width-2*min_xdistance) /(steps-1);
            Ystep = (axes_position_height - legend_position_height-2*min_ydistance) /(steps-1);
            
            axes_xlim = get(axes(i),'Xlim');
            axes_ylim = get(axes(i),'Ylim');
            
            %tic
            pom_x_positions = reshape(ones(2,1)*[axes_position_left+min_xdistance axes_position_left+axes_position_width-legend_position_width-min_xdistance],1,[]);
            pom_y_positions = repmat([axes_position_bottom+min_ydistance axes_position_bottom+axes_position_height-legend_position_height-min_ydistance],1,2);
            pom_width_positions = ones(1,2*2)*legend_position_width;
            pom_height_positions = ones(1,2*2)*legend_position_height;
            
            possible_legend_positions = cat(2,pom_x_positions',pom_y_positions',pom_width_positions',pom_height_positions');
            possible_legend_positions_center = possible_legend_positions(:,1:2)+ones(2*2,1)*[legend_position_width/2 legend_position_height/2];
            possible_legend_positions_ranks = ones(2*2,1)*max_rank;
            clearvars pom_x_positions pom_y_positions pom_width_positions pom_height_positions;
            %toc
        end
        
        draws = get(axes(i),'children');
        for j=1:length(draws)
            is_bar = isprop(draws(j),'BarWidth');
            if is_bar
                axes_xlim = get(axes(i),'Xlim');
                axes_ylim = get(axes(i),'Ylim');
                % vzdalenost od baru
                bar_width = get(draws(j),'BarWidth')/figure_width;
                xdata = get(draws(j),'Xdata');
                ydata = get(draws(j),'Ydata');
                xdata_transformed = axes_position_left+(xdata-axes_xlim(1))*axes_position_width/(axes_xlim(2)-axes_xlim(1));
                ydata_transformed = axes_position_bottom+(ydata-axes_ylim(1))*axes_position_height/(axes_ylim(2)-axes_ylim(1));
                y_zero_position = axes_position_bottom+(0-axes_ylim(1))*axes_position_height/(axes_ylim(2)-axes_ylim(1));
                
                for a=1:length(xdata_transformed)
                    
                    dist = ones(2*2,1)*max_rank;
                    
                    % x mezi, y mezi
                    index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))<legend_position_width/2+bar_width/2+min_xdistance) & ...
                        ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)>=min(ydata_transformed(a),y_zero_position) & ...
                        (possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)<=max(ydata_transformed(a),y_zero_position));
                    dist(index) = ones(sum(index),1)*-1;
                    
                    % x mimo, y mezi
                    index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))>=legend_position_width/2+bar_width/2+min_xdistance) & ...
                        ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)>=min(ydata_transformed(a),y_zero_position) & ...
                        (possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)<=max(ydata_transformed(a),y_zero_position));
                    dist(index) = figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_transformed(a))-ones(sum(index),1)*(legend_position_width/2+bar_width/2+min_xdistance));
                    
                    % x mezi, y pod
                    index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))<legend_position_width/2+bar_width/2+min_xdistance) & ...
                        ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)<min(ydata_transformed(a),y_zero_position));
                    dist(index) = figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*min(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance));
                    
                    % x mezi, y nad
                    index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))<legend_position_width/2+bar_width/2+min_xdistance) & ...
                        ((possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)>max(ydata_transformed(a),y_zero_position));
                    dist(index) = figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*max(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance));
                    
                    % x mimo, y pod
                    index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))>=legend_position_width/2+bar_width/2+min_xdistance) & ...
                        ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)<min(ydata_transformed(a),y_zero_position));
                    dist(index) = ((figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*min(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance))).^2+...
                        (figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_transformed(a))-ones(sum(index),1)*(legend_position_width+bar_width))).^2).^.5;
                    
                    % x mimo, y nad
                    index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))>=legend_position_width/2+bar_width/2+min_xdistance) & ...
                        ((possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)>max(ydata_transformed(a),y_zero_position));
                    dist(index) = ((figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*max(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance))).^2+...
                        (figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_transformed(a))-ones(sum(index),1)*(legend_position_width/2+bar_width/2+min_xdistance))).^2).^.5;
                    
                    index = dist<possible_legend_positions_ranks;
                    possible_legend_positions_ranks(index)=dist(index);
                    if all(possible_legend_positions_ranks==-1)
                        break
                    end
                end
            else
                axes_xlim = get(axes(i),'Xlim');
                axes_ylim = get(axes(i),'Ylim');
                % vzdalenost od cary
                xdata = get(draws(j),'Xdata');
                ydata = get(draws(j),'Ydata');
                xdata_transformed = axes_position_left+(xdata-axes_xlim(1))*axes_position_width/(axes_xlim(2)-axes_xlim(1));
                ydata_transformed = axes_position_bottom+(ydata-axes_ylim(1))*axes_position_height/(axes_ylim(2)-axes_ylim(1));
                
                xdata_steps = xdata_transformed(1):Xstep:xdata_transformed(end);
                xdata_transformed_index = 1;
                for a=1:length(xdata_steps)
                    while xdata_steps(a)>xdata_transformed(xdata_transformed_index+1)
                        if xdata_transformed_index<length(xdata_transformed)
                            xdata_transformed_index=xdata_transformed_index+1;
                        end
                    end
                    ydata_steps(a) = ydata_transformed(xdata_transformed_index)+ ...
                        (ydata_transformed(xdata_transformed_index+1)-ydata_transformed(xdata_transformed_index))*...
                        (xdata_steps(a)-xdata_transformed(xdata_transformed_index))/...
                        (xdata_transformed(xdata_transformed_index+1)-xdata_transformed(xdata_transformed_index));
                    
                    
                    % distance from tseries
                    % 4 moznosti: 1)v legende --> -1, 2)v x ano, y ne --> od strany 3)v
                    % x ne v y ano --> od strany, 4) v x ne v y ne --> od rohu
                    
                    % !!!! neporovnavat s centrem ale s rohem....
                    
                    dist = ones(2*2,1)*max_rank;
                    
                    index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))<legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))<legend_position_height/2+min_ydistance);
                    dist(index) = ones(sum(index),1)*-1;
                    
                    index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))<legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))>=legend_position_height/2+min_ydistance);
                    dist(index) = figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*ydata_steps(a))-ones(sum(index),1)*(legend_position_height/2+min_ydistance));
                    
                    index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))>=legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))<legend_position_height/2+min_ydistance);
                    dist(index) = figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_steps(a))-ones(sum(index),1)*(legend_position_width/2+min_xdistance));
                    
                    index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))>=legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))>=legend_position_height/2+min_ydistance);
                    dist(index) = ( (figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*ydata_steps(a))-ones(sum(index),1)*(legend_position_height/2+min_ydistance))).^2 + ...
                        (figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_steps(a))-ones(sum(index),1)*(legend_position_width/2+min_xdistance))).^2 ).^.5;
                    
                    index = dist<possible_legend_positions_ranks;
                    possible_legend_positions_ranks(index)=dist(index)';
                    if all(possible_legend_positions_ranks==-1)
                        break
                    end
                end
            end
            if all(possible_legend_positions_ranks==-1)
                break
            end
        end
        last_axes_position = axes_position;
    end
end

[maximum, index]=max(possible_legend_positions_ranks);
if maximum>best_legend_position_rank
    best_legend_position = possible_legend_positions(index,:);
    best_legend_position_rank = maximum;
end
last_axes_position = 0;

if best_legend_position_rank<0
    
    for i=pocet_axes:-1:1
        axes_position = get(axes(i),'position');
        type_axes =get(axes(i),'Tag');
        
        if any(axes_position ~= legend_position) && ~strcmp(type_axes,'scribeOverlay')
            if any(axes_position ~= last_axes_position)
                if last_axes_position~=0
                    
                    [maximum, index]=max(possible_legend_positions_ranks);
                    if maximum>best_legend_position_rank
                        best_legend_position = possible_legend_positions(index,:);
                        best_legend_position_rank = maximum;
                    end
                end
                
                
                axes_position_left = axes_position(1);
                axes_position_bottom = axes_position(2);
                axes_position_width = axes_position(3);
                axes_position_height = axes_position(4);
                Xstep = (axes_position_width - legend_position_width-2*min_xdistance) /(steps-1);
                Ystep = (axes_position_height - legend_position_height-2*min_ydistance) /(steps-1);
                
                axes_xlim = get(axes(i),'Xlim');
                axes_ylim = get(axes(i),'Ylim');
                
                %tic
                pom_x_positions = reshape(ones(steps,1)*(axes_position_left+min_xdistance:Xstep:axes_position_left+axes_position_width-legend_position_width-min_xdistance),1,[]);
                pom_y_positions = repmat((axes_position_bottom+min_ydistance:Ystep:axes_position_bottom+axes_position_height-legend_position_height-min_ydistance),1,steps);
                pom_width_positions = ones(1,steps*steps)*legend_position_width;
                pom_height_positions = ones(1,steps*steps)*legend_position_height;
                
                possible_legend_positions = cat(2,pom_x_positions',pom_y_positions',pom_width_positions',pom_height_positions');
                possible_legend_positions_center = possible_legend_positions(:,1:2)+ones(steps*steps,1)*[legend_position_width/2 legend_position_height/2];
                possible_legend_positions_ranks = ones(steps*steps,1)*max_rank;
                clearvars pom_x_positions pom_y_positions pom_width_positions pom_height_positions;
                %toc
            end
            
            draws = get(axes(i),'children');
            for j=1:length(draws)
                is_bar = isprop(draws(j),'BarWidth');
                if is_bar
                    axes_xlim = get(axes(i),'Xlim');
                    axes_ylim = get(axes(i),'Ylim');
                    % vzdalenost od baru
                    bar_width = get(draws(j),'BarWidth')/figure_width;
                    xdata = get(draws(j),'Xdata');
                    ydata = get(draws(j),'Ydata');
                    xdata_transformed = axes_position_left+(xdata-axes_xlim(1))*axes_position_width/(axes_xlim(2)-axes_xlim(1));
                    ydata_transformed = axes_position_bottom+(ydata-axes_ylim(1))*axes_position_height/(axes_ylim(2)-axes_ylim(1));
                    y_zero_position = axes_position_bottom+(0-axes_ylim(1))*axes_position_height/(axes_ylim(2)-axes_ylim(1));
                    
                    for a=1:length(xdata_transformed)
                        
                        dist = ones(steps*steps,1)*max_rank;
                        
                        % x mezi, y mezi
                        index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))<legend_position_width/2+bar_width/2+min_xdistance) & ...
                            ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)>=min(ydata_transformed(a),y_zero_position) & ...
                            (possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)<=max(ydata_transformed(a),y_zero_position));
                        dist(index) = ones(sum(index),1)*-1;
                        
                        % x mimo, y mezi
                        index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))>=legend_position_width/2+bar_width/2+min_xdistance) & ...
                            ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)>=min(ydata_transformed(a),y_zero_position) & ...
                            (possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)<=max(ydata_transformed(a),y_zero_position));
                        dist(index) = figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_transformed(a))-ones(sum(index),1)*(legend_position_width/2+bar_width/2+min_xdistance));
                        
                        % x mezi, y pod
                        index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))<legend_position_width/2+bar_width/2+min_xdistance) & ...
                            ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)<min(ydata_transformed(a),y_zero_position));
                        dist(index) = figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*min(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance));
                        
                        % x mezi, y nad
                        index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))<legend_position_width/2+bar_width/2+min_xdistance) & ...
                            ((possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)>max(ydata_transformed(a),y_zero_position));
                        dist(index) = figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*max(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance));
                        
                        % x mimo, y pod
                        index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))>=legend_position_width/2+bar_width/2+min_xdistance) & ...
                            ((possible_legend_positions_center(:,2)+legend_position_height/2+min_ydistance)<min(ydata_transformed(a),y_zero_position));
                        dist(index) = ((figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*min(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance))).^2+...
                            (figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_transformed(a))-ones(sum(index),1)*(legend_position_width+bar_width))).^2).^.5;
                        
                        % x mimo, y nad
                        index = (abs(possible_legend_positions_center(:,1)-xdata_transformed(a))>=legend_position_width/2+bar_width/2+min_xdistance) & ...
                            ((possible_legend_positions_center(:,2)-legend_position_height/2-min_ydistance)>max(ydata_transformed(a),y_zero_position));
                        dist(index) = ((figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*max(ydata_transformed(a),y_zero_position))-ones(sum(index),1)*(legend_position_height/2+min_ydistance))).^2+...
                            (figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_transformed(a))-ones(sum(index),1)*(legend_position_width/2+bar_width/2+min_xdistance))).^2).^.5;
                        
                        index = dist<possible_legend_positions_ranks;
                        possible_legend_positions_ranks(index)=dist(index);
                        if all(possible_legend_positions_ranks==-1)
                            break
                        end
                    end
                else
                    axes_xlim = get(axes(i),'Xlim');
                    axes_ylim = get(axes(i),'Ylim');
                    % vzdalenost od cary
                    xdata = get(draws(j),'Xdata');
                    ydata = get(draws(j),'Ydata');
                    xdata_transformed = axes_position_left+(xdata-axes_xlim(1))*axes_position_width/(axes_xlim(2)-axes_xlim(1));
                    ydata_transformed = axes_position_bottom+(ydata-axes_ylim(1))*axes_position_height/(axes_ylim(2)-axes_ylim(1));
                    
                    xdata_steps = xdata_transformed(1):Xstep:xdata_transformed(end);
                    xdata_transformed_index = 1;
                    for a=1:length(xdata_steps)
                        while xdata_steps(a)>xdata_transformed(xdata_transformed_index+1)
                            if xdata_transformed_index<length(xdata_transformed)
                                xdata_transformed_index=xdata_transformed_index+1;
                            end
                        end
                        ydata_steps(a) = ydata_transformed(xdata_transformed_index)+ ...
                            (ydata_transformed(xdata_transformed_index+1)-ydata_transformed(xdata_transformed_index))*...
                            (xdata_steps(a)-xdata_transformed(xdata_transformed_index))/...
                            (xdata_transformed(xdata_transformed_index+1)-xdata_transformed(xdata_transformed_index));
                        
                        
                        % distance from tseries
                        % 4 moznosti: 1)v legende --> -1, 2)v x ano, y ne --> od strany 3)v
                        % x ne v y ano --> od strany, 4) v x ne v y ne --> od rohu
                        
                        % !!!! neporovnavat s centrem ale s rohem....
                        
                        dist = ones(steps*steps,1)*max_rank;
                        
                        index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))<legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))<legend_position_height/2+min_ydistance);
                        dist(index) = ones(sum(index),1)*-1;
                        
                        index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))<legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))>=legend_position_height/2+min_ydistance);
                        dist(index) = figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*ydata_steps(a))-ones(sum(index),1)*(legend_position_height/2+min_ydistance));
                        
                        index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))>=legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))<legend_position_height/2+min_ydistance);
                        dist(index) = figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_steps(a))-ones(sum(index),1)*(legend_position_width/2+min_xdistance));
                        
                        index = (abs(possible_legend_positions_center(:,1)-xdata_steps(a))>=legend_position_width/2+min_xdistance) & (abs(possible_legend_positions_center(:,2)-ydata_steps(a))>=legend_position_height/2+min_ydistance);
                        dist(index) = ( (figure_height*(abs(possible_legend_positions_center(index,2)-ones(sum(index),1)*ydata_steps(a))-ones(sum(index),1)*(legend_position_height/2+min_ydistance))).^2 + ...
                            (figure_width*(abs(possible_legend_positions_center(index,1)-ones(sum(index),1)*xdata_steps(a))-ones(sum(index),1)*(legend_position_width/2+min_xdistance))).^2 ).^.5;
                        
                        index = dist<possible_legend_positions_ranks;
                        possible_legend_positions_ranks(index)=dist(index)';
                        if all(possible_legend_positions_ranks==-1)
                            break
                        end
                    end
                end
                if all(possible_legend_positions_ranks==-1)
                    break
                end
            end
            last_axes_position = axes_position;
        end
    end
    
    [maximum, index]=max(possible_legend_positions_ranks);
    if maximum>best_legend_position_rank
        best_legend_position = possible_legend_positions(index,:);
        best_legend_position_rank = maximum;
    end
    
end

if best_legend_position_rank<0
    best_legend_position = legend_position;
end

set(legend,'position',best_legend_position);
clearvars possible_legend_positions possible_legend_positions_center possible_legend_positions_ranks index ydata_steps priority_legend_positions;
clearvars Xstep Ystep index dist ydata_steps xdata_steps xdata ydata xdata_transformed ydata_transformed is_bar draws;
clearvars legend legend_position legend_position_height legend_position_width axes axes_position axes_position_bottom axes_position_height axes_position_left axes_position_width axes_xlim axes_ylim;
clearvars min_border maximum max_rank min_border min_xdistance min_ydistance;
clearvars figure_height figure_position figure_width steps pocet_axes last_axes_position;
clearvars bar_width best_legend_position best_legend_position_rank a i j type_axes xdata_transformed_index y_zero_position
%toc