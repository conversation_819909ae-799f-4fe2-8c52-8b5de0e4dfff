function [db_hist,db_fcast] = db_update(db_new,sr_opt)

% keyboard;

%% Manual data selection

% 1 -> selected time series will be taken from new histcore
% 0 -> selected time series will be taken from old histcore

% Filter vars
f.obs_C       = 1;
f.obs_J       = 1;
f.obs_GP      = 1;
f.obs_X       = 1;
f.obs_N       = 1;
f.obs_PC      = 1;
f.obs_PJ      = 1;
f.obs_PG      = 1;
f.obs_PX      = 1;
f.obs_PN      = 1;
f.obs_W       = 1;
f.obs_L       = 1;
f.obs_S       = 1;
f.obs_I       = 1;
f.obs_N_STAR  = 1;
f.obs_P_STAR_TILDE = 1;
f.obs_I_STAR  = 0;
f.obs_P       = 1;
f.obs_PREG    = 1;
f.obs_TARGET4 = 1;
f.obs_CPI     = 1;


% Fcast vars
fc.dot_n_star       = 1;
fc.dot_p_star_tilde = 1;
fc.i_star           = 0;
fc.dot_pREG         = 1;
fc.dot_g            = 1;
fc.dot_gp           = 1;
fc.dot_p            = 1;

%% Filter or fcast mode
if isfield(db_new,'obs_I')
    filtering = true;
else
    filtering = false;
end

%% DB overwriting (no need to touch anything below)

h_old = changedata(['..\database\Input-data\' sr_opt.histcore_compare], sr_opt);

if filtering
    
    d_old = histdata(h_old, sr_opt);
    filter_names = fieldnames(f);
    for ii = 1:length(filter_names)
        if f.(filter_names{ii})
            disp(['||| Taking ' filter_names{ii} ' from new histcore...']);
            d_old.(filter_names{ii}) = db_new.(filter_names{ii});
        end
    end
    
    db_hist = d_old;
    db_fcast= '';
    
else

    d_old  = fcastdata(h_old, sr_opt);
    fcast_names = fieldnames(fc);
    for ii = 1:length(fcast_names)
        if fc.(fcast_names{ii})
            disp(['||| Taking ' fcast_names{ii} ' from new histcore...']);
            d_old.(fcast_names{ii}) = db_new.(fcast_names{ii});
        end
    end
    
    db_hist = '';
    db_fcast= d_old;
    
end

end %<eof>