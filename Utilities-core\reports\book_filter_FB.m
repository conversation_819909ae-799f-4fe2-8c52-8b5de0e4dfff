function book_filter(d, m, settings, heading, basename)
disp(['Creating ' heading]);

rng = qq(1998,1):settings.ehist;

eList = dbnames(d,'nameFilter','^eps_\w*');
omegaList = dbnames(d,'nameFilter','^omega_\w*');

%--open the report--%
x = report.new(heading);

%%
%--Graph--%
sty = struct(); 
sty.line.lineWidth = 2;
sty.line.lineStyle = '-';
sty.line.Color = {[1 0 0],[0 0.5 0],[0 0 1]};
sty.highlight.faceColor = [0.8,0.8,0.8];
sty.legend.location = 'SouthOutside';
sty.legend.Orientation = 'Horizontal';
sty.axes.ylim=[ ...
                    '!! ylim = get(H,''ylim'');', ...
                    'k = 0.05*(ylim(2)-ylim(1));', ...
                    'SET = [ylim(1)-k,ylim(2)+k];'];

post = [ ...
    'leg = findobj(gcf,''Location'',''SouthOutside'');'...
    'set(leg,''location'',''none'');',...
    'posleg = get(leg,''position'');', ...
    'pos = get(H,''position'');', ...
    'k = 0.0075;', ...
    'set(H,''position'',[pos(1),pos(2)+k,pos(3),pos(4)-k]);'...
    'k = 0.063;', ...
    'set(leg,''position'',[posleg(1),pos(2)-k,posleg(3),posleg(4)]);'];

x.figure('Measurement Errors in Level','range', rng, 'subplot',[3 3],'dateformat','YY:P', 'style', sty,'datetick',rng(1):20:rng(end));
 x.graph(get(d.ne_zz_pstar_tilde,'comment'),'legend',true,'postprocess',post);
   x.series('Orig',d.ne_zz_pstar_tilde);
   x.series('Obs',d.zz_obs_PSTAR_TILDE);
   x.series('Mes',d.zz_mes_PSTAR_TILDE);
%     x.graph(get(d.ne_zz_p_BrentUSD_tilde, 'comment'));
%    x.series('',d.ne_zz_p_BrentUSD_tilde);
%    x.series('',d.zz_obs_P_BRENTUSD_TILDE);
%    x.series('',d.zz_mes_P_BRENTUSD_TILDE);
 x.graph(get(d.ne_zz_pstar_other_tilde, 'comment'));
   x.series('',d.ne_zz_pstar_other_tilde);
   x.series('',d.zz_obs_PSTAR_OTHER_TILDE);
   x.series('',d.zz_mes_PSTAR_OTHER_TILDE);
 x.graph(get(d.ne_zz_i_star, 'comment'),'legend',true,'postprocess',post);
   x.series('Obs',(1+d.ne_zz_i_star/100));
   x.series('Shdw',(1+d.ne_zz_i_star_shadow/100));
   x.series('Mes',d.zz_mes_I_STAR);  
 x.graph(get(d.ne_zz_usdeur, 'comment'));
   x.series('',d.ne_zz_usdeur);
   x.series('',d.zz_obs_USDEUR);
   x.series('',d.zz_mes_USDEUR);    
 x.graph(get(d.ne_zz_y_star, 'comment'));
   x.series('',d.ne_zz_y_star);
   x.series('',d.zz_obs_Y_STAR);
   x.series('',d.zz_mes_Y_STAR);
%  x.graph(get(d.zz_y_star_trend, 'comment'));
%    x.series('',d.zz_y_star_trend);
% %    x.series('',d.zz_obs_Y_STAR_TREND);
%    x.series('',d.zz_mes_Y_STAR_TREND);
%  x.graph(get(d.zz_y_star_gap, 'comment'));
%    x.series('',d.zz_y_star_gap);
% %    x.series('',d.zz_obs_Y_STAR_GAP);
%    x.series('',d.zz_mes_Y_STAR_GAP);
   
%% ----------------%
%--Graph--%
x.pagebreak();

x.figure('Measurement Errors in Growths','range', rng, 'subplot',[3 3],'dateformat','YY:P', 'style', sty,'datetick',rng(1):20:rng(end));
 x.graph(get(d.ne_zz_dot_pstar_tilde,'comment'),'legend',true,'postprocess',post);
   x.series('Orig',d.ne_zz_dot_pstar_tilde);
   x.series('Obs',4*(d.obs_PSTAR_TILDE-d.obs_PSTAR_TILDE{-1}));
   x.series('Mes',d.zz_dot_pstar_tilde);
%  x.graph(get(d.ne_zz_dot_p_BrentUSD_tilde,'comment'));
%    x.series('',d.ne_zz_dot_p_BrentUSD_tilde);
%    x.series('',4*(d.obs_P_BRENTUSD_TILDE-d.obs_P_BRENTUSD_TILDE{-1}));
%    x.series('',d.zz_dot_p_BrentUSD_tilde);  
 x.graph(get(d.ne_zz_dot_pstar_other_tilde,'comment'));
   x.series('',d.ne_zz_dot_pstar_other_tilde);
   x.series('',4*(d.obs_PSTAR_OTHER_TILDE-d.obs_PSTAR_OTHER_TILDE{-1}));
   x.series('',d.zz_dot_pstar_other_tilde);  
 x.graph(get(d.ne_zz_dot_usdeur, 'comment'));
   x.series('',d.ne_zz_dot_usdeur);
   x.series('',4*(d.obs_USDEUR-d.obs_USDEUR{-1}));
   x.series('',d.zz_dot_usdeur);    
 x.graph(get(d.ne_zz_dot_y_star, 'comment'));
   x.series('',d.ne_zz_dot_y_star);
   x.series('',4*(d.obs_Y_STAR-d.obs_Y_STAR{-1}));
   x.series('',d.zz_dot_y_star);
 x.graph(get(d.zz_dot_y_star_trend, 'comment'));
%   x.series('',d.ne_zz_dot_y_star_trend);
%    x.series('',4*(d.obs_Y_STAR_TREND-d.obs_Y_STAR_TREND{-1}));
   x.series('',d.zz_dot_y_star_trend);
% %% --structural shocks review (1)--%
% %--Graph--%
% x.pagebreak();
% 
% x.figure('Measurement Errors in Level','range', rng, 'subplot',[3 3],'dateformat','YY:P', 'style', sty,'datetick',rng(1):20:rng(end));
% graph_list={'eps_habit','eps_prem','eps_uip','eps_forex',...
%     'eps_aux_pc','eps_Istar','eps_Pstar','eps_Nstar'};
% for i=1:8
%     x.graph(get(d.(graph_list{i}),'comment'));
%     x.series('',d.(graph_list{i}));
% end
% %% --measurement errors--%
% %--Graph--%
% x.pagebreak();
% 
% sty.line.Color = {[0 0 1]};
% x.figure('Measurement Errors in Level','range', rng, 'subplot',[3 3],'dateformat','YY:P', 'style', sty,'datetick',rng(1):20:rng(end));
% graph_list={'omega_C','omega_J','omega_GP','omega_X','omega_N',...
%     'omega_N_STAR','omega_P_STAR_TILDE','omega_I','omega_I_STAR'};
% for i=1:9
%     x.graph(get(d.(graph_list{i}),'comment'),'legend',true,'postprocess',post);
%     x.series('Measurement Error',d.(graph_list{i}));
% end
%% Residuals

for ix = 1 : length(eList)
	if mod(ix,9)==1
		x.figure('Residuals','range', rng, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',rng(1):12:rng(end));
	end
	x.graph(eList{ix},'style',sty,'zeroLine=',true);
	x.series(get(d.([eList{ix}]), 'comment'),d.([eList{ix}]));
end

for ix = 1 : length(omegaList)
	if mod(ix,9)==1
		x.figure('Measurement errors','range', rng, 'subplot',[3 3],'dateformat','YY:P','style',sty,'datetick',rng(1):12:rng(end));
	end
	x.graph(omegaList{ix},'style',sty,'zeroLine=',true);
	x.series(get(d.([omegaList{ix}]), 'comment'),d.(omegaList{ix}));
end


%% -----------------%
x.publish([basename '.pdf'],'paperSize','a4paper','display',false);