% Driver for comparison of two model objects using function compare_model

clear all
%% chose repo for full model
repo ='2022_SZ_03';
modelName = '2022sz03';
filter = 1;

if filter == 1
    type = 'kalman';
else
    type = 'model';
end

%% loading models
m_full = load(['..\..\..\..\SZ\' repo '\model\trunk\forecasts\database\Output-data\' modelName '-' type '.mat']);
% m_full = load(['..\..\..\Model\forecasts\database\Output-data\develop_estim_ppi-kalman-fb.mat']);
% m_full = load(['..\database\Output-data\2022sz05-kalman-fb.mat']);
% m_full =  load(['..\database\Output-data\develop_estim_ppi_new-kalman-fb.mat']);
% m_full =  load(['..\database\Output-data\develop_FB_simple-kalman-fb.mat']);
m_full = m_full.m;

% m_small =  load(['..\..\FB\Model\forecasts\development\foreign_block.mat']);
% m_small =  load(['..\..\FB\Model\forecasts\database\Output-data\2022sz03_fbs-kalman-fb.mat']); 
% m_small =  load(['..\database\Output-data\develop_estim_ppi-kalman-fb.mat']);
% m_small =  load(['..\..\..\Model\forecasts\database\Output-data\develop_estim_ppi-kalman-fb.mat']);   % 2022sz03_baseline-filtermodel-fb
                                                                                                                % 2022sz03_baseline-fcastmodel-fb
m_small = load(['..\database\Output-data\2022sz05-kalman-fb.mat']);

m_small = m_small.m;

%% getting lists from model objects
% small model
yList_small= get(m_small,'yList');
xList_small= get(m_small,'xList');
eList_small= get(m_small,'eList');
pList_small= get(m_small,'pList');
params_small = get(m_small,'params');
% stdList_small= get(m_small,'stdList');
% yEqtn_small= get(m_small,'yEqtn');
% xEqtn_small= get(m_small,'xEqtn');
ss_small= get(m_small,'sstate');

% large model
yList_full= get(m_full,'yList');
xList_full= get(m_full,'xList');
eList_full= get(m_full,'eList');
pList_full= get(m_full,'pList');
params_full = get(m_full,'params');
% stdList_full= get(m_full,'stdList');
% yEqtn_full= get(m_full,'yEqtn');
% xEqtn_full= get(m_full,'xEqtn');
ss_full= get(m_full,'sstate');

%% model comparison
% compare parameters, variables and shocks in two models
% parameters with different values and unused paramters in larger model are reported
params_diff = compare_model(fieldnames(params_small),fieldnames(params_full),params_small,params_full); % parameters
x_diff = compare_model(xList_small,xList_full,ss_small,ss_full);                                        % model and measurement variables variables
y_diff = compare_model(yList_small,yList_full,ss_small,ss_full);                                        % observable variables
e_diff = compare_model(eList_small,eList_full,ss_small,ss_full);                                        % shocks (model and data)

[{'Params','',''}; params_diff; {'xList','',''}; x_diff; {'yList','',''}; y_diff ; {'eList','',''}; e_diff ]
% std_small = strfind(names,'std_')
