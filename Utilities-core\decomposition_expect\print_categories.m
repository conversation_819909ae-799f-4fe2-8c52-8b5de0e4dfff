%% Categorize output of decomposition into groups
function [GROUPS GROUPS_NMS] = print_categories(autogroups,datenames,varargin)

%- MAIN FUNCTION ---------------------------------------------------------%

%% parse options

default = {...
	'language',             'CZ' ...
	'group_modelchanges',	true, ...
	'group_tunes',			true, ...
	'group_obs',			true, ...
	'group_fix',			true, ...
	'group_res',			true, ...
	'pair_tunes',			true, ...
	'pair_filters',			true, ...
	'pair_fcasts',			true, ...
	'pair_expsur',			true, ...
	};

options = passopt(default, varargin{1:end});

% language
switch upper(options.language)
	case {'CZ'}
		language = 2;
	case {'EN'}
		language = 3;
end

%% split datenames

s = regexp(datenames,'__','split');
ndn = length(datenames);
indate = cell(ndn,1);
intype = cell(ndn,1);
inname = cell(ndn,1);
for i = 1:ndn
	indate{i} = char(s{i}(1));
	intype{i} = char(s{i}(2));
	inname{i} = char(s{i}(3));
end

%% prepare predefined groups

type = prepare_standard_type(intype,options);
pat_type = setdiff(unique(type),{''});
pat_group = prepare_standard_groups(type,options);
pat_rest = setdiff(pat_type,pat_group);

groups = cell(1,0);
groups_nms = cell(0,3);

%% make predefined groups

for i = 1:length(pat_group)
	groups_nms(i,:) = standard_groups_nms(pat_group{i});
	groups(:,i) = fill_standard_groups(pat_group{i},indate,type,inname,intype);
end

%% make groups set manually (in set_groups_filter)

[groups_aux groups_nms_aux] = set_groups(autogroups,...
	pat_rest,indate,type,inname,intype);

%% combine

aux = size(groups,2);
groups(:,aux+1:aux+size(groups_aux,2)) = groups_aux(:,:);
aux = size(groups_nms,1);
groups_nms(aux+1:aux+size(groups_nms_aux,1),:) = groups_nms_aux(:,:);

% display on screen
for i = 1:size(groups,2)
	disp(groups_nms(i,language));
	for j = 1:size(groups{i},2)
		disp(groups{i}{j});
	end
end

%% output

GROUPS		= groups;
GROUPS_NMS	= groups_nms(:,[1,language]);

end %- of MAIN FUNCTION --------------------------------------------------%






%% SUB FUNCTIONS

function group_nms = standard_groups_nms(pattern)

predefined = { 
% 			'id'			'CZ'						'EN'
			'model'			'Zmeny modelu'				'Model Changes'
			'tunes'			'Tuny'						'Tunes'
			'tunesnew'		'Nove tuny'					'New Tunes'
			'tunesold'		'Stare tuny'				'Old Tunes'
			'obs'			'Pozorovani'				'Observations'
			'obsrev'		'Pozorovani revizi'			'Observations in Revisions'
			'obsrls'		'Pozorovani novych dat'		'Observations in Release'
			'fix'			'Fixy'						'Fixes'
			'fixexp'		'Ocekavane fixy'			'Expected Fixes'
			'fixexpnew'		'Ocekavane nove fixy'		'Expected New Fixes'
			'fixexpold'		'Ocekavane stare fixy'		'Expected Old Fixes'
			'fixsur'		'Neocekavane fixy'			'Unexpected Fixes'
			'fixsurnew'		'Neocekavane nove fixy'		'Unexpected New Fixes'
			'fixsurold'		'Neocekavane stare fixy'	'Unexpected Old Fixes'
			'fixnew'		'Nove fixy'					'New Fixes'
			'fixold'		'Stare fixy'				'Old Fixes'
			'res'			'Rezidua'					'Residuals'
			'resexp'		'Ocekavane rezidua'			'Expected Residuals'
			'resexpnew'		'Ocekavane nove rezidua'	'Expected New Residuals'
			'resexpold'		'Ocekavane stare rezidua'	'Expected Old Residuals'
			'ressur'		'Neocekavane rezidua'		'Unexpected Residuals'
			'ressurnew'		'Neocekavane nove rezidua'	'Unexpected New Residuals'
			'ressurold'		'Neocekavane stare rezidua'	'Unexpected Old Residuals'
			'resnew'		'Nove rezidua'				'New Residuals'
			'resold'		'Stare rezidua'				'Old Residuals'
			'eps'			'Rezidua novych dat'		'Residuals in Release'
			'epsexp'		'Ocek. rezidua novych dat'	'Expected Residuals in Release'
			'epssur'		'Neoce. rezidua novych dat'	'Unexpected Residuals in Release'
			'put'			'Fixy novych dat'			'Fixes in Release'
			'putexp'		'Ocekavane fixy novych dat'	'Expected Fixes in Release'
			'putsur'		'Neocek. fixy novych dat'	'Unexpected Fixes in Release'
			'ini'			'Pocatecni podminky'		'Initial State'
			'ininew'		'Nove pocatecni podminky'	'New Initial State'
			'iniexpnew'		'Nove ocek. poc. podm.'		'New Expected Initial State'
			'inisurnew'		'Nove neocek. poc. podm.'	'New Unexpected Initial State'
			'iniold'		'Stare pocatecni podminky'	'Old Initial State'
			'iniexpold'		'Stare ocek. poc. podm.'	'Old Expected Initial State'
			'inisurold'		'Stare neocek. poc. podm.'	'Old Unexpected Initial State'
			'iniexp'		'Ocek. pocatecni podminky'	'Expected Initial State'
			'inisur'		'Neocek. pocatecni podm.'	'Unexpected Initial State'
	}; 

[~,index] = ismember(pattern,predefined(:,1));
group_nms = predefined(index,:);

end %- of SUB function ---------------------------------------------------%



function type = prepare_standard_type(intype,options)

type = intype;

if options.pair_fcasts
	type = strrep(type,'expnew','exp');
	type = strrep(type,'expold','exp');
	type = strrep(type,'surnew','sur');
	type = strrep(type,'surold','sur');
end

if options.pair_tunes
	type = strrep(type,'tunesnew','tunes');
	type = strrep(type,'tunesold','tunes');
end

if options.pair_filters
	type = strrep(type,'rev','');
	type = strrep(type,'rls','');
end

if options.pair_expsur
	type = strrep(type,'exp','');
	type = strrep(type,'sur','');
end

end %- of SUB function ---------------------------------------------------%




function pat_group = prepare_standard_groups(type,options)

if options.group_modelchanges
	type_modchange = cellfun(@char,regexp(type,'^model','match'),...
		'UniformOutput',false);
	pat_modchange = setdiff(unique(type_modchange),{''});
else pat_modchange = [];
end

if options.group_tunes
	type_tunes = cellfun(@char,regexp(type,'^tune\S*','match'),...
		'UniformOutput',false);
	pat_tunes = setdiff(unique(type_tunes),{''});
else pat_tunes = [];
end

if options.group_obs
	type_obs = cellfun(@char,regexp(type,'^obs\S*','match'),...
		'UniformOutput',false);
	pat_obs = setdiff(unique(type_obs),{''});
else pat_obs = [];
end

if options.group_fix
	type_fix = cellfun(@char,regexp(type,'^fix\S*','match'),...
		'UniformOutput',false);
	type_put = cellfun(@char,regexp(type,'^put\S*','match'),...
		'UniformOutput',false);
	pat_fix = setdiff(unique(union(type_fix,type_put)),{''});
else pat_fix = [];
end

if options.group_res
	type_res = cellfun(@char,regexp(type,'^res\S*','match'),...
		'UniformOutput',false);
	type_eps = cellfun(@char,regexp(type,'^eps\S*','match'),...
		'UniformOutput',false);
	pat_res = setdiff(unique(union(type_res,type_eps)),{''});
else pat_res = [];
end

pat_group = [pat_modchange; pat_tunes; pat_obs; pat_fix; pat_res];

end %- of SUB function ---------------------------------------------------%




function [group] = fill_standard_groups(pattern,date,type,name,intype)

index = strmatch(pattern,type,'exact');
grouptype = unique(intype(index));
used_datenames = (regexprep(grouptype,'(\S*)','all__$1__all'))';

group = {findloc_datenames(used_datenames,date,intype,name,intype)};

end %- of SUB function ---------------------------------------------------%


