% filters history and returns a database with smoothed estimates
%
% SYNOPSIS
%          dout = filterhistory(m, din, dtunes, p, rng)
% where
%          m is a parsed model
%          d is a historical database containing observed variables
%          dtunes is a database with tunes
%          p is a database with parameter values including std_
%          rng is the history range

function dout = filterhistory(m, d, dtunes, p, rng, varargin)
  
  % merge dtunes to the database d
  d = dbmerge(d, dtunes);
 
  % issue a warning for tunes which are not in the model
  names = get(m, 'ynames');
  tunes = fieldnames(dtunes);
  for i=1:length(tunes)
    tname = tunes{i};
    tmatch = strmatch(tname, names, 'exact');
    ts = dtunes.(tname);
    if (isempty(tmatch) & istseries(ts) & ~isempty(ts))
      warning(['Non-empty tune ' tname ' is found in the database but not ' ...
	       'in the model']);
    end
  end
  
% filter, smooth and return
obs_names = dbnames(d,'nameFilter','^obs_\w*');
obs = dbclip(d*obs_names,rng);
d = d*names;
if nargin<6
	[M,OUTP] = filter(m, d, rng);
else
	[M,OUTP] = filter(m, d, rng, varargin{1});
end

tunes_names = dbnames(OUTP.mean,'nameFilter','^tune_\w*');
OUTP.mean = dbbatch(OUTP.mean,'$0','tseries(rng,nan([length(rng),1]))','nameList',tunes_names);
tunes_names = fieldnames(dtunes);
OUTP.mean = dbbatch(OUTP.mean,'$0','dtunes.$0','nameList',tunes_names);
dout = OUTP;
  
