﻿# -*- coding: utf-8 -*-
"""
Created on Mon Mar 30 16:10:58 2015

@author: U04483
@modified: Feb 10 2020, U6541
"""
#https://www.scribd.com/doc/61671486/50/EViews-COM-Automation-Client-Support-MATLAB-and-R
#http://www.eviews.com/download/whitepapers/EViews_COM_Automation.pdf
#http://timgolden.me.uk/python/win32_how_do_i/generate-a-static-com-proxy.html
#http://win32com.goermezer.de/content/category/7/86/192/
#http://docs.activestate.com/activepython/2.4/pywin32/PyWin32.HTML
#http://pythonexcels.com/python-excel-mini-cookbook/
#http://starship.python.net/~skippy/conferences/tools99/html/ppframe.htm

#seaborn
#http://stanford.edu/~mwaskom/software/seaborn/tutorial/axis_grids.html

#from __future__ import unicode_literals

#http://matplotlib.org/1.4.3/examples/pylab_examples/fonts_demo.html
#https://datasciencelab.wordpress.com/2013/12/21/beautiful-plots-with-pandas-and-matplotlib/
#http://mamut.spseol.cz/matplotlib/
#http://www.pythonic.eu/fjfi/posts/matplotlib.html

import win32com.client, os
import glob

import matplotlib.dates as dts
import matplotlib.ticker as tkr
import matplotlib.pyplot as plt 
import matplotlib.patches as ptch

import matplotlib as mpl

font_path = r'C:\Windows\Fonts\arial.ttf'
#font_path = r'C:\Windows\Fonts\times.ttf'
font_prop = mpl.font_manager.FontProperties(fname=font_path)#, size=14)

#from matplotlib import rcParams
#rcParams['font.family'] = 'Sans-serif'
##rcParams['font.family'] = 'Frutiger'
##rcParams['font.family'] = 'Solpera'
##rcParams['font.sans-serif'] = ['Calibri']
#rcParams['font.sans-serif'] = ['Calibri']
#rcParams['font.sans-serif'] = ['Solpera Book']

#rcParams['font.family'] = 'Arial'
##rcParams['font'] = 'Arial Narrow'
##rcParams['font.family'] = 'Solpera'
##rcParams['font.sans-serif'] = ['Arial']
##rcParams['font.sans-serif'] = ['Solpera Book']

#import matplotlib
#matplotlib.use('PS')
#matplotlib.use( "agg" )
#import matplotlib.font_manager as fm

# zapne LaTex pri interpretaci stringu
#from matplotlib import rc # importování hodnot z matplotlibrc
#rc('font',**{'family':'sans-serif','sans-serif':['Helvetica']})
### pokud se vám zalíbil Palatino nebo jakýkoliv jiný font
##rc('font',**{'family':'serif','serif':['Palatino']))
#rc('text', usetex=True)

#font = {'family' : 'normal',
##        'weight' : 'bold',
##        'size'   : 22}
#
#matplotlib.rc('font', **font)
#http://matplotlib.org/examples/pylab_examples/barchart_demo2.html


#==============================================================================
# priprava na zmenu fontu
#==============================================================================
# Set the font properties (can use more variables for more fonts)
#font_path = 'C:\Windows\Fonts\AGaramondPro-Regular.otf'
#font_prop = font_manager.FontProperties(fname=font_path, size=14)
#
#ax = plt.subplot() # Defines ax variable by creating an empty plot
#
## Define the data to be plotted
#x = np.linspace(0, 10)
#y = x + np.random.normal(x)
#plt.plot(x, y, 'b+', label='Data points')
#
#for label in (ax.get_xticklabels() + ax.get_yticklabels()):
#    label.set_fontproperties(font_prop)
#    label.set_fontsize(13) # Size here overrides font_prop
#
#plt.title("Exponentially decaying oscillations", fontproperties=font_prop,
#          size=16, verticalalignment='bottom') # Size here overrides font_prop
#plt.xlabel("Time", fontproperties=font_prop)
#plt.ylabel("Amplitude", fontproperties=font_prop)
#plt.legend(loc='lower right', prop=font_prop) # NB different 'prop' argument for legend
#plt.text(0, 0, "Misc text", fontproperties=font_prop)
#plt.show()
    
import numpy as np
import datetime
import time


def cm2inch(*tupl):
    inch = 2.54
    if isinstance(tupl[0], tuple):
        return tuple(i/inch for i in tupl[0])
    else:
        return tuple(i/inch for i in tupl)

# formating dates
#http://matplotlib.org/api/dates_api.html
def my_format_function_monthly(x, pos=None):
    x = dts.num2date(x)
    if (pos == 0) or (x.month==1):
        fmt = '%m/%y'
    else:
        fmt = '%m'
    label = x.strftime(fmt)
    #label = label.rstrip("0")
    label = label.rstrip(".")
    label = label.lstrip("0")
    label = label.lstrip(".")    
    return label

def my_format_function_quaterly(x, pos=None):
    x = dts.num2date(x)
    if (x.month>0) and (x.month<=3):
        fmt = 'I/%y'
    elif (x.month>3) and (x.month<=6):
        if pos == 0:
            fmt = 'II/%y'
        else:
            fmt = 'II'            
    elif (x.month>6) and (x.month<=9):
        if pos == 0:
            fmt = 'III/%y'
        else:
            fmt = 'III'
    elif (x.month>9) and (x.month<=12):
        if pos == 0:
            fmt = 'IV/%y'
        else:
            fmt = 'IV' 
            
    label = x.strftime(fmt)
    #label = label.rstrip("0")
    label = label.rstrip(".")
    label = label.lstrip("0")
    label = label.lstrip(".")    
    return label    
    
def my_format_function_yearly(x, pos=None):
    x = dts.num2date(x)
    fmt = '%Y'
    label = x.strftime(fmt)
    #label = label.rstrip("0")
    label = label.rstrip(".")
    label = label.lstrip("0")
    label = label.lstrip(".")    
    return label
    
    
def choose_color_scheme(scheme='cnbmanual_2020',labels=[],barlabels=[],pointlabels=[],stackedlinelabels=[]):

    #shaded_area = (237/255.,237/255.,237/255.)
    shaded_area = (220/255.,220/255.,220/255.)#(227/255.,227/255.,227/255.)
    black = (0,0,0)
    nearlyblack = (50/255.,50/255.,50/255.)
    darkgrey = (160/255.,160/255.,160/255.)

#    https://jiffyclub.github.io/palettable/#cookbook
    # These are the "Tableau 20" colors as RGB.  
    # http://tableaufriction.blogspot.ro/2012/11/finally-you-can-use-tableau-data-colors.html  
    #http://blogs.sas.com/content/iml/2014/10/01/colors-for-heat-maps.html  
    #http://colorbrewer2.org/

    tableau10 = [(31, 119, 180), (255, 127, 14),
                 (44, 160, 44), (214, 39, 40), 
                 (148, 103, 189), (140, 86, 75),
                 (227, 119, 194), (127, 127, 127), 
                 (188, 189, 34), (23, 190, 207)]

    tableau10light = [(174, 199, 232), (255, 187, 120),  
                 (152, 223, 138), (255, 152, 150),  
                 (197, 176, 213), (196, 156, 148),  
                 (247, 182, 210), (199, 199, 199),  
                 (219, 219, 141), (158, 218, 229)]

    tableau10medium = [(114, 158, 206), (255, 158, 74),  
                 (103, 191, 92), (237, 102, 93),  
                 (173, 139, 201), (168, 120, 110),  
                 (237, 151, 202), (162, 162, 162),  
                 (205, 204, 93), (109, 204, 93)]

    tableau10colorblind = [(0, 107, 164), (255, 128, 14),  
                 (171, 171, 171), (89, 89, 89),  
                 (95, 158, 209), (200, 82, 0),
                 (137, 137, 137), (162, 200, 236), 
                 (255, 188, 121), (207, 207, 207)]
    
    tableau5gray = [(96, 99, 106), (165, 172, 175),  
                 (65, 68, 81), (143, 135, 130),  
                 (207, 207, 207)]
    
    tableau9trafficlight = [(177, 3, 24), (219, 161, 58),  
                 (48, 147, 67), (216, 37, 38),  
                 (255, 193, 86), (105, 183, 100),  
                 (242, 108, 100), (255, 221, 113),  
                 (159, 205, 153)]
    
    tableau6purplegray = [(123, 102, 210), (220, 95, 189),  
                 (148, 145, 123), (153, 86, 136),  
                 (208, 152, 238), (215, 213, 197)]

    tableau12purplegray = [(123, 102, 210), (166, 153, 232),  
                 (220, 95, 189), (255, 192, 218),  
                 (95, 90, 65), (180, 177, 155),  
                 (153, 86, 136), (216, 152, 186),  
                 (171, 106, 213), (208, 152, 238),
                 (139, 124, 110), (219, 212, 197)]
    
    tableau6bluered = [(44, 105, 176), (240, 39, 32),  
                 (172, 97, 60), (107, 163, 214),  
                 (234, 107, 115), (233, 195, 155)]
    
    tableau12bluered = [(44, 105, 176), (181, 200, 226),  
                 (240, 39, 32), (255, 182, 176),  
                 (172, 97, 60), (233, 195, 155),  
                 (107, 163, 214), (181, 223, 253),  
                 (172, 135, 99), (221, 201, 180),  
                 (189, 10, 54), (244, 155, 122)]
    
    tableau6greenorange = [(50, 162, 81), (255, 127, 15),  
                 (60, 183, 204), (255, 217, 74),  
                 (57, 115, 124), (184, 90, 13)]
    
    tableau12greenorange = [(50, 162, 81), (172, 217, 141),  
                 (255, 127, 15), (255, 185, 119),  
                 (60, 183, 204), (152, 217, 228),  
                 (184, 90, 13), (255, 217, 74),  
                 (57, 115, 124), (133, 180, 169),  
                 (130, 133, 59), (204, 201, 77)]
        
    tableau20 = [(31, 119, 180), (174, 199, 232), (255, 127, 14), (255, 187, 120),  
                 (44, 160, 44), (152, 223, 138), (214, 39, 40), (255, 152, 150),  
                 (148, 103, 189), (197, 176, 213), (140, 86, 75), (196, 156, 148),  
                 (227, 119, 194), (247, 182, 210), (127, 127, 127), (199, 199, 199),  
                 (188, 189, 34), (219, 219, 141), (23, 190, 207), (158, 218, 229)]
                 
    cnb = [(255, 0, 0),(255, 204, 0),(0, 0, 255),(0, 128, 0),(0, 0, 0),(150, 150, 150),
           (153, 51, 0),(204, 204, 255)]+tableau20
    
    cnbtableau = [(214, 39, 40),(31, 119, 180),(255, 127, 14),(44, 160, 44),(65, 68, 81),(165, 172, 175),
                  (188, 189, 34),(174, 199, 232)]+tableau20

    cnbmanual = [(214, 39, 40),(26,33,85),(247, 97, 86),   #ytmavecervena, tmavemodra, zarive cervena
                 (36,38,169),(157,171,226),   #zaklad fialovomodra, svetlemodra
                 (108,111,112),     #zaklad tmava seda
                 (181,182,179),(206,207,203)] #svetle a svetlesvetle seda]
                 
    cnbmanual_cs = [(214, 39, 40),(26,33,85),(157,171,226),   #tmavemodra, tmavecervena, svetlemodra
                 (36,38,169),(213,43,30),   #zaklad fialovomodra, zarive cervena
                 (108,111,112),     #zaklad tmava seda
                 (181,182,179),(206,207,203)] #svetle a svetlesvetle seda]                 
                  
    cnbmanual_old = [(0,63,124),(95,155,200),(0,52,105),(0,204,153),
                 (170,226,202),(255,51,0),(111,111,111),(85,140,181)]
                 
#    cnbpdf = [(233,94,63),(62,133,193),(255,220,0),(30,161,45),(177,178,180),
#              (94,197,236),(147,52,136)]
                 
    cnbpdf = [(241,77,62),(75,130,203),(253,220,3),(50,181,63),(167,165,165),
              (35,31,32),(101,205,245),(175,219,40),(245,134,18),(139,43,153)]                 
    # cervena, modra, zluta, zelena, sediva
    # cerna, svetlemodra, svetlezelena, oranzova, fialova
    
    # zmena poradi barev na prvnich dvou mistech (kvuli stara - tmave modra vs nova - svetle modra)
    cnbmanual_2020_orig = [(36, 38, 169),(213,43,30),(255, 187, 0), # tmavomodra, cervena, zlta 
                 (154,205,50),(0,206,209),                          # svetlozelena, tyrkysova
                 (108,111,112),                                     # zaklad tmava seda
                 (138,43,226),(157,171,226),                        # fialova svetlemodra,
                 (255,99,71), (46,167,84)]                          # svetlocervena, tmavozelena 

    cnbmanual_2020 = [(36, 38, 169),(157,171,226),(213,43,30),(255, 187, 0),   #tmavomodra,svetlemodra, cervena, zlta 
                 (154,205,50),(0,206,209),   #svetlozelena, tyrkysova
                 (108,111,112),     #zaklad tmava seda
                 (138,43,226), #fialova
                 (255,99,71), (46,167,84)]   # svetlocervena, tmavozelena 
        
    cnbmanual_2020_CS = [(157,171,226),(36, 38, 169),(213,43,30),(255, 187, 0),   #tmavomodra,svetlemodra, cervena, zlta 
                 (154,205,50),(0,206,209),   #svetlozelena, tyrkysova
                 (108,111,112),     #zaklad tmava seda
                 (138,43,226), #fialova
                 (255,99,71), (46,167,84)]   # svetlocervena, tmavozelena                      

    if scheme == 'cnb' or scheme == 'cnbpdf':
        fontcolors = black
    elif scheme == 'cnbmanual':    
        fontcolors = (26/255.,33/255.,85/255.)
        darkgrey = (108/255.,111/255.,112/255.)
    elif scheme == 'cnbmanual_2020':    
        fontcolors = black
        darkgrey = (0/255.,0/255.,0/255.)
        shaded_area = (233/255.,233/255.,233/255.)#(220/255.,220/255.,220/255.)#
    elif scheme == 'cnbmanual_2020_CS':    
        fontcolors = black
        darkgrey = (0/255.,0/255.,0/255.)
        shaded_area = (233/255.,233/255.,233/255.)#(220/255.,220/255.,220/255.)#        
    else:
        fontcolors = black
        darkgrey = (0/255.,0/255.,0/255.)

        
    try:
        if len(barlabels)==0:
            linecolors = eval(scheme + '[:len(labels)][::-1]')
            barcolors = []
            pointcolors = eval(scheme + '[:len(pointlabels)]') 
            stackedlinecolors = eval(scheme + '[:len(stackedlinelabels)]') 
        elif len(labels)==0:
            linecolors = []
            barcolors = eval(scheme + '[:len(barlabels)]')
            pointcolors = eval(scheme + '[:len(pointlabels)]')
            stackedlinecolors = eval(scheme + '[:len(stackedlinelabels)]')
        elif len(labels)==1:
            linecolors = [(0,0,0)]
            barcolors = eval(scheme + '[:len(barlabels)]')
            pointcolors = eval(scheme + '[:len(pointlabels)]')
            stackedlinecolors = eval(scheme + '[:len(stackedlinelabels)]')
        else:
            if scheme == 'tableau10':
                linecolors = tableau10[:len(labels)][::-1]
                barcolors = tableau10light[:len(barlabels)] 
                pointcolors = tableau10[:len(pointlabels)]
                stackedlinecolors = tableau10[:len(stackedlinelabels)]
            elif scheme == 'cnb' or scheme == 'cnbpdf':
                linecolors = eval(scheme + '[:len(labels)][::-1]') 
                if len(labels)>0:
                    sumlines = ([(0, 0, 0)]+cnbpdf)[:len(labels)][::-1]
                else:
                    sumlines = [(0, 0, 0)]    
                pointcolors = eval(scheme + '[:len(pointlabels)]')
                stackedlinecolors = eval(scheme + '[:len(stackedlinelabels)]')
                linecolors.extend(sumlines)
                linecolors = linecolors[-len(labels):]
                barcolors = eval(scheme + '[:len(barlabels)]')
            elif scheme == 'cnbmanual_2020':
                pointcolors = eval(scheme + '[:len(pointlabels)]')
                stackedlinecolors = eval(scheme + '[:len(stackedlinelabels)]')
                linecolors = eval(scheme + '[:len(labels)][::-1]') 
                sumlines = [(157,171,226),(36, 38, 169)]
                linecolors.extend(sumlines)
                linecolors = linecolors[-len(labels):]
                barcolors = eval(scheme + '[:len(barlabels)]')
            elif scheme == 'cnbmanual_2020_CS':
                pointcolors = eval(scheme + '[:len(pointlabels)]')
                stackedlinecolors = eval(scheme + '[:len(stackedlinelabels)]')
                linecolors = eval(scheme + '[:len(labels)][::-1]') 
                sumlines = [(157,171,226),(36, 38, 169)]
                linecolors.extend(sumlines)
                linecolors = linecolors[-len(labels):]
                barcolors = eval(scheme + '[:len(barlabels)]')                
            else:
                linecolors = eval(scheme + '[:len(labels)][::-1]') 
#                sumlines = [(65*.8, 68*.8, 81*.8),(189, 10, 54)][::-1]
                sumlines = [(65*.8, 68*.8, 81*.8),(214, 39, 40)][::-1]
                pointcolors = eval(scheme + '[:len(pointlabels)]')
                stackedlinecolors = eval(scheme + '[:len(stackedlinelabels)]')
                linecolors.extend(sumlines)
                linecolors = linecolors[-len(labels):]
#                print linecolors
                barcolors = eval(scheme + '[:len(barlabels)]')
    except:
        if len(barlabels)==0:
            linecolors = tableau20[:len(labels)][::-1]    
            barcolors = []
            pointcolors = tableau20[:len(pointlabels)]
            stackedlinecolors = tableau20[:len(stackedlinelabels)]
        elif len(labels)==0:
            linecolors = []
            barcolors = tableau20[:len(barlabels)]
            pointcolors = tableau20[:len(pointlabels)]
            stackedlinecolors = tableau20[:len(stackedlinelabels)]
        else:
            linecolors = tableau20[:2*len(labels)][::-1][1::2]
            barcolors = tableau20[:2*len(barlabels)][1::2] 
            pointcolors = tableau20[:len(pointlabels)]
            stackedlinecolors = tableau20[:len(stackedlinelabels)]
            
    # Scale the RGB values to the [0, 1] range, which is the format matplotlib accepts.  
    for i in range(len(linecolors)):  
        r, g, b = linecolors[i]  
        linecolors[i] = (r / 255., g / 255., b / 255.) 
        
    for i in range(len(barcolors)):  
        r, g, b = barcolors[i]  
        barcolors[i] = (r / 255., g / 255., b / 255.)   

    for i in range(len(pointcolors)):  
        r, g, b = pointcolors[i]  
        pointcolors[i] = (r / 255., g / 255., b / 255.)  

    for i in range(len(stackedlinecolors)):  
        r, g, b = stackedlinecolors[i]  
        stackedlinecolors[i] = (r / 255., g / 255., b / 255.)            
            
    return linecolors,barcolors,pointcolors,stackedlinecolors,shaded_area,fontcolors,darkgrey


## scatter plot - function plotS
def plots(data, pointsX = 'all', pointsY = 'all', ax = None, figsize = (8.5,6), 
          figtitle = '', titleX = '', titleY = '', figsubtitle = '', figfooter = '',
          xlabels = [], ylabels = [], xticks_number = None, yticks_number = None,
          ylim_min = None, ylim_max = None, xlim_min = None, xlim_max = None,
          colorscheme = 'cnbmanual_2020_orig', fontColor=[],
          pointcolors = [], pointtype = ['o'], pointsize = [170], pointlabels = [],
          figtitleFontSize = 17, figfooterFontSize = 14, ticksFontSize=14,
          gridlines = 'both',
          ticksDecPoints=0, ticksDecSepCS=True, pct = False,
          legend = 'upper center', legpushdown = 0, legpos = None, legcols = 3,
          font_prop = font_prop):
    '''
    plotsc!
    data - pandas dataframe s daty pro vykresleni
    pointsX = 'all' - soubor rad, ktery obsahuje x-ovou hodnotu (napr. ['tseries1_xVal','tseries2_xVal','tseries3_xVal'])
    pointsY = 'all' - soubor rad, ktery obsahuje y-ovou hodnotu (napr. ['tseries1_yVal','tseries2_yVal','tseries3_yVal'])
                       body v grafu jsou pak tvoreny pary ('tseries1_xVal','tseries1_yVal'), ('tseries2_xVal','tseries2_yVal') apod.
    ax - slouzi pro vytvoreni ax objektu, pomoci ktereho lze vytvorit a odkazovat se na podgrafy
    figsize - velikost grafu (default nastaven na prezentaci 16:9 (8.5,6) - velikost obrazku pro rozlozeni "obrazek vlevo, text vpravo")
    figtitle - nazev grafu
    titleX - nazev osy x
    title Y - nazev osy y
    figsubtitle - podnazev grafu
    figfooter - poznamka pod graf (TBD)
    xlabels - oznaceni na ose x, napr. [-2,1,0,1]
    ylabels - oznaceni na ose y
    xticks_number - pocet ticku na ose x
    yticks_number - pocet ticku na ose y
    ylim_min, ylim_max, xlim_min, xlim_max - rozsahy os
    colorscheme - barevne schema (moznoti viz vyse)
    fontColor - barva fontu
    pointcolors - barva bodu
    pointtype - tvar bodu (default nastaven na kolecko), o - circle, v - triangle down, ^ - triangle up, s - square, p - pentagon, p - plus (filled), h - hexagon, D - diamond, . - point
    pointsize - velikost bodu
    pointlabels - oznaceni bodu pro legendu
    figtitleFontSize - velikost pisma nazvu grafu
    figfooterFontSize - velikost pisma poznamky
    ticksFontSize - velikost pisma ticku
    gridlines - mrizka grafu (volby vertical, horizontal a both)
    ticksDecPoints - pocet mist za desetinnou carkou
    ticksDecSepCS - desetinna carka (True - carka, False - tecka)
    pct - oznaceni procent za cisla
    legend - umisteni legendy
    legpusdown - vertikalni posunuti legendy z jeji vychozi pozice
    legpos - moznost nastaveni pozice legendy, prepise legpushdown, napr. (-0.1,0.1)
    legcols - pocet sloupce legendy
    font_prop - nastaveni fontu
    '''

    if ax is None:
        f, ax = plt.subplots()
        f.set_size_inches(figsize)
    else:
        f = None

    # Drop rows with NaN 
    data = data.dropna()
    
    # If dimensions columnsX and columnsY do not match, trimm columns accordingly
    if len(pointsX) != len(pointsY):
       trim_dim = min(len(pointsX), len(pointsY)) 
       pointsX = pointsX[:trim_dim]
       pointsY = pointsY[:trim_dim]
    
    # Handle pointlabels, point types, pointsize, point colors, font color
    if len(pointlabels)<len(pointsX):
        pointlabels = pointsX    
              
    if len(pointtype)<len(pointsX):
        pointtype = [pointtype[0]]*(len(pointsX)) 
        
    if len(pointsize)<len(pointsX):
        pointsize = [pointsize[0]]*(len(pointsX))
        
    if len(fontColor)==0:
        fontColor = [(0,0,0)]
    
    _, _, pointcolors_pom, _, _, sumlinecolor_pom, _ = choose_color_scheme(colorscheme,[],[],pointlabels,[])    

    if len(pointcolors)<len(pointsX):
        pointcolors = pointcolors_pom
    
    # x and y limits - flags    
    if (ylim_min is None) and (not ylabels):
        guess_ylim_min = True
    else:
        guess_ylim_min = False
    if (ylim_max is None) and (not ylabels):
        guess_ylim_max = True
    else:
        guess_ylim_max = False
    if (xlim_min is None) and (not xlabels):
        guess_xlim_min = True
    else:
        guess_xlim_min = False
    if (xlim_max is None) and (not xlabels):
        guess_xlim_max = True
    else:
        guess_xlim_max = False

    # Set x and y labels
    if len(ylabels)>0:
        ax.set_yticks(ylabels)
        ax.set_yticklabels([str(y) for y in ylabels])  
    elif not yticks_number is None:
        ax.yaxis.set_major_locator(plt.MaxNLocator(yticks_number-1))
    else:
        ax.yaxis.set_major_locator(plt.MaxNLocator(9,steps=[1,2,5,10]))
    
    if len(xlabels)>0:
        ax.set_xticks(xlabels)
        ax.set_xticklabels([str(x) for x in xlabels])  
    elif not xticks_number is None:
        ax.xaxis.set_major_locator(plt.MaxNLocator(xticks_number-1))
    else:
        ax.xaxis.set_major_locator(plt.MaxNLocator(9,steps=[1,2,5,10]))

    # Create basic scatter plot
    zorder = 30
    rank = 0
    
    for columnX, columnY in zip(pointsX, pointsY):
        data_x = np.array(data[columnX].values)
        data_y = np.array(data[columnY].values)
        data_x = data_x.astype(float)
        data_y = data_y.astype(float)
        data_x = data_x[~np.isnan(data_y)]
        data_y = data_y[~np.isnan(data_y)]
    
        ax.scatter(data_x, data_y, s=pointsize[rank], color = pointcolors[rank], lw = 0.1, edgecolors='white', marker = pointtype[rank], label = pointlabels[rank],zorder=zorder)        
        zorder += 1
        rank += 1

    # Handling limits on axes if hard limits set on (ylim_min, ylim_max, xlim_min, xlim_max, ylabels, xlabels)
    if not guess_ylim_min:
        if (ylim_min is not None) and (ylabels != []):
            ylim_min = min(ylabels)
        elif ylim_min is None:
            ylim_min = min(ylabels)
        elif not ylabels:
            ylim_min = ylim_min
    if not guess_ylim_max:
        if (ylim_max is not None) and (ylabels != []):
            ylim_max = max(ylabels)
        elif ylim_max is None:
            ylim_max = max(ylabels)
        elif not ylabels:
            ylim_max = ylim_max
    if not guess_xlim_min:
        if (xlim_min is not None) and (xlabels != []):
            xlim_min = min(xlabels)
        elif xlim_min is None:
            xlim_min = min(xlabels)
        elif not xlabels:
            xlim_min = xlim_min
    if not guess_xlim_max:
        if (xlim_max is not None) and (xlabels != []):
            xlim_max = max(xlabels)
        elif xlim_max is None:
            xlim_max = max(xlabels)
        elif not ylabels:
            xlim_max = xlim_max
       
    ax.set_ylim(ylim_min, ylim_max)  
    ax.set_xlim(xlim_min, xlim_max)

    # Spines - set color, set width            
    ax.spines["top"].set_color((210/255.,210/255.,210/255.))
    ax.spines["top"].set_linewidth(0.5)
    ax.spines["bottom"].set_color((210/255.,210/255.,210/255.))
    ax.spines["bottom"].set_linewidth(0.5)
    ax.spines["right"].set_color((210/255.,210/255.,210/255.))
    ax.spines["right"].set_linewidth(0.5)
    ax.spines["left"].set_color((210/255.,210/255.,210/255.))
    ax.spines["left"].set_linewidth(0.5)

    # Retrieve major ticks and mix, max values
    yticks_major = ax.yaxis.get_majorticklocs()
    xticks_major = ax.xaxis.get_majorticklocs() 
    ylim_min = yticks_major[0]
    ylim_max = yticks_major[-1]
    xlim_min = xticks_major[0]
    xlim_max = xticks_major[-1]
    
    zorder = 2 

    # Gridlines
    if gridlines == 'horizontal' or gridlines == 'both':
        for y in yticks_major:
            if y!=0:
                ax.plot([xlim_min, xlim_max], [y] * len([xlim_min, xlim_max]), "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder) 
        zorder += 1

    if gridlines == 'vertical' or gridlines == 'both':
        for x in xticks_major:
            if x!=0:
                ax.plot([x] * len([ylim_min, ylim_max]),[ylim_min, ylim_max], "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder) 
        zorder += 1
        
    # Gridlines - main grids going through the origin
    if 0 in yticks_major:
        ax.plot([xlim_min, xlim_max], [0] * len([xlim_min, xlim_max]), "-", lw=0.75, color="black", alpha=1,zorder=zorder)    
        zorder += 1
    if 0 in xticks_major: 
        ax.plot([0] * len([ylim_min, ylim_max]), [ylim_min, ylim_max], "-", lw=0.75, color="black", alpha=1,zorder=zorder)    
        zorder += 1

    # Ticks
    ax.tick_params(axis="x", which="both", bottom="on", top="off", left="off", right="off",
                   labelbottom="on", labelleft="on", labelsize=ticksFontSize, pad = 5+ticksFontSize/14., labelcolor = 'k')

    ax.tick_params(axis="y", which="both", bottom="on", top="off",left="on", right="off",
                   labelbottom="on", labelleft="on", labelsize=ticksFontSize, pad = 5+ticksFontSize/14., labelcolor = 'k')


    onetick = round(yticks_major[1]-yticks_major[0],10)
    ticksDecPoints_cur = 0
    while True:
        n = onetick*10**ticksDecPoints_cur % 1
        if round(n,20) == 0:
            break
        ticksDecPoints_cur += 1
    
#http://stackoverflow.com/questions/2389846/python-decimals-format 
    if ticksDecPoints>ticksDecPoints_cur:
        ticksDecPoints_cur=ticksDecPoints
    
    # Pct sign
    if pct:
        format_string = '%%0.%df %%%% ' %ticksDecPoints_cur
    else:
        format_string = '%%0.%df' %ticksDecPoints_cur    
    ax.set_yticks(yticks_major)
    ax.set_xticks(xticks_major)
    yticks_major_labels = [(format_string %x) for x in yticks_major]
    xticks_major_labels = [(format_string %x) for x in xticks_major]
    
    # Digit separator - dot vs comma
    if ticksDecSepCS is True:
        yticks_major_labels = [y.replace('.',',') for y in yticks_major_labels]
        xticks_major_labels = [x.replace('.',',') for x in xticks_major_labels] 
    ax.set_yticklabels(yticks_major_labels)
    ax.set_xticklabels(xticks_major_labels)

    # Fonts - x-axis nad y-axis labels,
    for label in ax.get_xticklabels():
        font_prop.set_size(ticksFontSize) #ticksFontSize
        label.set_fontproperties(font_prop)
    
    for label in ax.get_yticklabels():
        label.set_fontproperties(font_prop) 
        
    # Axes titles
    if titleX != '':
        plt.xlabel(titleX, fontsize=ticksFontSize, fontproperties=font_prop, labelpad = 6+ticksFontSize/14.)
    if titleY != '':
        plt.ylabel(titleY, fontsize=ticksFontSize, fontproperties=font_prop, labelpad = 6+ticksFontSize/14.)
    
    
    # Fig title
    if len(figtitle)>0:
        if len(figsubtitle)>0:
            font_prop.set_size(figtitleFontSize)
            ax.text(xlim_min+(xlim_max-xlim_min)/2, ylim_max+(ylim_max-ylim_min)*0.055*(figtitleFontSize/17.), 
                    figtitle, fontsize=figtitleFontSize, ha="center", color = 'k', fontproperties=font_prop)  
            font_prop.set_size(round(figtitleFontSize*.8235))            
            ax.text(xlim_min+(xlim_max-xlim_min)/2, ylim_max+(ylim_max-ylim_min)*0.01*(figtitleFontSize/17.), 
                    figsubtitle, fontsize=round(figtitleFontSize*.8235), ha="center", color = 'k', fontproperties=font_prop)  
        else:
            font_prop.set_size(figtitleFontSize)
            ax.text(xlim_min+(xlim_max-xlim_min)/2, ylim_max+(ylim_max-ylim_min)*0.03, figtitle, 
                    fontsize=figtitleFontSize, ha="center", color = 'k', fontproperties=font_prop)  

    # Legend
    if legpos is None:
        bbox_to_anchor=(0.5, -0.15-(ticksFontSize-14)*.006 + legpushdown)
    else:
        bbox_to_anchor = legpos    

    while '' in pointlabels:
        pointlabels.remove('')    
    if len(legend)>0:
        legend=legend
#        'best'         : 0, (only implemented for axis legends)
#        'upper right'  : 1,
#        'upper left'   : 2,
#        'lower left'   : 3,
#        'lower right'  : 4,
#        'right'        : 5,
#        'center left'  : 6,
#        'center right' : 7,
#        'lower center' : 8,
#        'upper center' : 9,
#        'center'       : 10,
        if len(pointlabels)>0:
            leghandles, leglabels = ax.get_legend_handles_labels()
            leglabels = leglabels[:len(pointlabels)]
            leghandles = leghandles[:len(pointlabels)]
        
        font_prop.set_size(ticksFontSize)
        
        legend = ax.legend(leghandles, leglabels, fontsize=ticksFontSize,
                           loc = legend,borderaxespad=0.05,
                           bbox_to_anchor=bbox_to_anchor,
                           ncol=legcols, prop=font_prop,
                           handleheight=2, labelspacing=0.07)

        for txt in legend.get_texts():
            txt.set_y(2) # y-position 
#            txt.set_ha("center") # horizontal alignment of text item
#            txt.set_x(-5) # x-position
                         
        frame = legend.get_frame()
        frame.set_color('none')
       
    return f, ax   
        

## plot time series - function plotG
def plotg(data,start = None, end = None,
          xlabels = [],xticks_rotation=0,xticks_all = False,
          xaxisindent = False,
          xdatetime = True, whiteline = False,
          ylabels = [], yticks_number = None,
          ylabels2axis = [],
          columns = 'all',labels=[],linecolors = [],linemarker = None, linemarkersize = 3.5,linesty=[],
          fill_below_lines = False,
          bars = [],bars2 = [],ntdelta = 30,ehistdelta=90,legend2=0,barcolors2=[],
          barlabels = [],barcolors=[],sortbars = False,bars_stacked = True,widthbar = 0,
          diffbars = [], diff2axis = False,switchlineorder=False,
          columns2axis = [],labels2axis = [],bars2axis = [],barlabels2axis = [],
          points = [],pointlabels = [],pointcolors=[],pointtype = ['D'],pointsize = [20],
          points2axis = [],pointlabels2axis = [],
          stackedlines = [],stackedlinelabels = [],stackedlinecolors = [],
          fillbetween = [],fillbetweenlabels = [],fillbetweencolors = [],
          datafreq = None,
          figsize = (8.5,6),
          ylim_min = None,ylim_max = None, xlim_min = None, xlim_max = None, ylim_hard = False,
          ylim2nd_min = None,ylim2nd_max = None,
          colorscheme = 'cnbmanual_2020',
          figtitle = '',figsubtitle = '', figfooter = '',
          figtitleFontSize = 17,figfooterFontSize = 14,fontColor=[],
          pct = False, ticksFontSize=14, ticksDecPoints=0, ticksDecSepCS=True, legend = 'best',legpushdown = 0, legpos = None,
          ehist = None,ehistjustticks=False,ehist2=None,
          gridlines = 'horizontal',
          diffbarslabel = u'Rozdíl (pravá osa)', diffbarslabel_short = u'Rozdíl', legcols = 3,
          shaded_area=None,sumlinecolor=None,diffbarscolor=None,
          ax = None,
          font_prop = font_prop,
          graphSty_newRls = False):    
    '''
    plotg!
    data - pandas dataframe, ktery musi mit i spravne nastavenou casovou osu
    start = None, end = None, zacatek a konec dat - string pr "2012-02"
    xlabels = {'2013':u'první rok', '2016':u'druhý rok'} volitelne labels 
    xticks_rotation = in angles rotation of xcustom labels
    xticks_all = False - ticks na x ose budou jen nektere, podle frekvence, True = kazdy mesic/ctvrleti/rok bude mit tick
    xdatetime = True - standard - xdata musi byt v numpy.datetime64 pri false se pouzije float
    ylabels = [1,1.1,1.2,1.3] volitelne labels, ylabels2axis = [0.1,0.3,0.5,0.7]   
    yticks_number = maximalni pocet ticku na y ose - nepouzije se v pripade ze sou fixne zadane yticks
    columns = 'all',labels=[],linecolors = [], - seznam car (vsechny z data) labely a barvy volitelne 
    linemarker = None, linemarkersize = 3.5, - k caram je mozne pridat markery - i velikost
    fill_below_lines = False - normalni cara, pokud true, vyplni prosto mezi carou a x-ovou osou
    bars = [],barlabels = [],barcolors=[], - seznam baru, labelu a barvy volitelne
    sortbars = False - zda ma bary radit dle velikosti
    bars_stacked = True - zda maji byt bary skladane na sebe nebo vedle sebe
    diffbars = [], diff2axis = False, rozdilove bary - zadava se ciselny indez z bar
    diffbarslabel_short = u'Rozdíl' - legenda rozdilovych baru pro diff2axis False
    diffbarslabel = u'Rozdíl (pravá osa)' - legenda rozdilovych baru pro diff2axis True
    columns2axis = [],labels2axis = [] - cary na druhe ose
    bars2axis = [],barlabels2axis = [] - bary na druhe ose
    ntdelta - horizontalni posun mezi bars a bars2
    ehistdelta - bars2 ehist shifter
    legend2 - display legend for bars2
    points = [], - sloupec z dataframe pro vykresleni bodu (scatterplot)
    pointlabels = [], - labely bodu scatterplootu
    pointcolors=[], - volitelne barvy bodu, jinak jede stejne jako lines 
    pointtype = ['D'], - typ markeru scatter o - kruh D - diamomnd - viz marker matplotlib
    pointsize = [20], - velikost bodu scatterplotu
    datafreq = None, - frekvence dat zatim by se asi nemela zadavat - veme si z dat
    figsize = (10,5.25), - velikost grafu
    ylim_min = None,ylim_max = None, y-osa minimum maximum, jinak si vyhleda samo
    xlim_min = None, xlim_max = None, x-osa minimum maximum, jinak si vyhleda samo
    ylim_hard = False, když True drží zadané hodnoty ylim_min, ylim_max  
    ylim2nd_min = None,ylim2nd_max = None, y druha osa minimum maximum, jinak si vyhleda samo
    colorscheme = 'tableau20', - vyber colorscheme 
            moznosti(tableau10, tableau10light, tableau10medium,
                     tableau10colorblind, tableau5gray, tableau9trafficlight,
                     tableau6purplegray, tableau12purplegray, tableau6bluered,
                     tableau12bluered, tableau6greenorange, tableau12greenorange,
                     tableau20, cnb, cnbtableau, cnbmanual, cnbmanual_2020
    figtitle = '',figsubtitle = '', figfooter = '' - nazev podnazev a footer tabulky
    figtitleFontSize ,figfooterFontSize - nastaveni velikosti pisma Titlu (subtitle 0.8235) a footeru
    pct = False - zda se u y osy zobrazi znak procent fi ne
    fontColor=[0.1,0.1,0.1] - barva pisma v grafu
    ticksFontSize=figfooterFontSize, ticksDecPoints=0, ticksDecSepCS=True - velikost ticku a zda budou mit desetinou carku a desetinnou carsku nebo tecku
    legend = 'best' - pozice legendy,
    legpushdown = posunuti legendy zaporne cislo dolu, kladne nahoru - napr. -0.13
    legpos = moznost nastaveni pozice legendy, overridne legpushdown
    legcols = 3 - pocet sloupcu legendy
    ehist = None - sedive zvyrazneni budoucnosti - zadava se jako string datum v ceskem formatu "14.5.2015"
    gridlines = both - vykresleni horizontalnich a vertikalnich gridu, vetical - pouze vertikalni, horizontal - pouze horizontalni, None - bez gridu
    ax = muze se dat link na subploty, pak vytvori pouze urcitej subplot...
    linesty = line styles as strings, if empty then solid line
    graphSty_newRls = styl grafu srovnani prognozy a novych dat (vzor grafu mala SZ) - kombinace tenke a normalni cary
    '''
    
#    prop = fm.FontProperties(fname='Arial.ttf')
    if len(bars2) == 0:
        if columns=='all':
            columns = data.columns.values[::-1]
        else:
            columns = columns[::-1]
        
    if bars=='all':
        bars = data.columns.values      
        
    if len(labels)<len(columns):
        labels = columns    
    else:
        labels = labels[::-1]

    if len(barlabels)<len(bars):
        barlabels = bars
        
    if len(labels2axis)<len(columns2axis):
        labels2axis = columns2axis

    if len(barlabels2axis)<len(bars2axis):
        barlabels2axis = bars2axis     
              
    if len(pointlabels)<len(points):
        pointlabels = points  

    if len(pointlabels2axis)<len(points2axis):
        pointlabels2axis = points2axis         
              
    if len(pointtype)<(len(points)+len(points2axis)):
        pointtype = [pointtype[0]]*(len(points)+len(points2axis)) 
        
    if len(pointsize)<(len(points)+len(points2axis)):
        pointsize = [pointsize[0]]*(len(points)+len(points2axis)) 
        
    if len(stackedlinelabels)<len(stackedlines):
        stackedlinelabels = stackedlines
        
    linecolors_pom,barcolors_pom,pointcolors_pom,stackedlinecolors_pom, shaded_area_pom,sumlinecolor_pom,diffbarscolor_pom = choose_color_scheme(colorscheme,labels+labels2axis,barlabels+barlabels2axis,pointlabels+pointlabels2axis,stackedlinelabels)    
    if shaded_area is None:
        shaded_area = shaded_area_pom
    if sumlinecolor is None:
        sumlinecolor = sumlinecolor_pom
    if diffbarscolor is None:
        diffbarscolor = diffbarscolor_pom
   
    if len(linecolors)==0:
        linecolors = linecolors_pom
    else:
        linecolors = linecolors[:(len(columns)+len(columns2axis))]
        linecolors = linecolors[::-1]    
    if len(barcolors)==0:
        barcolors = barcolors_pom
        
    if len(barcolors2)==0:
        do_alpha = True
        barcolors2 = barcolors
    else: 
        do_alpha = False

    if len(fontColor)==0:
        fontColor = sumlinecolor
 
    if len(pointcolors)<(len(points)+len(points2axis)):
        pointcolors = pointcolors_pom
        
    if len(stackedlinecolors)<len(stackedlines):
        stackedlinecolors = stackedlinecolors_pom        
        
    if len(linesty)==0:
        linesty=[]
        for i in range(1,len(columns)+1):
            linesty.append(u'-')
        
    if ylim_min is None:
        guess_ylim_min = True
    else:
        guess_ylim_min = False
    if ylim_max is None:
        guess_ylim_max = True
    else:
        guess_ylim_max = False
    if xlim_min is None:
        guess_xlim_min = True
    else:
        guess_xlim_min = False
    if xlim_max is None:
        guess_xlim_max = True
    else:
        guess_xlim_max = False
        
    if ylim2nd_min is None:
        guess_ylim2nd_min = True
    else:
        guess_ylim2nd_min = False
    if ylim2nd_max is None:
        guess_ylim2nd_max = True
    else:
        guess_ylim2nd_max = False 

#    fig = pylab.figure()
#    ax = fig.gca()
#     
#    # Plotting stuff here ...
#    ax.plot_date(dates, values, 'b.-')
#     
#    # Set major x ticks on Mondays.
#    ax.xaxis.set_major_locator(
#        matplotlib.dates.WeekdayLocator(byweekday=matplotlib.dates.MO)
#    )
#    ax.xaxis.set_major_formatter(
#        matplotlib.dates.DateFormatter('%a %d\n%b %Y')
#    )
#import matplotlib.pyplot as plt
#from matplotlib.dates import AutoDateFormatter, AutoDateLocator
#
#xtick_locator = AutoDateLocator()
#xtick_formatter = AutoDateFormatter(xtick_locator)
#
#ax = plt.axes()
#ax.xaxis.set_major_locator(xtick_locator)
#ax.xaxis.set_major_formatter(xtick_formatter)
#import datetime
#import numpy as np
#import matplotlib.pyplot as plt
#from matplotlib.dates import AutoDateFormatter, AutoDateLocator, date2num
#
#x = [datetime.datetime.now() + datetime.timedelta(days=30*i) for i in range(20)]
#y = np.random.random((20))
#
#xtick_locator = AutoDateLocator()
#xtick_formatter = AutoDateFormatter(xtick_locator)
#
#for i in range(4):
#    ax = plt.subplot(2,2,i+1)
#    ax.xaxis.set_major_locator(xtick_locator)
#    ax.xaxis.set_major_formatter(xtick_formatter)
#    ax.plot(date2num(x),y)
#
#
#plt.show()
        
    # You typically want your plot to be ~1.33x wider than tall. This plot is a rare  
    # exception because of the number of lines being plotted on it.  
    # Common sizes: (10, 7.5) and (12, 9)  
    if ax is None:
        f, ax = plt.subplots()
        f.set_size_inches(figsize)
    else:
        f = None
    ax2nd = None
    
    if xdatetime:
        if datafreq is None:
            datafreq = data.index.freqstr
            if datafreq is None:
                datafreq = 'M-DEC'    
        if datafreq == 'Q-DEC' or datafreq == 'QS-JAN' or datafreq == 'QS' or datafreq == 'Q':
            if xticks_all:
                xtick_locator = dts.MonthLocator(bymonth=[1,4,7,10] ,interval=1)
            else:
                xtick_locator = dts.MonthLocator(bymonth=[1] ,interval=1)
                
            xtick_formatter = tkr.FuncFormatter(my_format_function_quaterly)
            barwidth = 3*18
            barxlimkoef = .5
        elif datafreq == 'M-DEC' or datafreq == 'MS' or datafreq == 'M':
            if xticks_all:
                xtick_locator = dts.MonthLocator(bymonth=[1,2,3,4,5,6,7,8,9,10,11,12] ,interval=1)
            else:
                xtick_locator = dts.MonthLocator(bymonth=[1,4,7,10] ,interval=1)            
            xtick_formatter = tkr.FuncFormatter(my_format_function_monthly)
            barwidth = 18
            barxlimkoef = .5
        elif datafreq == 'AS-JAN' or datafreq == 'AS' or datafreq == 'A':
            xtick_locator = dts.YearLocator()
            xtick_formatter = tkr.FuncFormatter(my_format_function_yearly)
            barwidth = 12*18
            barxlimkoef = .5 
    else:
        pass
    
    if widthbar>0:
        barwidth = barwidth*widthbar
        
    if bars_stacked:
        barstart = 0
        barscount = 1
    else:
        barscount = max(len(bars),len(bars2axis))
        barwidth_date_base = 216.2 / (12*18) * barwidth        
        barwidth = float(barwidth) / barscount
        barwidth_date = np.timedelta64(int(round(barwidth_date_base*86400 / barscount)),'s')
#        print barwidth_date_base*86400 / barscount
#        barwidth_date = dts.num2date(barwidth_date_base / barscount)
        barstart_date = -barwidth_date*barscount / 2. + barwidth_date / 2.
        
    if len(stackedlines)>0 or len(fillbetween)>0:
        barwidth = 0

    if xdatetime:    
        if len(xlabels)>0:
            ax.set_xticks(np.array(list(xlabels.keys()), dtype='datetime64[D]'))
            ax.set_xticklabels(xlabels.values(),rotation=xticks_rotation)       
        else:
            ax.xaxis.set_major_locator(xtick_locator)
            ax.xaxis.set_major_formatter(xtick_formatter)
    
    if len(ylabels)>0:
        ax.set_yticks(ylabels)
        ax.set_yticklabels([str(y) for y in ylabels])  
    elif not yticks_number is None:
        ax.yaxis.set_major_locator(plt.MaxNLocator(yticks_number-1))
    else:
        ax.yaxis.set_major_locator(plt.MaxNLocator(9,steps=[1,2,5,10]))
            
            
          
    # Remove the tick marks; they are unnecessary with the tick lines we just plotted.            
    ax.spines["top"].set_visible(False)  
    ax.spines["bottom"].set_visible(False)  
    ax.spines["right"].set_visible(False)  
    ax.spines["left"].set_visible(False)            
    if diff2axis or len(columns2axis)>0 or len(bars2axis)>0 or len(points2axis)>0:
        ax2nd = ax.twinx()
        ax2nd.spines["top"].set_visible(False)  
        ax2nd.spines["bottom"].set_visible(False)  
        ax2nd.spines["right"].set_visible(False)  
        ax2nd.spines["left"].set_visible(False)        
        ax.set_zorder(2) # make it on top
        ax.set_facecolor('none')
        ax2nd.set_facecolor('none')
        ax2nd.set_zorder(1)
        ax.tick_params(axis="x", which="both", bottom="on", top="off",  
                        labelbottom="on", left="off", right="off", labelleft="on",pad = 2+ticksFontSize/14.,labelsize=ticksFontSize,labelcolor = fontColor)
        ax.tick_params(axis="y", which="both", bottom="on", top="off",  
                        labelbottom="on", left="off", right="off", labelleft="on",pad = 10+ticksFontSize/14.,labelsize=ticksFontSize,labelcolor = fontColor)        
        
        ax2nd.set_frame_on(False)

        ax2nd.get_yaxis().tick_right()        
        ax2nd.tick_params(axis="x", which="both", bottom="on", top="off",  
                        labelbottom="on", left="off", right="off", labelleft="on",pad = 2+ticksFontSize/14.,labelsize=ticksFontSize,labelcolor = fontColor)
        ax2nd.tick_params(axis="y", which="both", bottom="on", top="off",  
                        labelbottom="on", left="off", right="off", labelleft="off", labelright="on",pad = 0+ticksFontSize/14.,labelsize=ticksFontSize,labelcolor = fontColor) 
                        
        if len(ylabels2axis)>0:
            ax2nd.set_yticks(ylabels2axis)
            ax2nd.set_yticklabels([str(y) for y in ylabels2axis])                        
                        
#        ax.locator_params(axis = 'y', nbins = 6)
#        ax2nd.locator_params(axis = 'y', nbins = 6)                        
    else:
        
        # Ensure that the axis ticks only show up on the bottom and left of the plot.  
        # Ticks on the right and top of the plot are generally unnecessary chartjunk.  
        ax.get_xaxis().tick_bottom()  
        ax.get_yaxis().tick_left()  
        
        ax.tick_params(axis="x", which="both", bottom="on", top="off",  
                        labelbottom="on", left="off", right="off", labelleft="on",pad = 2+ticksFontSize/14.,labelsize=ticksFontSize,labelcolor = fontColor)
        ax.tick_params(axis="y", which="both", bottom="on", top="off",  
                        labelbottom="on", left="off", right="off", labelleft="on",pad = 10+ticksFontSize/14.,labelsize=ticksFontSize,labelcolor = fontColor)
                    
        
                                                             
    #sortbars = True                                                         
    sortedbars = []
    if len(bars)>0 and sortbars:
        for rank, column in enumerate(bars):    
            if (start is None) and (end is None):
                data_x = np.array(data[column].index.values)
                data_y = np.array(data[column].values)
            elif start is None:
                data_x = np.array(data[column][:end].index.values)
                data_y = np.array(data[column][:end].values)            
            elif end is None:
                data_x = np.array(data[column][start:].index.values)
                data_y = np.array(data[column][start:].values)            
            else:
                data_x = np.array(data[column][start:end].index.values)
                data_y = np.array(data[column][start:end].values)
            pos_data_y = np.array([max(x,0) for x in data_y])
            neg_data_y = np.array([min(x,0) for x in data_y])
            
            sortedbars.append((column,barlabels[rank],pos_data_y.sum()-neg_data_y.sum()))
         
        #print sortedbars 
        sortedbars = sorted(sortedbars, key=lambda tup: tup[2],reverse=True)
        #print sortedbars
        bars = []
        barlabels = []
        for tup in sortedbars:
            bars.append(tup[0])
            barlabels.append(tup[1])


    pos_bottom2nd = 0
    neg_bottom2nd = 0
    zorder=30
    for rank, column in enumerate(bars2axis):
        if (start is None) and (end is None):
            data_x = np.array(data[column].index.values)
            data_y = np.array(data[column].values)
        elif start is None:
            data_x = np.array(data[column][:end].index.values)
            data_y = np.array(data[column][:end].values)            
        elif end is None:
            data_x = np.array(data[column][start:].index.values)
            data_y = np.array(data[column][start:].values)            
        else:
            data_x = np.array(data[column][start:end].index.values)
            data_y = np.array(data[column][start:end].values)            
        pos_data_y = np.array([max(x,0) for x in data_y])
        neg_data_y = np.array([min(x,0) for x in data_y])
        
        if bars_stacked:
            ax2nd.bar(data_x,  
                    pos_data_y,  
                    width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels2axis[rank],bottom=pos_bottom2nd,zorder=zorder)
            zorder += 1
            ax2nd.bar(data_x,  
                    neg_data_y,  
                    width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels2axis[rank],bottom=neg_bottom2nd,zorder=zorder)
            zorder += 1
        else:
            ax2nd.bar(data_x+barstart_date + barwidth_date*rank,  
                    pos_data_y,  
                    width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels2axis[rank],zorder=zorder)
            zorder += 1
            ax2nd.bar(data_x+barstart_date + barwidth_date*rank,  
                    neg_data_y,  
                    width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels2axis[rank],zorder=zorder)
            zorder += 1
                
        if bars_stacked:
            pos_bottom2nd = pos_bottom2nd + pos_data_y
            neg_bottom2nd = neg_bottom2nd + neg_data_y
        else:
            if type(pos_bottom2nd) is int:
                pos_bottom2nd = [pos_bottom2nd]*len(pos_data_y)
            if type(neg_bottom2nd) is int:
                neg_bottom2nd = [neg_bottom2nd]*len(neg_data_y)             
            pos_bottom2nd = np.amax([pos_bottom2nd,pos_data_y],axis=0)
            neg_bottom2nd = np.amax([neg_bottom2nd,neg_data_y],axis=0)     

        if guess_ylim2nd_min:
            if ylim2nd_min is None:
                ylim2nd_min = min(neg_bottom2nd[~np.isnan(neg_bottom2nd)])    
            if min(neg_bottom2nd[~np.isnan(neg_bottom2nd)])<ylim2nd_min:
                ylim2nd_min = min(neg_bottom2nd[~np.isnan(neg_bottom2nd)])
        if guess_ylim2nd_max:  
            if ylim2nd_max is None:              
                ylim2nd_max = max(pos_bottom2nd[~np.isnan(pos_bottom2nd)])
            if max(pos_bottom2nd[~np.isnan(pos_bottom2nd)])>ylim2nd_max:
                ylim2nd_max = max(pos_bottom2nd[~np.isnan(pos_bottom2nd)])
        if guess_xlim_min:
            if xlim_min is None:
                xlim_min = min(data_x)              
            if min(data_x)<xlim_min:
                xlim_min = min(data_x)
        if guess_xlim_max:
            if xlim_max is None:
                xlim_max = max(data_x)
            if max(data_x)>xlim_max:
                xlim_max = max(data_x)


    if len(bars2) > 0:
        n_time_delta = ntdelta
    else:
        n_time_delta = 0    
    
        
    time_delta = np.timedelta64(n_time_delta,'D')
    

    pos_bottom = 0
    neg_bottom = 0
    zorder=50
    alpha = 1

    for rank, column in enumerate(bars):
            if (start is None) and (end is None):
                data_x = np.array(data[column].index.values)
                data_y = np.array(data[column].values)
            elif start is None:
                data_x = np.array(data[column][:end].index.values)
                data_y = np.array(data[column][:end].values)            
            elif end is None:
                data_x = np.array(data[column][start:].index.values)
                data_y = np.array(data[column][start:].values)            
            else:
                data_x = np.array(data[column][start:end].index.values)
                data_y = np.array(data[column][start:end].values)            
            pos_data_y = np.array([max(x,0) for x in data_y])
            neg_data_y = np.array([min(x,0) for x in data_y])
            if bars_stacked:
                ax.bar(data_x,  
                        pos_data_y,  
                        width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels[rank],bottom=pos_bottom,zorder=zorder,alpha=alpha,linewidth=0)
                ax.bar(data_x,  
                        neg_data_y,  
                        width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels[rank],bottom=neg_bottom,zorder=zorder,alpha=alpha,linewidth=0)
                zorder += 1                
            else:
                ax.bar(data_x+barstart_date + barwidth_date*rank,  
                        pos_data_y,  
                        width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels[rank],zorder=zorder,alpha=alpha,linewidth=0)
                ax.bar(data_x+barstart_date + barwidth_date*rank,  
                        neg_data_y,  
                        width = barwidth, color=barcolors[rank],align='center',edgecolor = 'none', label = barlabels[rank],zorder=zorder,alpha=alpha,linewidth=0)
                zorder += 1                
             
            if bars_stacked:
                pos_bottom = pos_bottom + pos_data_y
                neg_bottom = neg_bottom + neg_data_y
            else:
                if type(pos_bottom) is int:
                    pos_bottom = [pos_bottom]*len(pos_data_y)
                if type(neg_bottom) is int:
                    neg_bottom = [neg_bottom]*len(neg_data_y)              
                pos_bottom = np.amax([pos_bottom,pos_data_y],axis=0)
                neg_bottom = np.amax([neg_bottom,neg_data_y],axis=0)
                    
            if guess_ylim_min:
                if ylim_min is None:
                    ylim_min = min(neg_bottom[~np.isnan(neg_bottom)])    
                if min(neg_bottom[~np.isnan(neg_bottom)])<ylim_min:
                    ylim_min = min(neg_bottom[~np.isnan(neg_bottom)])
            if guess_ylim_max:   
                if ylim_max is None:
                    ylim_max = max(pos_bottom[~np.isnan(pos_bottom)])
                if max(pos_bottom[~np.isnan(pos_bottom)])>ylim_max:
                    ylim_max = max(pos_bottom[~np.isnan(pos_bottom)])
            if guess_xlim_min:
                if xlim_min is None:
                    xlim_min = min(data_x)              
                if min(data_x)<xlim_min:
                    xlim_min = min(data_x)
            if guess_xlim_max:
                if xlim_max is None:
                    xlim_max = max(data_x)
                if max(data_x)>xlim_max:
                    xlim_max = max(data_x)  


    pos_bottom = 0
    neg_bottom = 0
    zorder=40
    if len(bars2) > 0:
        if do_alpha:
            alpha = 0.4
        else:
            alpha = 1
        for rank, column in enumerate(bars2):
            if (start is None) and (end is None):
                data_x = np.array(data[column].index.values)
                data_y = np.array(data[column].values)
            elif start is None:
                data_x = np.array(data[column][:end].index.values)
                data_y = np.array(data[column][:end].values)            
            elif end is None:
                data_x = np.array(data[column][start:].index.values)
                data_y = np.array(data[column][start:].values)            
            else:
                data_x = np.array(data[column][start:end].index.values)
                data_y = np.array(data[column][start:end].values)            
            pos_data_y = np.array([max(x,0) for x in data_y])
            neg_data_y = np.array([min(x,0) for x in data_y])
            if bars_stacked:
                ax.bar(data_x + time_delta,  
                        pos_data_y,  
                        width = barwidth, color=barcolors2[rank],align='center',edgecolor = 'none', label = barlabels[rank],bottom=pos_bottom,zorder=zorder,alpha = alpha,linewidth=0)
                zorder += 1
                ax.bar(data_x + time_delta,  
                        neg_data_y,  
                        width = barwidth, color=barcolors2[rank],align='center',edgecolor = 'none', label = barlabels[rank],bottom=neg_bottom,zorder=zorder, alpha = alpha,linewidth=0)
                zorder += 1                
            else:
                ax.bar(data_x+barstart_date + barwidth_date*rank,  
                        pos_data_y,  
                        width = barwidth, color=barcolors2[rank],align='center',edgecolor = 'none', label = barlabels[rank],zorder=zorder, alpha = alpha,linewidth=0)
                zorder += 1
                ax.bar(data_x+barstart_date + barwidth_date*rank,  
                        neg_data_y,  
                        width = barwidth, color=barcolors2[rank],align='center',edgecolor = 'none', label = barlabels[rank],zorder=zorder, alpha = alpha,linewidth=0)
                zorder += 1                
             
            if bars_stacked:
                pos_bottom = pos_bottom + pos_data_y
                neg_bottom = neg_bottom + neg_data_y
            else:
                if type(pos_bottom) is int:
                    pos_bottom = [pos_bottom]*len(pos_data_y)
                if type(neg_bottom) is int:
                    neg_bottom = [neg_bottom]*len(neg_data_y)              
                pos_bottom = np.amax([pos_bottom,pos_data_y],axis=0)
                neg_bottom = np.amax([neg_bottom,neg_data_y],axis=0)
                    
            if guess_ylim_min:
                if ylim_min is None:
                    ylim_min = min(neg_bottom[~np.isnan(neg_bottom)])    
                if min(neg_bottom[~np.isnan(neg_bottom)])<ylim_min:
                    ylim_min = min(neg_bottom[~np.isnan(neg_bottom)])
            if guess_ylim_max:      
                if ylim_max is None:
                    ylim_max = max(pos_bottom[~np.isnan(pos_bottom)])
                if max(pos_bottom[~np.isnan(pos_bottom)])>ylim_max:
                    ylim_max = max(pos_bottom[~np.isnan(pos_bottom)])
            if guess_xlim_min:
                if xlim_min is None:
                    xlim_min = min(data_x)              
                if min(data_x)<xlim_min:
                    xlim_min = min(data_x)
            if guess_xlim_max:
                if xlim_max is None:
                    xlim_max = max(data_x)
                if max(data_x)>xlim_max:
                    xlim_max = max(data_x)  
                
                

    zorder=70
    if len(diffbars)==2:
        if (start is None) and (end is None):
            data_x = np.array(data[columns[::-1][diffbars[0]]].index.values)
            data_y = np.array(data[columns[::-1][diffbars[0]]].values)-np.array(data[columns[::-1][diffbars[1]]].values)
        elif start is None:
            data_x = np.array(data[columns[::-1][diffbars[0]]][:end].index.values)
            data_y = np.array(data[columns[diffbars[0]]][:end].values)-np.array(data[columns[::-1][diffbars[1]]][start:end].values) 
        elif end is None:
            data_x = np.array(data[columns[::-1][diffbars[0]]][start:].index.values)
            data_y = np.array(data[columns[::-1][diffbars[0]]][start:].values)-np.array(data[columns[::-1][diffbars[1]]][start:end].values) 
        else:
            data_x = np.array(data[columns[::-1][diffbars[0]]][start:end].index.values)
            data_y = np.array(data[columns[::-1][diffbars[0]]][start:end].values)-np.array(data[columns[::-1][diffbars[1]]][start:end].values)           
        pos_data_y = np.array([max(x,0) for x in data_y])
        neg_data_y = np.array([min(x,0) for x in data_y])
            
        if diff2axis:
            ax2nd.bar(data_x,  
                    pos_data_y,  
                    width = barwidth, color=diffbarscolor,align='center',edgecolor = 'none', label = diffbarslabel,bottom=0,zorder=zorder)
            zorder += 1
            ax2nd.bar(data_x,  
                    neg_data_y,  
                    width = barwidth, color=diffbarscolor,align='center',edgecolor = 'none', label = diffbarslabel,bottom=0,zorder=zorder)
            zorder += 1                
            if guess_ylim2nd_min:
                if ylim2nd_min is None:                    
                    ylim2nd_min = min(neg_data_y[~np.isnan(neg_data_y)])    
                if min(neg_data_y[~np.isnan(neg_data_y)])<ylim2nd_min:
                    ylim2nd_min = min(neg_data_y[~np.isnan(neg_data_y)])
            if guess_ylim2nd_max:
                if ylim2nd_max is None:              
                    ylim2nd_max = max(pos_data_y[~np.isnan(pos_data_y)])                    
                if max(pos_data_y[~np.isnan(pos_data_y)])>ylim2nd_max:
                    ylim2nd_max = max(pos_data_y[~np.isnan(pos_data_y)])                    
        else:
            ax.bar(data_x,  
                    pos_data_y,  
                    width = barwidth, color=diffbarscolor,align='center',edgecolor = 'none', label = diffbarslabel_short,bottom=0,zorder=zorder)
            zorder += 1
            ax.bar(data_x,  
                    neg_data_y,  
                    width = barwidth, color=diffbarscolor,align='center',edgecolor = 'none', label = diffbarslabel_short,bottom=0,zorder=zorder)
            zorder += 1                
            if guess_ylim_min:
                if ylim_min is None:
                    ylim_min = min(neg_data_y[~np.isnan(neg_data_y)])    
                if min(neg_data_y[~np.isnan(neg_data_y)])<ylim_min:
                    ylim_min = min(neg_data_y[~np.isnan(neg_data_y)])
            if guess_ylim_max:              
                if ylim_max is None:
                    ylim_max = max(pos_data_y[~np.isnan(pos_data_y)])
                if max(pos_data_y[~np.isnan(pos_data_y)])>ylim_max:
                    ylim_max = max(pos_data_y[~np.isnan(pos_data_y)])
        if guess_xlim_min:
            if xlim_min is None:
                xlim_min = min(data_x)              
            if min(data_x)<xlim_min:
                xlim_min = min(data_x)
        if guess_xlim_max:
            if xlim_max is None:
                xlim_max = max(data_x)
            if max(data_x)>xlim_max:
                xlim_max = max(data_x)  
          
    zorder=90          
    for rank, fillbetweencolumns in enumerate(fillbetween):
        if (start is None) and (end is None):
            data_x = np.array(data[fillbetweencolumns[0]].index.values)
            data_y1 = np.array(data[fillbetweencolumns[0]].values)
            data_y2 = np.array(data[fillbetweencolumns[1]].values)
        elif start is None:
            data_x = np.array(data[fillbetweencolumns[0]][:end].index.values)
            data_y1 = np.array(data[fillbetweencolumns[0]][:end].values)
            data_y2 = np.array(data[fillbetweencolumns[1]][:end].values)                      
        elif end is None:
            data_x = np.array(data[fillbetweencolumns[0]][start:].index.values)
            data_y1 = np.array(data[fillbetweencolumns[0]][start:].values)
            data_y2 = np.array(data[fillbetweencolumns[1]][start:].values)                        
        else:
            data_x = np.array(data[fillbetweencolumns[0]][start:end].index.values)
            data_y1 = np.array(data[fillbetweencolumns[0]][start:end].values)
            data_y2 = np.array(data[fillbetweencolumns[1]][start:end].values)            
                          
        ax.fill_between(data_x, data_y1,data_y2,facecolor=fillbetweencolors[rank],label = fillbetweenlabels[rank],
                        linewidth=0.0,zorder = zorder)
#        ax.fill_between(data_x, data_y1,data_y2,facecolor=fillbetweencolors[rank],linewidth=0.0,zorder = zorder)
        zorder += 1
        
        if guess_ylim_min:
            if ylim_min is None:
                ylim_min = min(data_y1[~np.isnan(data_y1)])    
            if min(data_y1[~np.isnan(data_y1)])<ylim_min:
                ylim_min = min(data_y1[~np.isnan(data_y1)])
            if min(data_y2[~np.isnan(data_y2)])<ylim_min:
                ylim_min = min(data_y2[~np.isnan(data_y2)])                
        if guess_ylim_max:
            if ylim_max is None:
                ylim_max = max(data_y1[~np.isnan(data_y1)])  
            if max(data_y1[~np.isnan(data_y1)])>ylim_max:
                ylim_max = max(data_y1[~np.isnan(data_y1)])  
            if max(data_y2[~np.isnan(data_y2)])>ylim_max:
                ylim_max = max(data_y2[~np.isnan(data_y2)])                 
        if guess_xlim_min:
            if xlim_min is None:
                xlim_min = min(data_x)              
            if min(data_x)<xlim_min:
                xlim_min = min(data_x)
        if guess_xlim_max:
            if xlim_max is None:
                xlim_max = max(data_x)
            if max(data_x)>xlim_max:
                xlim_max = max(data_x)        
        
                
    zorder=110                          
    if len(stackedlines)>0:
        if (start is None) and (end is None):
            data_x = np.array(data[stackedlines[0]].index.values)
            data_y = np.array(data[stackedlines].values)
        elif start is None:
            data_x = np.array(data[stackedlines[0]][:end].index.values)
            data_y = np.array(data[stackedlines][:end].values)            
        elif end is None:
            data_x = np.array(data[stackedlines[0]][start:].index.values)
            data_y = np.array(data[stackedlines][start:].values)            
        else:
            data_x = np.array(data[stackedlines[0]][start:end].index.values)
            data_y = np.array(data[stackedlines][start:end].values) 
                          
        ax.stackplot(data_x, data_y.T,colors=stackedlinecolors,label = stackedlinelabels,
                      linewidth=0,zorder = zorder)
        zorder += 1
        
        if guess_ylim_min:
            if ylim_min is None:
                ylim_min = data_y.min()
            if data_y.min()<ylim_min:
                ylim_min = data_y.min()
        if guess_ylim_max:  
            if ylim_max is None:
                ylim_max = max(data_y.sum(axis=1))             
            if max(data_y.sum(axis=1))>ylim_max:
                ylim_max = max(data_y.sum(axis=1))
        if guess_xlim_min:
            if xlim_min is None:
                xlim_min = min(data_x)              
            if min(data_x)<xlim_min:
                xlim_min = min(data_x)
        if guess_xlim_max:
            if xlim_max is None:
                xlim_max = max(data_x)
            if max(data_x)>xlim_max:
                xlim_max = max(data_x)       
                       
#    print ylim_min,ylim_max

    zorder=130                          
    for rank, column in enumerate(columns2axis):  
        # Plot each line separately with its own color, using the Tableau 20  
        # color set in order.
        if (start is None) and (end is None):
            data_x = np.array(data[column].index.values)
            data_y = np.array(data[column].values)
        elif start is None:
            data_x = np.array(data[column][:end].index.values)
            data_y = np.array(data[column][:end].values)            
        elif end is None:
            data_x = np.array(data[column][start:].index.values)
            data_y = np.array(data[column][start:].values)            
        else:
            data_x = np.array(data[column][start:end].index.values)
            data_y = np.array(data[column][start:end].values) 
        if guess_ylim2nd_min:
            if ylim2nd_min is None:
                ylim2nd_min = min(data_y[~np.isnan(data_y)])    
            if min(data_y[~np.isnan(data_y)])<ylim2nd_min:
                ylim2nd_min = min(data_y[~np.isnan(data_y)])
        if guess_ylim2nd_max:    
            if ylim2nd_max is None:
                ylim2nd_max = max(data_y[~np.isnan(data_y)])
            if max(data_y[~np.isnan(data_y)])>ylim2nd_max:
                ylim2nd_max = max(data_y[~np.isnan(data_y)])
        if guess_xlim_min:
            if xlim_min is None:
                xlim_min = min(data_x)              
            if min(data_x)<xlim_min:
                xlim_min = min(data_x)
        if guess_xlim_max:
            if xlim_max is None:
                xlim_max = max(data_x)
            if max(data_x)>xlim_max:
                xlim_max = max(data_x)
        if len(diffbars)==2 or len(bars)>0 or len(bars2axis)>0 or whiteline:
            ax2nd.plot(data_x,  
                    data_y,  
                    lw=3.5, color='white', label = labels2axis[rank],zorder=zorder)
 
        zorder += 1                            
 # modified here              
        if graphSty_newRls == True:
            if rank == 0:     
 #               if len(linecolors) > 1 and (len(linecolors) == len(columns2axis)):
 #                  sub_const_even = int(len(linecolors)/2) 
 #               else:
                sub_const_even = 2
                sub_const_odd  = sub_const_even
            
            # ordering in the vecotr not inverted!              
            if rank%2 != 0: # for odd numbers
                ax2nd.plot(data_x,  
                           data_y,  
                           lw=2.5, color=linecolors[1], label = labels2axis[rank], zorder=zorder)
                sub_const_odd = sub_const_odd + 1
            else: # for even numbers
                ax2nd.plot(data_x,  
                           data_y,  
                           lw=1.5, color=linecolors[1], label = '_nolegend_', zorder=zorder)
                sub_const_even = sub_const_even + 1
        else:
            ax2nd.plot(data_x,  
                       data_y,  
                       lw=2.5, color=linecolors[rank], label = labels2axis[rank],zorder=zorder)
# modified here
        
        if fill_below_lines:                
            ax2nd.fill(data_x,  
                    data_y,  
                    facecolor=linecolors[rank], alpha=1, label = labels2axis[rank],zorder=zorder)
        zorder += 1
      
    zorder=150                        
    col_count = 0     
        
    for rank, column in enumerate(columns):  
        col_count = col_count + 1
        # Plot each line separately with its own color, using the Tableau 20  
        # color set in order.
        if (start is None) and (end is None):
            data_x = np.array(data[column].index.values)
            data_y = np.array(data[column].values)
        elif start is None:
            data_x = np.array(data[column][:end].index.values)
            data_y = np.array(data[column][:end].values)            
        elif end is None:
            data_x = np.array(data[column][start:].index.values)
            data_y = np.array(data[column][start:].values)            
        else:
            data_x = np.array(data[column][start:end].index.values)
            data_y = np.array(data[column][start:end].values) 
        if guess_ylim_min:
            if ylim_min is None:
                ylim_min = min(data_y[~np.isnan(data_y)])    
            if min(data_y[~np.isnan(data_y)])<ylim_min:
                ylim_min = min(data_y[~np.isnan(data_y)])
        if guess_ylim_max:
            if ylim_max is None:
                ylim_max = max(data_y[~np.isnan(data_y)])
            if max(data_y[~np.isnan(data_y)])>ylim_max:
                ylim_max = max(data_y[~np.isnan(data_y)])
        if guess_xlim_min:
            if xlim_min is None:
                xlim_min = min(data_x)              
            if min(data_x)<xlim_min:
                xlim_min = min(data_x)
        if guess_xlim_max:
            if xlim_max is None:
                xlim_max = max(data_x)
            if max(data_x)>xlim_max:
                xlim_max = max(data_x)
        if len(bars2) > 0 :
            if col_count > 1:   
                ax.plot(data_x,  
                        data_y,  
                        lw=1.5, color='white', 
                        marker = '.', markersize = 12, mew = 0, zorder=zorder, linestyle='none', label='_nolegend_')
                ax.plot(data_x,  
                        data_y,  
                        lw=1.5, color=linecolors[len(columns2axis)], 
                        marker = '.', markersize = 10, mew = 0, zorder=zorder, linestyle='none', label='_nolegend_')
            else:
                if len(diffbars)==2 or len(bars)>0 or len(bars2axis)>0 or whiteline:
                    ax.plot(data_x,  
                            data_y,  
                            lw=3.5, color='white', label = '_nolegend_',zorder=zorder)
                    zorder += 1                         
                ax.plot(data_x,  
                        data_y,  
                        lw=2.5, color=linecolors[len(columns2axis)+rank], label = '_nolegend_',
                        marker = linemarker, markersize = linemarkersize, mew = 0, zorder=zorder, linestyle=linesty[rank])   
        else:
            if len(diffbars)==2 or len(bars)>0 or len(bars2axis)>0 or whiteline:
                ax.plot(data_x,  
                        data_y,  
                        lw=3.5, color='white', label = labels[rank],zorder=zorder)
                zorder += 1                         
## modified here            
            if graphSty_newRls == True:
                if rank == 0:     
                    if len(linecolors) > 1 and (len(linecolors) == len(columns) or len(linecolors) == (len(columns) + len(columns2axis))):
                        sub_const_even = int(len(linecolors)/2)
                    else:
                        sub_const_even = 0
                    sub_const_odd  = sub_const_even

                    if len(columns2axis) > 0 and (len(linecolors) == (len(columns) + len(columns2axis))):
                        sub_const_even = 3
                        sub_const_odd  = 3
                          
                if rank%2 != 0: # for odd numbers
                    ax.plot(data_x,  
                            data_y,  
                            lw=1.5, color=linecolors[sub_const_odd], label = '_nolegend_',
                            marker = linemarker, markersize = linemarkersize, mew = 0, zorder=zorder, linestyle=linesty[rank])
                    sub_const_odd = sub_const_odd + 1
                else: # for even numbers
                    ax.plot(data_x,  
                            data_y,  
                            lw=2.5, color=linecolors[sub_const_even], label = labels[rank],
                            marker = linemarker, markersize = linemarkersize, mew = 0, zorder=zorder+10, linestyle=linesty[rank]) 
                    sub_const_even = sub_const_even + 1
            else:
                ax.plot(data_x,  
                        data_y,  
                        lw=2.5, color=linecolors[len(columns2axis)+rank], label = labels[rank],
                        marker = linemarker, markersize = linemarkersize, mew = 0, zorder=zorder, linestyle=linesty[rank])
## modified here                
        if fill_below_lines:                
            ax.fill(data_x,  
                    data_y,  
                    facecolor=linecolors[len(columns2axis)+rank], alpha=1, label = labels[rank],zorder=zorder)                
        if switchlineorder == True:
            zorder = zorder-2
        else:
            zorder += 1    
    
    for rank, column in enumerate(points):
        if (start is None) and (end is None):
            data_x = np.array(data[column].index.values)
            data_y = np.array(data[column].values)
        elif start is None:
            data_x = np.array(data[column][:end].index.values)
            data_y = np.array(data[column][:end].values)            
        elif end is None:
            data_x = np.array(data[column][start:].index.values)
            data_y = np.array(data[column][start:].values)            
        else:
            data_x = np.array(data[column][start:end].index.values)
            data_y = np.array(data[column][start:end].values)      
        data_y = data_y.astype(float)              
        data_x = data_x[~np.isnan(data_y)]
        data_y = data_y[~np.isnan(data_y)]                 
        ax.scatter(data_x, data_y, s=pointsize[rank], facecolors=pointcolors[rank],lw=0.5, edgecolors='white', marker = pointtype[rank], label = pointlabels[rank],zorder=zorder)        
        zorder += 1

    for rank, column in enumerate(points2axis):
        if (start is None) and (end is None):
            data_x = np.array(data[column].index.values)
            data_y = np.array(data[column].values)
        elif start is None:
            data_x = np.array(data[column][:end].index.values)
            data_y = np.array(data[column][:end].values)            
        elif end is None:
            data_x = np.array(data[column][start:].index.values)
            data_y = np.array(data[column][start:].values)            
        else:
            data_x = np.array(data[column][start:end].index.values)
            data_y = np.array(data[column][start:end].values)      
        data_y = data_y.astype(float)              
        data_x = data_x[~np.isnan(data_y)]
        data_y = data_y[~np.isnan(data_y)]                  
        ax2nd.scatter(data_x, data_y, s=pointsize[rank], facecolors=pointcolors[rank],lw=0.5, edgecolors='white', marker = pointtype[rank], label = pointlabels2axis[rank],zorder=zorder)        
        zorder += 1        
        
    if len(columns)>0:
        column = columns[0]
        if (start is None) and (end is None):
            data_x = np.array(data[column].index.values)
            data_y = np.array(data[column].values)
        elif start is None:
            data_x = np.array(data[column][:end].index.values)
            data_y = np.array(data[column][:end].values)            
        elif end is None:
            data_x = np.array(data[column][start:].index.values)
            data_y = np.array(data[column][start:].values)            
        else:
            data_x = np.array(data[column][start:end].index.values)
            data_y = np.array(data[column][start:end].values)         
    elif len(bars)>0:
        column = bars[0]
        if (start is None) and (end is None):
            data_x = np.array(data[column].index.values)
            data_y = np.array(data[column].values)
        elif start is None:
            data_x = np.array(data[column][:end].index.values)
            data_y = np.array(data[column][:end].values)            
        elif end is None:
            data_x = np.array(data[column][start:].index.values)
            data_y = np.array(data[column][start:].values)            
        else:
            data_x = np.array(data[column][start:end].index.values)
            data_y = np.array(data[column][start:end].values)         
                
    # Limit the range of the plot to only where the data is.  
    # Avoid unnecessary whitespace. 
    if xdatetime:
        xticks_major = ax.xaxis.get_majorticklocs()
        xtick = (barxlimkoef*(data_x[1].astype('float64') - data_x[0].astype('float64')))
        if xaxisindent:
                xlim_min = (xlim_min.astype('float64') - xtick).astype('<M8[ns]')
                xlim_max = (xlim_max.astype('float64') + xtick).astype('<M8[ns]')
        else:
            if len(bars)>0 or (len(diffbars)==2 and not diff2axis):
                if len(stackedlines)==0 and len(fillbetween)==0:
                    xlim_min = (xlim_min.astype('float64')- xtick).astype('<M8[ns]')
                    xlim_max = (xlim_max.astype('float64') + xtick).astype('<M8[ns]')
                
            if len(bars2axis)>0 or (len(diffbars)==2 and diff2axis):
                if len(stackedlines)==0 and len(fillbetween)==0:
                    xlim_min = (xlim_min.astype('float64') - xtick).astype('<M8[ns]')
                    xlim_max = (xlim_max.astype('float64') + xtick).astype('<M8[ns]')
                
                
        
    if diff2axis: #or len(columns2axis)>0 or len(bars2axis)>0:
        if ylim_min<0 and ylim_max>0:
            if ylim2nd_min<0 and ylim2nd_max>0:        
                ylim_min = min(ylim_min,ylim2nd_min)
                ylim_max = max(ylim_max,ylim2nd_max)

    xlim_max = (xlim_max.astype('float64')).astype('<M8[ns]')+time_delta/2
            
    ax.set_ylim(ylim_min, ylim_max)  
    ax.set_xlim(xlim_min, xlim_max)      
        
    # Make sure your axis ticks are large enough to be easily read.  
    # You don't want your viewers squinting to read your plot.  
#    ax.xticks(fontsize=14)


#    time.sleep(0.001)
    yticks_major = ax.yaxis.get_majorticklocs()
    if ylim_hard is True:
        if ylim_min is None or ylim_max is None:
            ylim_min = yticks_major[0]- (yticks_major[1]-yticks_major[0])*0.25
            ylim_max = yticks_major[-1]+ (yticks_major[-1]-yticks_major[-2])*0.25            
        else:
            yticks_major = yticks_major[yticks_major>=ylim_min]
            yticks_major = yticks_major[yticks_major<=ylim_max]
            ylim_min = ylim_min - (yticks_major[1]-yticks_major[0])*0.25
            ylim_max = ylim_max + (yticks_major[-1]-yticks_major[-2])*0.25        
    else:
        ylim_min = yticks_major[0]- (yticks_major[1]-yticks_major[0])*0.25
        ylim_max = yticks_major[-1]+ (yticks_major[-1]-yticks_major[-2])*0.25
    ax.set_ylim(ylim_min, ylim_max)
    
    if diff2axis or len(columns2axis)>0 or len(bars2axis)>0:
#        print '1st:',ylim_min,ylim_max
#        print '2nd:',ylim2nd_min,ylim2nd_max
        y_onetick = yticks_major[1]-yticks_major[0]
        yticks2nd_major = ax2nd.yaxis.get_majorticklocs()
        #y_onetick2nd = yticks2nd_major[1]-yticks2nd_major[0]
        if ylim2nd_min<=0  and ylim2nd_max>=0: # pravá osa obsahuje nulu (to by ale měla vždy)
            if ylim_min<=0 and ylim_max>=0: # levá osa obsahuje nulu
                if guess_ylim2nd_min or guess_ylim2nd_max:                
                          zeropos = np.where(yticks_major == 0)
                          if zeropos[0].size == 0:
                              zeropos = np.floor(yticks_major.size/2)
                          else:
                              zeropos = zeropos[0][0]
                          onetick2nd_pos = abs(ylim2nd_max/(yticks_major.size-zeropos-1))
                          onetick2nd_neg = abs(ylim2nd_min/(zeropos))
                          onetick2nd_guess = max(onetick2nd_pos,onetick2nd_neg)
                          onetick2nd_order = np.floor(np.log10(onetick2nd_guess))
                          if onetick2nd_order < -2:
                              onetick2nd_order = -2                          
                          tick_power = np.power(10,abs(onetick2nd_order))
                          tick_power = tick_power.astype(int)
                          if onetick2nd_order < 0:
                               onetick2nd_guess = np.ceil(onetick2nd_guess*tick_power)/tick_power
                          else:
                               onetick2nd_guess = np.ceil(onetick2nd_guess/tick_power)*tick_power                          
                          ylim2nd_min = -onetick2nd_guess*zeropos
                          ylim2nd_max = onetick2nd_guess*(yticks_major.size-zeropos-1)
#                ax2nd.yaxis.set_ticks([0,0.5,1,1.5,2,2.5,3,3.5,4])
                if guess_ylim2nd_min and guess_ylim2nd_max:
                    yticks2nd_major = ax2nd.yaxis.get_majorticklocs()
                    ax2nd.yaxis.set_ticks(yticks2nd_major)
                y_onetick = (ylim2nd_max - ylim2nd_min) / float((len(yticks_major)-1))
                ylim2nd_max = ylim2nd_max + y_onetick*0.25
                yticks2nd_major = np.arange(ylim2nd_min,ylim2nd_max,y_onetick)
                ylim2nd_min = ylim2nd_min - y_onetick*0.25             
                ax2nd.yaxis.set_ticks(yticks2nd_major)
            else: # levá osa neobsahuje nulu
                    if min(yticks2nd_major) == 0 or min(yticks2nd_major) > 0: 
                          ylim2nd_min = 0
                          onetick2nd_guess = abs(ylim2nd_max/(yticks_major.size-1))
                          onetick2nd_order = np.floor(np.log10(onetick2nd_guess))
                          if onetick2nd_order < -2:
                              onetick2nd_order = -2                          
                          tick_power = np.power(10,abs(onetick2nd_order))
                          tick_power = tick_power.astype(int)
                          if onetick2nd_order < 0:
                               onetick2nd_guess = np.ceil(onetick2nd_guess*tick_power)/tick_power
                          else:
                               onetick2nd_guess = np.ceil(onetick2nd_guess/tick_power)*tick_power                          
                          ylim2nd_max = ylim2nd_min + (yticks_major.size-1)*onetick2nd_guess
                    elif max(yticks2nd_major) == 0 or max(yticks2nd_major) < 0:
                          ylim2nd_max = 0
                          onetick2nd_guess = abs(ylim2nd_min/(yticks_major.size-1))
                          onetick2nd_order = np.floor(np.log10(onetick2nd_guess))
                          if onetick2nd_order < -2:
                              onetick2nd_order = -2                          
                          tick_power = np.power(10,abs(onetick2nd_order))
                          tick_power = tick_power.astype(int)
                          if onetick2nd_order < 0:
                               onetick2nd_guess = np.ceil(onetick2nd_guess*tick_power)/tick_power
                          else:
                               onetick2nd_guess = np.ceil(onetick2nd_guess/tick_power)*tick_power
                          ylim2nd_min = ylim2nd_max - (yticks_major.size-1)*onetick2nd_guess                        
                    else: 
                          zeropos = np.where(yticks2nd_major == 0)
                          if zeropos[0].size == 0:
                              zeropos = np.floor(yticks_major.size/2)
                          else:
                              zeropos = zeropos[0][0]
                          zeropos_guess = round((zeropos+1)/yticks2nd_major.size*yticks_major.size,0)-1
                          if zeropos_guess == 0 and zeropos == 1:
                              zeropos = 1
                          elif zeropos_guess >= yticks_major.size-1 and zeropos < yticks2nd_major.size-1:
                              zeropos = yticks_major.size-2
                          elif zeropos_guess > yticks_major.size-1:
                              zeropos = yticks_major.size-1        
                          else: 
                              zeropos = zeropos_guess
                          onetick2nd_pos = abs(ylim2nd_max/(yticks_major.size-zeropos-1))
                          onetick2nd_neg = abs(ylim2nd_min/(zeropos))
                          onetick2nd_guess = max(onetick2nd_pos,onetick2nd_neg)
                          onetick2nd_order = np.floor(np.log10(onetick2nd_guess))
                          if onetick2nd_order < -2:
                              onetick2nd_order = -2
                          tick_power = np.power(10,abs(onetick2nd_order))
                          tick_power = tick_power.astype(int)
                          if onetick2nd_order < 0:
                               onetick2nd_guess = np.ceil(onetick2nd_guess*tick_power)/tick_power
                          else:
                               onetick2nd_guess = np.ceil(onetick2nd_guess/tick_power)*tick_power
                          ylim2nd_min = -onetick2nd_guess*zeropos
                          ylim2nd_max = onetick2nd_guess*(yticks_major.size-zeropos-1)
#                ax2nd.yaxis.set_ticks([0,0.5,1,1.5,2,2.5,3,3.5,4])
                    if guess_ylim2nd_min and guess_ylim2nd_max:
                        yticks2nd_major = ax2nd.yaxis.get_majorticklocs()
                        ax2nd.yaxis.set_ticks(yticks2nd_major)
                    y_onetick = (ylim2nd_max - ylim2nd_min) / float((len(yticks_major)-1))
                    ylim2nd_max = ylim2nd_max + y_onetick*0.25
                    yticks2nd_major = np.arange(ylim2nd_min,ylim2nd_max,y_onetick)
                    ylim2nd_min = ylim2nd_min - y_onetick*0.25             
                    ax2nd.yaxis.set_ticks(yticks2nd_major)
                
#                if abs(ylim2nd_min)>ylim2nd_max: 
#                    prostr = len(yticks_major)-len(yticks2nd_major[yticks2nd_major>=0])
#                else:
#                    prostr = len(yticks2nd_major[yticks2nd_major<=0])
##                print prostr
##                y_onetick = (ylim2nd_max - ylim2nd_min) / float((len(yticks_major)-1))
#                ylim2nd_min = -prostr*y_onetick - y_onetick*0.25
#                ylim2nd_max = (len(yticks_major)-prostr-1)*y_onetick+y_onetick*0.25
#                yticks2nd_major = np.arange(-prostr*y_onetick,(len(yticks_major)-prostr)*y_onetick,y_onetick)
        else: # pravá osa neobsahuje nulu
            if guess_ylim2nd_min:
                prostr=len(yticks_major)/2+1
                prostr_y2nd = round((ylim2nd_min + (ylim2nd_max - ylim2nd_min) / 2 ))
                y2nd_onetick = y_onetick
                while True:
    #                print y2nd_onetick, prostr_y2nd
    #                print (prostr_y2nd - prostr*y2nd_onetick - y2nd_onetick*0.25),ylim2nd_min 
    #                print (prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25),ylim2nd_max
                    if (prostr_y2nd - prostr*y2nd_onetick - y2nd_onetick*0.25)<ylim2nd_min and (prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25)>ylim2nd_max:
                        break
                    else:
                        y2nd_onetick = y2nd_onetick*2
                ylim2nd_min = prostr_y2nd -prostr*y2nd_onetick - y2nd_onetick*0.25
                ylim2nd_max = prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25
                yticks2nd_major = np.arange(prostr_y2nd-prostr*y2nd_onetick,prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25,y2nd_onetick)
            elif guess_ylim2nd_max:
                prostr=len(yticks_major)/2+1
                prostr_y2nd = round((ylim2nd_min + (ylim2nd_max - ylim2nd_min) / 2 ))
                y2nd_onetick = y_onetick
                while True:
    #                print y2nd_onetick, prostr_y2nd
    #                print (prostr_y2nd - prostr*y2nd_onetick - y2nd_onetick*0.25),ylim2nd_min 
    #                print (prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25),ylim2nd_max
                    if (prostr_y2nd - prostr*y2nd_onetick - y2nd_onetick*0.25)<ylim2nd_min and (prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25)>ylim2nd_max:
                        break
                    else:
                        y2nd_onetick = y2nd_onetick*2
                ylim2nd_min = prostr_y2nd -prostr*y2nd_onetick - y2nd_onetick*0.25
                ylim2nd_max = prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25
                yticks2nd_major = np.arange(prostr_y2nd-prostr*y2nd_onetick,prostr_y2nd + (len(yticks_major)-prostr-1)*y2nd_onetick+y2nd_onetick*0.25,y2nd_onetick)
            else:
                y_tick = (yticks_major[-1]-yticks_major[0])/(len(yticks_major)-1)
                y2nd_tick = (ylim2nd_max-ylim2nd_min)/(len(yticks_major)-1)
                ylim2nd_max = ylim2nd_max + (ylim_max-yticks_major[-1])*y2nd_tick/y_tick
                if len(ylabels2axis)==0:
                    yticks2nd_major = np.arange(ylim2nd_min,ylim2nd_max,y2nd_tick)
                ylim2nd_min = ylim2nd_min - (yticks_major[0]-ylim_min)*y2nd_tick/y_tick
                
        
#        print ylim2nd_min, ylim2nd_max        
#        print '2nd after:',ylim2nd_min,ylim2nd_max 
        if np.isnan(ylim2nd_min) or np.isnan(ylim2nd_max):
            ylim2nd_min = ylim_min
            ylim2nd_max = ylim_max
            yticks2nd_major = yticks_major
        ax2nd.set_ylim(ylim2nd_min, ylim2nd_max)
        ax2nd.set_xlim(xlim_min, xlim_max)
        ax2nd.yaxis.set_ticks(yticks2nd_major) 
        
#        yticks2nd_major = ax2nd.yaxis.get_majorticklocs()
#        ylim2nd_min = yticks2nd_major[0] - (yticks2nd_major[1]-yticks2nd_major[0])*0.25
#        ylim2nd_max = yticks2nd_major[-1] + (yticks2nd_major[-1]-yticks2nd_major[-2])*0.25
#        ax2nd.set_ylim(ylim2nd_min, ylim2nd_max)
        
#    yticks_major_min = 0
#    yticks_major_max = len(yticks_major)
#    for i in range(len(yticks_major)):
#        if yticks_major[i]<ylim_min:
#            yticks_major_min += 1    
#        if yticks_major[i]>ylim_max:
#                    yticks_major_max -= 1
#    yticks_major = yticks_major[yticks_major_min:yticks_major_max]
#    yticks_major = np.hstack([ylim_min,yticks_major,ylim_max]) 
        
    onetick = round(yticks_major[1]-yticks_major[0],10)
    ticksDecPoints_cur = 0
    while True:
        n = onetick*10**ticksDecPoints_cur % 1
        if round(n,20) == 0:
            break
        ticksDecPoints_cur += 1
    
#http://stackoverflow.com/questions/2389846/python-decimals-format 
    if ticksDecPoints>ticksDecPoints_cur:
        ticksDecPoints_cur=ticksDecPoints
       
    if pct:
        format_string = '%%0.%df %%%% ' %ticksDecPoints_cur
    else:
        format_string = '%%0.%df' %ticksDecPoints_cur
        
    ax.set_yticks(yticks_major)
    #yticks_major_labels = [(format_string %x).rstrip('0').rstrip('.') for x in yticks_major]
    yticks_major_labels = [(format_string %x) for x in yticks_major]
    if ticksDecSepCS is True:
        yticks_major_labels = [x.replace('.',',') for x in yticks_major_labels]   
    ax.set_yticklabels(yticks_major_labels)
    
    if diff2axis or len(columns2axis)>0 or len(bars2axis)>0:
        yticks2nd_major = ax2nd.yaxis.get_majorticklocs()
        onetick = round(yticks2nd_major[1]-yticks2nd_major[0],10)
        ticksDecPoints_cur = 0
        while True:
            n = onetick*10**ticksDecPoints_cur % 1
            if round(n,20) == 0:
                break
            ticksDecPoints_cur += 1
            
        if ticksDecPoints>ticksDecPoints_cur:
            ticksDecPoints_cur=ticksDecPoints
           
        if pct:
            format_string = '%%0.%df %%%% ' %ticksDecPoints_cur
        else:
            format_string = '%%0.%df' %ticksDecPoints_cur            
            
        ax2nd.set_yticks(yticks2nd_major)
        #yticks2nd_major_labels = [(format_string %x).rstrip('0').rstrip('.') for x in yticks2nd_major]
        yticks2nd_major_labels = [(format_string %x) for x in yticks2nd_major]
        if ticksDecSepCS is True:
            yticks2nd_major_labels = [x.replace('.',',') for x in yticks2nd_major_labels]   
        ax2nd.set_yticklabels(yticks2nd_major_labels)    
    
    ns = 1e-9
    xlim_min_matplotlib = dts.date2num(datetime.datetime.utcfromtimestamp(xlim_min.astype(float) * ns))
    xlim_max_matplotlib = dts.date2num(datetime.datetime.utcfromtimestamp(xlim_max.astype(float) * ns))
    xticks_major = ax.xaxis.get_majorticklocs()
    
    if datafreq == 'M-DEC':
        xticks_major_min = 0
        xticks_major_max = len(xticks_major)
        for i in range(len(xticks_major)):
            if xticks_major[i]<xlim_min_matplotlib:
                xticks_major_min += 1    
            if xticks_major[i]>xlim_max_matplotlib:
                xticks_major_max -= 1
        xticks_major = xticks_major[xticks_major_min:xticks_major_max]
        xticks_major = np.hstack([xlim_min_matplotlib,xticks_major,xlim_max_matplotlib])    
        ax.set_xticks(xticks_major)
        
    # Provide tick lines across the plot to help your viewers trace along  
    # the axis ticks. Make sure that the lines are light and small so they  
    # don't obscure the primary data lines.  
    zorder = 2        
    if diff2axis or len(columns2axis)>0 or len(bars2axis)>0:
        for y in yticks2nd_major: #[1:-1]:
            itemindex = np.where(yticks2nd_major==y)
            if y>-0.001 and y<0.001 or yticks_major[itemindex[0]]==0:
                ax2nd.plot([xlim_min, xlim_max], [y] * len([xlim_min, xlim_max]), "-", lw=0.75, color="black", alpha=1,zorder=zorder)                              
            else:
#                ax2nd.plot([xlim_min, xlim_min],[ylim2nd_min, ylim2nd_max], "-", lw=1, color="black", alpha=1,zorder=zorder)
                if gridlines == 'both':
                    ax2nd.plot([xlim_min, xlim_max], [y] * len([xlim_min, xlim_max]), "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder)
                    for x in xticks_major:
                        ax2nd.plot([x] * len([ylim2nd_min, ylim2nd_max]),[ylim2nd_min, ylim2nd_max], "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder)         
                elif gridlines == 'horizontal':   
                    ax2nd.plot([xlim_min, xlim_max], [y] * len([xlim_min, xlim_max]), "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder)
                elif gridlines == 'vertical':
                    for x in xticks_major:
                        ax2nd.plot([x] * len([ylim2nd_min, ylim2nd_max]),[ylim2nd_min, ylim2nd_max], "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder)         
                zorder += 1
    else:
        for y in yticks_major: #[1:-1]:
            if y==0:
                ax.plot([xlim_min, xlim_max], [y] * len([xlim_min, xlim_max]), "-", lw=0.75, color="black", alpha=1,zorder=zorder)    
            else:
#                ax.plot([xlim_min, xlim_min],[ylim_min, ylim_max], "-", lw=1, color="black", alpha=1,zorder=zorder)
                if gridlines == 'both':
                    ax.plot([xlim_min, xlim_max], [y] * len([xlim_min, xlim_max]), "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder) 
                    for x in xticks_major:
                        ax.plot([x] * len([ylim_min, ylim_max]),[ylim_min, ylim_max], "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder)
                if gridlines == 'horizontal':
                    ax.plot([xlim_min, xlim_max], [y] * len([xlim_min, xlim_max]), "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder) 
                if gridlines == 'vertical':
                   for x in xticks_major:
                        ax.plot([x] * len([ylim_min, ylim_max]),[ylim_min, ylim_max], "-", lw=0.5, color=(210/255.,210/255.,210/255.), alpha=1,zorder=zorder)
            zorder += 1
        
    # matplotlib's title() call centers the title on the plot, but not the graph,  
    # so I used the text() call to customize where the title goes.  
      
    # Make the title big enough so it spans the entire plot, but don't make it  
    # so big that it requires two lines to show.  
      
    # Note that if the title is descriptive enough, it is unnecessary to include  
    # axis labels; they are self-evident, in this plot's case.

# TODO: posun legendy
    #print dir(ax)
    
    #print ax.get_tightbbox()
    #print ax.get_xaxis_text1_transform()

#    print ax.titleOffsetTrans
#    print ax.transAxes
#    print ax.viewLim
#    print ax.xcorr()
      
    if len(figtitle)>0:
        if len(figsubtitle)>0:
            font_prop.set_size(figtitleFontSize)
            ax.text(xlim_min+(xlim_max-xlim_min)/2, ylim_max+(ylim_max-ylim_min)*0.055*(figtitleFontSize/17.), 
                    figtitle, fontsize=figtitleFontSize, ha="center",color = fontColor,fontproperties=font_prop)  
            font_prop.set_size(round(figtitleFontSize*.8235))            
            ax.text(xlim_min+(xlim_max-xlim_min)/2, ylim_max+(ylim_max-ylim_min)*0.01*(figtitleFontSize/17.), 
                    figsubtitle, fontsize=round(figtitleFontSize*.8235), ha="center",color = fontColor,fontproperties=font_prop)  
        else:
            font_prop.set_size(figtitleFontSize)
            ax.text(xlim_min+(xlim_max-xlim_min)/2, ylim_max+(ylim_max-ylim_min)*0.03, figtitle, 
                    fontsize=figtitleFontSize, ha="center",color = fontColor,fontproperties=font_prop)  
#    legend = 'lower center'
#    legend='upper center'
    if legpos is None:
        bbox_to_anchor=(0.5, -0.045-(ticksFontSize-14)*.006+legpushdown)
    else:
        bbox_to_anchor = legpos    
    #bbox_to_anchor=(0.5, 1., -0.05, -0.05)   

    while '' in pointlabels:
        pointlabels.remove('')    
    if len(legend)>0:
        legend='upper center'
#        'best'         : 0, (only implemented for axis legends)
#        'upper right'  : 1,
#        'upper left'   : 2,
#        'lower left'   : 3,
#        'lower right'  : 4,
#        'right'        : 5,
#        'center left'  : 6,
#        'center right' : 7,
#        'lower center' : 8,
#        'upper center' : 9,
#        'center'       : 10,
        if diff2axis or len(columns2axis)>0 or len(bars2axis)>0 or len(points2axis)>0:
            leghandles, leglabels = ax.get_legend_handles_labels()
            leghandles2nd, leglabels2nd = ax2nd.get_legend_handles_labels()
            if len(diffbars)==2 or len(bars)>0 or len(bars2axis)>0 or whiteline:
                if len(pointlabels)>0:
                    leglabels = leglabels[:2*len(labels)][1::2][::-1]+leglabels[2*len(labels):][::2]+ leglabels2nd[:2*len(labels2axis)][1::2][::-1]+leglabels2nd[2*len(labels2axis):][::2]+leglabels[-len(pointlabels):]
                    leghandles = leghandles[:2*len(labels)][1::2][::-1]+leghandles[2*len(labels):][::2] + leghandles2nd[:2*len(labels2axis)][1::2][::-1]+leghandles2nd[2*len(labels2axis):][::2]+leghandles[-len(pointlabels):]
                elif len(pointlabels2axis)>0:
                    leglabels = leglabels[:2*len(labels)][1::2][::-1]+leglabels[2*len(labels):][::2]+ leglabels2nd[:2*len(labels2axis)][1::2][::-1]+leglabels2nd[2*len(labels2axis):][::2]+leglabels[-len(pointlabels2axis):]
                    leghandles = leghandles[:2*len(labels)][1::2][::-1]+leghandles[2*len(labels):][::2] + leghandles2nd[:2*len(labels2axis)][1::2][::-1]+leghandles2nd[2*len(labels2axis):][::2]+leghandles[-len(pointlabels2axis):]                    
                else:
                    leglabels = leglabels[:2*len(labels)][1::2][::-1]+leglabels[2*len(labels):][::2]+ leglabels2nd[:2*len(labels2axis)][1::2][::-1]+leglabels2nd[2*len(labels2axis):][::2]
                    leghandles = leghandles[:2*len(labels)][1::2][::-1]+leghandles[2*len(labels):][::2] + leghandles2nd[:2*len(labels2axis)][1::2][::-1]+leghandles2nd[2*len(labels2axis):][::2]
            else:
                leghandles = leghandles2nd + leghandles
                leglabels = leglabels2nd + leglabels
                if len(pointlabels)>0:
                    leglabels = leglabels[:len(labels+labels2axis)][::-1]+leglabels[-len(pointlabels):]
                    leghandles = leghandles[:len(labels+labels2axis)][::-1]+leghandles[-len(pointlabels):]                    
                elif len(pointlabels2axis)>0:
                    leglabels = leglabels[len(pointlabels2axis):len(labels+labels2axis+pointlabels2axis)][::-1]+leglabels[:len(pointlabels2axis)]
                    leghandles = leghandles[len(pointlabels2axis):len(labels+labels2axis+pointlabels2axis)][::-1]+leghandles[:len(pointlabels2axis)]                    
                
                else:
                    leglabels = leglabels[:len(labels+labels2axis)][::-1]
                    leghandles = leghandles[:len(labels+labels2axis)][::-1]            
        else:
            leghandles, leglabels = ax.get_legend_handles_labels()
            if len(diffbars)==2 or len(bars)>0 or whiteline:
                if len(pointlabels)>0:
                    if labels == ['']:
                        leglabels = leglabels[:2*len(labels)][len(pointlabels)::len(pointlabels)+1][::-1]+leglabels[2*len(labels):][::2][1:]+leglabels[:len(pointlabels)]
                        leghandles = leghandles[:2*len(labels)][len(pointlabels)::len(pointlabels)+1][::-1]+leghandles[2*len(labels):][::2][1:]+leghandles[:len(pointlabels)]                           
                    else:    
                        leglabels = leglabels[:2*len(labels)][1::2] + leglabels[2*len(labels)+len(pointlabels):][::2] + leglabels[2*len(labels):2*len(labels)+len(pointlabels)]
                        leghandles = leghandles[:2*len(labels)][1::2] + leghandles[2*len(labels)+len(pointlabels):][::2] + leghandles[2*len(labels):2*len(labels)+len(pointlabels)]                           
                elif len(fillbetweenlabels)>0:
                    leglabels = leglabels[:2*len(labels)][1::2][::-1]+leglabels[2*len(labels):][::2]+leglabels[-len(fillbetweenlabels):]
                    leghandles = leghandles[:2*len(labels)][1::2][::-1]+leghandles[2*len(labels):][::2]+leglabels[-len(fillbetweenlabels):]                                
                else:
                    leglabels = leglabels[:2*len(labels)][1::2][::-1]+leglabels[2*len(labels):][::2]
                    leghandles = leghandles[:2*len(labels)][1::2][::-1]+leghandles[2*len(labels):][::2]            
            else:
                if len(pointlabels)>0:
                    if labels == ['']:
                        leglabels = leglabels[-len(pointlabels):]
                        leghandles = leghandles[-len(pointlabels):]  
                    else:
                        leglabels = leglabels[:len(labels)][::-1]+leglabels[-len(pointlabels):]
                        leghandles = leghandles[:len(labels)][::-1]+leghandles[-len(pointlabels):] 
                elif len(fillbetweenlabels)>0:
                    leglabels = leglabels[:len(labels)][::-1]+leglabels[-len(fillbetweenlabels):]
                    leghandles = leghandles[:len(labels)][::-1]+leghandles[-len(fillbetweenlabels):]                    
                else:
                    leglabels = leglabels[:len(labels)][::-1]
                    leghandles = leghandles[:len(labels)][::-1]
                    
#            if len(stackedlinelabels)>0:
#                proxy_rects = [ptch.Rectangle((0, 0), 1, 1, fc=stackedlinecolors[i]) for i in range(len(stackedlines)) if len(stackedlinelabels[i])>0]
#                # make the legend    
#                leglabels = proxy_rects
#                leghandles = stackedlinelabels
        if len(bars2) > 0 and legend2 < 1:                    
            leghandles = leghandles[0:len(bars)]     
            leglabels  = leglabels[0:len(bars)]               
            font_prop.set_size(ticksFontSize)
        else:
            if legend2>0:
                leglabels = barlabels
        legend = ax.legend(leghandles, leglabels,fontsize=ticksFontSize,
                           loc = legend,borderaxespad=0.05,
                           bbox_to_anchor=bbox_to_anchor,
                           ncol=legcols, prop=font_prop)
        for txt in legend.get_texts():
            txt.set_color(fontColor)                            
        frame = legend.get_frame()
#        if not ehist is None:
#            frame.set_color(shaded_area)    
#            #frame.set_alpha(0.5)
#        else:
#            frame.set_color('white')
        frame.set_color('none')
#        frame.set_facecolor('green')
#        frame.set_edgecolor('red')

    # Always include your data source(s) and copyright notice! And for your  
    # data sources, tell your viewers exactly where the data came from,  
    # preferably with a direct link to the data. Just telling your viewers  
    # that you used data from the "U.S. Census Bureau" is completely useless:  
    # the U.S. Census Bureau provides all kinds of data, so how are your  
    # viewers supposed to know which data set you used?  
    # legend inside
#    if len(figfooter)>0:
#        ax.text(xlim_min_matplotlib-(xlim_max_matplotlib - xlim_min_matplotlib)*0.05*10/figsize[0], ylim_min-(ylim_max - ylim_min)*0.08*5.25/figsize[1], figfooter, fontsize=10)
    # legend outside
#    print ax.get_legend()._loc
    if len(figfooter)>0:
        if len(leglabels)/float(legcols) <= 1:
            font_prop.set_size(figfooterFontSize)
            ax.text(xlim_min_matplotlib-(xlim_max_matplotlib - xlim_min_matplotlib)*0.05*10/figsize[0],
                    ylim_min -(ylim_max - ylim_min)*0.17*5.25/figsize[1]*(ticksFontSize/14.)+legpushdown*(ylim_max - ylim_min), 
                    figfooter, fontsize=figfooterFontSize,color = fontColor, fontproperties=font_prop)                        
        elif len(leglabels)/float(legcols) <= 2:
            ax.text(xlim_min_matplotlib-(xlim_max_matplotlib - xlim_min_matplotlib)*0.05*10/figsize[0],
                    ylim_min -(ylim_max - ylim_min)*0.24*5.25/figsize[1]*(ticksFontSize/14.)+legpushdown*(ylim_max - ylim_min), 
                    figfooter, fontsize=figfooterFontSize,color = fontColor, fontproperties=font_prop)            
        elif len(leglabels)/float(legcols) <= 3:
            ax.text(xlim_min_matplotlib-(xlim_max_matplotlib - xlim_min_matplotlib)*0.05*10/figsize[0],
                    ylim_min -(ylim_max - ylim_min)*0.31*5.25/figsize[1]*(ticksFontSize/14.)+legpushdown*(ylim_max - ylim_min), 
                    figfooter, fontsize=figfooterFontSize,color = fontColor, fontproperties=font_prop)            
        elif len(leglabels)/float(legcols) <= 4:
            ax.text(xlim_min_matplotlib-(xlim_max_matplotlib - xlim_min_matplotlib)*0.05*10/figsize[0],
                    ylim_min -(ylim_max - ylim_min)*0.38*5.25/figsize[1]*(ticksFontSize/14.)+legpushdown*(ylim_max - ylim_min), 
                    figfooter, fontsize=figfooterFontSize,color = fontColor, fontproperties=font_prop)            

    # draw shaded area
    #ehist = '14.2.2014'
    if not ehist is None: 
        if diff2axis or len(columns2axis)>0 or len(bars2axis)>0: 
            if ehist2 is None:
                if ehistjustticks:
                    ehistbottom = ax2nd.get_yticks()[0] #-5000*(10^200)    
                    ehistbarsize = ax2nd.get_yticks()[-1] - ehistbottom # 10000*(10^200)                  
                else:
                    ehistbottom = ylim2nd_min #-5000*(10^200)    
                    ehistbarsize = ylim2nd_max - ylim2nd_min # 10000*(10^200)
                barwidth0 = ax2nd.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'));
                ax2nd.bar(dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y')),ehistbarsize,width = barwidth0,color=shaded_area,edgecolor = 'none', bottom = ehistbottom,zorder=1,align='edge')    
            else:
                if ehistjustticks:
                    ehistbottom = ax2nd.get_yticks()[0] #-5000*(10^200)    
                    ehistbarsize = ax2nd.get_yticks()[-1] - ehistbottom # 10000*(10^200)                  
                else:
                    ehistbottom = ylim2nd_min #-5000*(10^200)    
                    ehistbarsize = ylim2nd_max - ylim2nd_min # 10000*(10^200)
                barwidth0 = ax2nd.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'));
                barwidth1 = ax2nd.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist2,'%d.%m.%Y'));                                          
                ax2nd.bar(dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y')),ehistbarsize,width = barwidth0,color=(220/255.,220/255.,220/255.),edgecolor = 'none', bottom = ehistbottom,zorder=2,align='edge')    
                ax2nd.bar(dts.date2num(datetime.datetime.strptime(ehist2,'%d.%m.%Y')),ehistbarsize,width = barwidth1,color=(233/255.,233/255.,233/255.),edgecolor = 'none', bottom = ehistbottom,zorder=1,align='edge')    
        else:
            if yticks_major[0]==0:
                ehistbottom = 0
                if ehistjustticks:
                    ehistbarsize = yticks_major[-1]    
                else:
                    ehistbarsize = ylim_max #10000*(10^200)
            elif yticks_major[-1]==0: 
                if ehistjustticks:
                    ehistbottom = yticks_major[0]
                else:
                    ehistbottom = ylim_min #-10000*(10^200)
                ehistbarsize = -ehistbottom
            else:
                if ehistjustticks:
                    ehistbottom = yticks_major[0] #-5000*(10^200)    
                    ehistbarsize = yticks_major[-1] - ehistbottom # 10000*(10^200)                    
                else:
                    ehistbottom = ylim_min #-5000*(10^200)    
                    ehistbarsize = ylim_max - ylim_min # 10000*(10^200)   
            if len(bars2) == 0:                    
                if ehist2 is None:
                    barwidth0 = ax.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'));
                    ax.bar(dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y')),ehistbarsize,width = barwidth0,color=shaded_area,edgecolor = 'none', bottom = ehistbottom,zorder=1,align='edge')
                else:
                    barwidth1 = ax.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'));
                    barwidth2 = ax.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist2,'%d.%m.%Y'));
                    ax.bar(dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y')),ehistbarsize,width = barwidth1,color=(220/255.,220/255.,220/255.), bottom = ehistbottom,zorder=2,edgecolor = "none",linewidth=0,align='edge')
                    ax.bar(dts.date2num(datetime.datetime.strptime(ehist2,'%d.%m.%Y')),ehistbarsize,width = barwidth2,color=(233/255.,233/255.,233/255.), bottom = ehistbottom,zorder=1,edgecolor = "none",linewidth=0,align='edge')                                                           
            else:
                barwidth1 = ax.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'))-ehistdelta-n_time_delta/2;
                barwidth2 = ax.get_xlim()[1]-dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'))-n_time_delta/2;
                ax.bar(dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'))+ehistdelta+n_time_delta/2,ehistbarsize,width = barwidth1,color=shaded_area, bottom = ehistbottom,zorder=1,alpha=0.75,edgecolor = "none",linewidth=0,align='edge')
                ax.bar(dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y'))+n_time_delta/2,ehistbarsize,width = barwidth2,color=shaded_area, bottom = ehistbottom,zorder=1,alpha=0.75,edgecolor = "none",linewidth=0,align='edge')                

#        ax.axvspan(dts.date2num(datetime.datetime.strptime(ehist,'%d.%m.%Y')),dts.date2num(datetime.datetime.strptime('15.2.3014','%d.%m.%Y')),ymin = -10^200,ymax= 10^200, facecolor=shaded_area, edgecolor=shaded_area, alpha = 1)
    for label in ax.get_xticklabels():
        font_prop.set_size(figfooterFontSize)#ticksFontSize
        label.set_fontproperties(font_prop)
    
    for label in ax.get_yticklabels():
        label.set_fontproperties(font_prop)        

    if not ax2nd is None:
        for label in ax2nd.get_xticklabels():
            label.set_fontproperties(font_prop)
        
        for label in ax2nd.get_yticklabels():
            label.set_fontproperties(font_prop)
        
    return f, ax, ax2nd


def export(prefix = 'pic', picformat = 'png', name = '' , picdpi = 300):
    if len(name)>0:
        if not ((':\\' in name) or ('\\\\' in name)):
            current_dir = os.getcwd()
            pic_file = current_dir + '\\' + name
        else:
            pic_file = name
    else:
        current_dir = os.getcwd()
        if not ((':\\' in prefix) or ('\\\\' in prefix)):
            prefix = current_dir + '\\' + prefix 
    
        pic_soubory = glob.glob(prefix + '*.' + picformat)
    
        pic_file = prefix + str(len(pic_soubory)) + '.' + picformat

    plt.savefig(pic_file, bbox_inches="tight", transparent = True, dpi=picdpi)        