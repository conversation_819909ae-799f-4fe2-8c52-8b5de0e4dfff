function decomp_explode_fulfillment(decomp_list,groups,groups_nms)

%% driver_fulfillment <stolen>

sr_opt = evalin('base','sr_opt');

new_ID              = evalin('base','new_ID');      % name of the new sz (all - forecast, filter)
old_ID              = evalin('base','old_ID');		% name of the old sz
decomp_range		= evalin('base','decomp_range');% range of matrix
limit				= evalin('base','limit');		% computation error limit
twostages_ID		= evalin('base','twostages_ID');% name of the stage simulation; if empty string - no two stages
recompute           = 0;						    % 0 - try to find already computed items, 1 - recompute all

displaygroups       = 1;

%% report options

grouping_type       = evalin('base','grouping_type');	% 1 - Zuzka - READ_GROUPS.M 
                                                        % 0 - Franta - SET_GROUPS.M
autogroups			= 'MANUAL';	
output_ID			= evalin('base','output_ID'); % this string is added to saved report and xls name; could be also empty string
                                                
% Following list of contributions is compulsory for filling in output tables in excel.
% It uses groups names which must be in ODPOCET format! 
% odpocetlist = {	...
% 		'mod_TO_dot_cpi4'
% 		'is_TO_dot_cpi4'
% 		'foreign_TO_dot_cpi4'
% 		'reg_TO_dot_cpi4'
% 		'rest_TO_dot_cpi4'
% 		'mod_TO_i'
% 		'is_TO_i'
% 		'foreign_TO_i'
% 		'reg_TO_i'
% 		'rest_TO_i'
% 				};

doXLS				= 0;						% export contributions in .xls format
doReport			= 0;						% report in PDF
doGraphs			= 1;						% MATLAB graphs
graph_style         = evalin('base','graph_style');	% 1 - new, 0 - old
click_legend        = 1;                        % Turn on/off onClick feature for detailed decomposition

language			= evalin('base','language');						% language version EN/CZ
plot_range          = evalin('base','plot_range');	% range which will be depicted 
												% for exporting to table must be range of fullfilment forecast but starts -1 in history

% show_modelchange	= evalin('base','show_modelchange');						% shows contribution of model changes in graphs and reports
% new_orig_ID			= '2014sz04_interveneBT_Agr_dolep27';				% original name of the new sz (before model change)
% old_orig_ID			= '2013sz01';				% original name of the old sz (before model change)

%% export options

% doPrepareGraphs		= 0; % if you want to check graphs without exporting
% 
% doUpdateXLS			= 0;
% Tablesfile = absolute_path(['..\..\..\..\Tables\TablesforText-2014sz05.xlsm']);
% 
% doExportDOC			= 1;
% Textfile = absolute_path(['..\..\..\..\Text\Plneni-2014sz05.docx']);
% 
% doExportPPT			= 0;
% Pptfile = absolute_path(['..\..\..\..\Presentations\Plneni-2014sz05.pptx']);


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% END OF INPUT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% COMPUTE THE DECOMPOSITION
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% if ~isempty(twostages_ID)
% 	
% 	if strcmp(old_ID,'SS')
% 		error('Two-stage decomposition not valid for SS decomposition!');
% 	else
% 		[sr_opt.new, sr_opt.old, sr_opt.cmp] = sr_options_get_for_compare(new_ID,old_ID);
% 	end
% 	
% 	[decomp_tuneold1, ...
% 	decomp_tunenew1, ...
% 	decomp_tunerev1, ...         
% 	decomp_rev1, ...
% 	decomp_rls1, ...
% 	decomp_fcastold1, ...
% 	decomp_fcastnew1, ...
% 	decomp_ini1, ...
% 	decomp_type1, ...
% 	d_pure_new1, ...
% 	d_pure_old1, ...
% 	options1, sr_opt1] = run_decomposition(new_ID, twostages_ID, ...
% 		'decomp_range',decomp_range, ...
% 		'plan_type',11, ...
% 		'detail_level',1,...
% 		'ini2obs',0, ...
% 		'obs_free',0, ...
% 		'just_filter',0, ...
% 		'rls2shocks',0, ...
% 		'obs2shocks',0, ...
% 		'recompute',recompute, ...
% 		'limit',limit, ...
% 		'show_modelchange',show_modelchange);
% 
% 	decomp1 = merge_decomposition(new_ID, twostages_ID, options1.decomp_range, sr_opt1, ...
% 		decomp_tuneold1, decomp_tunenew1, decomp_tunerev1, decomp_rev1, decomp_rls1, ...
% 		decomp_fcastold1, decomp_fcastnew1, decomp_ini1, d_pure_new1, d_pure_old1, ...
% 		11, decomp_type1, 1, limit, show_modelchange);
% 	
% 	[decomp_tuneold2, ...
% 	decomp_tunenew2, ...
% 	decomp_tunerev2, ...         
% 	decomp_rev2, ...
% 	decomp_rls2, ...
% 	decomp_fcastold2, ...
% 	decomp_fcastnew2, ...
% 	decomp_ini2, ...
% 	decomp_type2, ...
% 	d_pure_new2, ...
% 	d_pure_old2, ...
% 	options2, sr_opt2] = run_decomposition(twostages_ID, old_ID, ...
% 		'decomp_range',decomp_range, ...
% 		'plan_type',11, ...
% 		'detail_level',1,...
% 		'ini2obs',1, ...
% 		'obs_free',1, ...
% 		'just_filter',0, ...
% 		'rls2shocks',0, ...
% 		'obs2shocks',0, ...
% 		'recompute',recompute, ...
% 		'limit',limit, ...
% 		'show_modelchange',show_modelchange);
% 
% 	decomp2 = merge_decomposition(twostages_ID, old_ID, options2.decomp_range, sr_opt2, ...
% 		decomp_tuneold2, decomp_tunenew2, decomp_tunerev2, decomp_rev2, decomp_rls2, ...
% 		decomp_fcastold2, decomp_fcastnew2, decomp_ini2, d_pure_new2, d_pure_old2, ...
% 		11, decomp_type2, 1, limit, show_modelchange);
% 	
% 	d_pure_new = d_pure_new1;
% 	d_pure_old = d_pure_old2;
% 	
% 	decomp = merge_decomp_stages(decomp1,decomp2);
% 	
% else
% 	
% 	[decomp_tuneold, ...
% 	decomp_tunenew, ...
% 	decomp_tunerev, ...         
% 	decomp_rev, ...
% 	decomp_rls, ...
% 	decomp_fcastold, ...
% 	decomp_fcastnew, ...
% 	decomp_ini, ...
% 	decomp_type, ...
% 	d_pure_new, ...
% 	d_pure_old, ...
% 	options, sr_opt] = run_decomposition(new_ID, old_ID, ...
% 		'decomp_range',decomp_range, ...
% 		'plan_type',11, ...
% 		'detail_level',1,...
% 		'ini2obs',0, ...
% 		'obs_free',0, ...
% 		'just_filter',0, ...
% 		'rls2shocks',0, ...
% 		'obs2shocks',0, ...
% 		'recompute',recompute, ...
% 		'limit',limit, ...
% 		'show_modelchange',show_modelchange);
% 
% 	decomp = merge_decomposition(old_ID, new_ID, options.decomp_range, sr_opt, ...
% 		decomp_tuneold, decomp_tunenew, decomp_tunerev, decomp_rev, decomp_rls, ...
% 		decomp_fcastold, decomp_fcastnew, decomp_ini, d_pure_new, d_pure_old, ...
% 		11, decomp_type, 1, limit, show_modelchange);
% 	
% end


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% PREPARE BASIC GROUPS AND SAVE OUTPUT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% Data of contributions saved in Output-data/[contrib.mat] file

% if (doUpdateXLS || doExportDOC || doExportPPT || doPrepareGraphs) && (doXLS == 0)
% 	disp('Exporting contributions to xls turned on.');
% 	doXLS = 1;
% end
% 
% if doGraphs % actual number of figures
% 	fignum = length(decomp_list);
% else
% 	fignum = 0;
% end

decomp =evalin('base','decomp');

[contrib, groups_nms] = report_decomposition(decomp, sr_opt, plot_range, ...
	'decomp_list',decomp_list, ...
	'grouping_type',grouping_type, ...
	'autogroups',autogroups, ...
	'firstgroups',0,...
	'xls',doXLS, ...
	'report',doReport, ...
	'graphs',doGraphs, ...
	'grip',0, ...
	'output_id',output_ID, ...
	'language',language, ...
	'zz_transform',1, ...
    'graph_style', graph_style, ...
    'manual_groups_names', groups_nms, ...
    'manual_groups', groups, ...
    'displaygroups', displaygroups, ...
    'click_legend', click_legend);

end %<eof>
