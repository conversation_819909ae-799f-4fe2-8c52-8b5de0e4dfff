﻿# -*- coding: utf-8 -*-
"""
Created on Tue Mar 31 16:19:05 2020

@author: U06541
@troubleshooting: <PERSON><PERSON>@cnb.cz
"""


import datetime as dt
import pandas as pd
import collections
import cx_Oracle


# --------------------------------------------------------------------------- #

def getTseriesFromDBASE(tseries,
                        database,
                        sdate='1996',
                        edate='2027'
                        ):
    
    '''
    This function exports data from the SQL database into a DataFrame object. Only
    data with same frequencies can be included in one DataFrame object.
    
    Inputs of the function are:
        tseries:  name of the time series in the form of a string within a list; 
                  if more than one time series is needed, the input is separated 
                  by comma as follows ['tseries_1','tseries_2',...]
        database: name of the snapshot/version in the form of a string/integer within a list; 
                  if more than one snapshot/version is needed, the input is separated 
                  by comma as follows ['database_1','database_2',...];
                  if the list input is left with an empty string, [''],
                  the function returns the latest version of the time series;
                  both mentioned approaches can be combined, for example, ['database_1', ''];
                  the list cannot contain the same inputs (for example, ['database_1', 'database_1'] returns Error)
        sdate:    starting year in the form of a string; fixed at 1996
        edate:    ending year in the form of a string; fixed at 2025
    
    Example:
        getTseriesFromDBASE(['infKorxphNtxp_yoy_qq_nso_412'], database = ['2019sz01_database',4,''])
        The function returns the time series named 'infKorxphNtxp_yoy_qq_nso_412':
            1) from the snapshot named '2019sz01_database',
            2) in the version no. 4,
            3) in the latest version in the database.
    
    Nov 2020 Jan Zacek    
    
    '''
        
    databaseCount = len(database)
    tseriesCount  = len(tseries)
    
    sqlSelectQuery = [0]*tseriesCount*databaseCount
                                           
    sqlSelectQueryCount = len(sqlSelectQuery)
    listNamesOrig       = [0]*sqlSelectQueryCount
    listNames           = [0]*sqlSelectQueryCount
    listNamesDatab      = [0]*sqlSelectQueryCount

    if databaseCount > 1:
        uniqueness_checkDB = len(set(database)) == databaseCount
            
        if uniqueness_checkDB == False:
            print('\u001b[31m' + 'Error: Some elements in the list of databases are equal!' + '\u001b[0m')
    
    if tseriesCount > 1:
        uniqueness_checkTS = len(set(tseries)) == tseriesCount
        listDuplicatesTS   = [item for item, count in collections.Counter(tseries).items() if count > 1]                        
        listToStr          = ' '.join([str(elem) for elem in listDuplicatesTS])
        
        if uniqueness_checkTS == False:
            print('\u001b[31m' + 'Error: Some elements in the list of time series are equal! | ' + listToStr + '\u001b[0m')
    
    i = 0
    while i < databaseCount:
        
        firstZeroPos = next((index for index, value in enumerate(sqlSelectQuery) if value == 0), None)
        
        if not database[i] == '':

            if isinstance(database[i], int):
                j = 0
                while j < tseriesCount:
                    sqlSelectQuery[firstZeroPos + j] = "select EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.TYP, EXM_DPSZ_LV_V.RADA, EXM_DPSZ_LV_V.HODNOTA  \
                                                        from EXM.EXM_DPSZ_LV_V \
                                                        where (EXM_DPSZ_LV_V.VERZE ='" + str(database[i]) + "' and EXM_DPSZ_LV_V.KOD = '" + tseries[j] +"') \
                                                        order by EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.RADA ASC"
                    listNames[firstZeroPos + j]      = tseries[j] + '_ver' + str(database[i])
                    listNamesOrig[firstZeroPos + j]  = tseries[j]
                    listNamesDatab[firstZeroPos + j] = str(database[i])
                    j += 1
                           
            else:
                j = 0
                while j < tseriesCount:
                    sqlSelectQuery[firstZeroPos + j] = "select EXM_DPSZ_SS_V.KOD, EXM_DPSZ_SS_V.TYP, EXM_DPSZ_SS_V.RADA, EXM_DPSZ_SS_V.HODNOTA  \
                                                        from EXM.EXM_DPSZ_SS_V \
                                                        where (EXM_DPSZ_SS_V.SNIMEK = '" + database[i] + "' and EXM_DPSZ_SS_V.KOD = '" + tseries[j] + "') \
                                                        order by EXM_DPSZ_SS_V.KOD, EXM_DPSZ_SS_V.RADA ASC"
                    listNames[firstZeroPos + j]      = tseries[j] + '_' + database[i]
                    listNamesOrig[firstZeroPos + j]  = tseries[j]
                    listNamesDatab[firstZeroPos + j] = database[i]
                    j += 1
                                
        elif database[i] == '':
            j = 0
            while j < tseriesCount:
                sqlSelectQuery[firstZeroPos + j]  = "select EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.TYP, EXM_DPSZ_LV_V.RADA, EXM_DPSZ_LV_V.HODNOTA  \
                                                     from EXM.EXM_DPSZ_LV_V \
                                                     where (EXM_DPSZ_LV_V.VERZE = (select max(EXM_DPSZ_LV_V.VERZE) from EXM.EXM_DPSZ_LV_V \
                                                     where EXM_DPSZ_LV_V.KOD = '" + tseries[j] + "') and (EXM_DPSZ_LV_V.KOD = '" + tseries[j] +"')) \
                                                     order by EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.RADA ASC"
                listNames[firstZeroPos + j]      = tseries[j]
                listNamesOrig[firstZeroPos + j]  = tseries[j]
                listNamesDatab[firstZeroPos + j] = 'the latest version'
                j += 1
            
        i += 1                                     
# Test               
#    dsn_tns = '(DESCRIPTION= (ADDRESS=(PROTOCOL=TCP)(HOST=exa1testscan.cnb.cz)(PORT=4104)) \
#               (CONNECT_DATA=(SERVICE_NAME=DWSST.cnb.cz)))'
# Production
    dsn_tns = '(DESCRIPTION_LIST=(LOAD_BALANCE=off)(FAILOVER=on)(DESCRIPTION= \
               (CONNECT_TIMEOUT=5)(TRANSPORT_CONNECT_TIMEOUT=3)(RETRY_COUNT=3) \
               (ADDRESS_LIST=(LOAD_BALANCE=on) \
               (ADDRESS=(PROTOCOL=TCP)(HOST=exa0prodscan.cnb.cz)(PORT=4103))) \
               (CONNECT_DATA=(SERVICE_NAME=F_DWSSP.cnb.cz))) \
               (DESCRIPTION= \
               (CONNECT_TIMEOUT=5)(TRANSPORT_CONNECT_TIMEOUT=3)(RETRY_COUNT=3) \
               (ADDRESS_LIST=(LOAD_BALANCE=on) \
               (ADDRESS=(PROTOCOL=TCP)(HOST=exa1prodscan.cnb.cz)(PORT=4103))) \
               (CONNECT_DATA=(SERVICE_NAME=F_DWSSP.cnb.cz))))'
    
    conn = cx_Oracle.connect(dsn=dsn_tns)
    
    if conn.ping() == None:
        print('Info: SQL database connected!')
    else:
        print('\u001b[31m' + 'Error: SQL database not connected!' + '\u001b[0m')
        
    try:
        k = 0
        while k < sqlSelectQueryCount:                
            outDataPom = pd.read_sql_query(sqlSelectQuery[k], conn)
            if k == 0:
                outData = createEmptyDF(outDataPom, sdate, edate)
                
            outDataPom = assignDateToDF(outDataPom)
            outDataPom = outDataPom.rename(columns={'HODNOTA': listNames[k]})
            
            if outDataPom.iloc[0,1] == 'D':
                outDataPom.drop(['KOD', 'TYP'], axis=1, inplace=True)
            else:    
                outDataPom.drop(['KOD', 'TYP', 'RADA'], axis=1, inplace=True)

            vars()["outData" + str(k)] = outDataPom
            k += 1
    
        conn.close()
        print('Info: SQL database disconnected!')
        
        outDataForMerge = [0]*sqlSelectQueryCount
        l = 0
        while l < sqlSelectQueryCount:
            outDataForMerge[l] = eval(("outData" + str(l)))
            l += 1
        
        m = 0
        while m < len(outDataForMerge):
            outData = outData.join(outDataForMerge[m], lsuffix='', rsuffix='')
            m += 1
        
        outData.drop('Empty', axis=1, inplace=True)
        
        print('\u001b[32m' + 'Info: Time series successfully exported!' + '\u001b[0m')
        
    except:
        conn.close()
        print('Info: SQL database disconnected!')
        print('\u001b[31m' + 'Error: Time series not exported!' + '\u001b[0m')
        print('\u001b[31m' + 'Error: Invalid or suspicious entry - ' + listNamesOrig[k] + ' | ' + listNamesDatab[k] + '\u001b[0m')
    
    return outData


# --------------------------------------------------------------------------- #

def createEmptyDF(dataFrame,
                  sdate,
                  edate,
                  ):

    '''
    This function creates an empty DataFrame object based on a time
    series frequency identifier.
    
    Inputs of the function are:
        dataFrame: a DataFrame object
        sdate:     starting year in the form of a string
        edate:     ending year in the form of a string
    
    Apr 2020 Jan Zacek    
    '''
    
    freq = dataFrame.iloc[0,1]
       
    start = dt.date(int(sdate),1,1)
    end   = dt.date(int(edate),12,31)
    
    if freq == 'Y':
        dateRange = pd.date_range(start = start, end = end, freq='AS', normalize = True)
    elif freq == 'Q':
        dateRange = pd.date_range(start = start, end = end, freq='QS', normalize = True)
    elif freq == 'M':
        dateRange = pd.date_range(start = start, end = end, freq='MS', normalize = True)
    elif freq == 'D':
        dateRange = pd.date_range(start = start, end = end, freq='B', normalize = True)
    else:
        print('\u001b[31m' + 'Error: Empty dataframe not created!' + '\u001b[0m')

    outData  = pd.DataFrame(index=dateRange, columns=['Empty'])     
        
    return outData


# --------------------------------------------------------------------------- #

def assignDateToDF(dataFrame
                   ):
    
    '''
    This function assigns data frequency to a DataFrame object based on a time
    series frequency identifier.
    
    Inputs of the function are:
        dataFrame: a DataFrame object
    
    Apr 2020 Jan Zacek    
    '''

    freq = dataFrame.iloc[0,1]    

    try:
        if freq == 'D':
#            dataFrame['RADA'].str.replace('D','-', inplace = True)
            dataFrame['RADA'] = dataFrame['RADA'].str[:4] + '-' + dataFrame['RADA'].str[5:7] + '-' + dataFrame['RADA'].str[7:9]
#            ii = 0
#            while ii < len(dataFrame):
#                dateToChange = dataFrame.iloc[ii,2]
#                try:
#                    year  = dateToChange[:4]
#                    month = dateToChange[5:7]
#                    day   = dateToChange[7:9]
#                    dataFrame.iloc[ii,2] = year + '-' + month + '-' + day
#                except:
#                    print('Warning: DataFrame dates are incomplete!')
#                ii += 1
            
            dataFrame['RADA'] = dataFrame['RADA'].astype('datetime64[ns]')
            dataFrame.set_index('RADA',inplace = True)
            dataFrame.sort_index(axis = 0, inplace = True)
    
#             print('Info: Data frequency recognized!')
#             print('Info: Data frequency assigned!')
            
        elif freq in ['M','Q','Y']:
            dateString = dataFrame.iloc[0,2]
            day = 1
            year = int(dateString[:4])
            
            if freq == 'M':
                month = int(dateString[5:])
            elif freq == 'Q':
                month = int(dateString[5:]) + 2*(int(dateString[5:])-1)
            elif freq == 'Y':
                month = 1 
            
            start    = dt.date(year,month,day)
            dfPeriod = len(dataFrame)
    
#             print('Info: Data frequency recognized!')
    
    except:
        print('\u001b[31m' + 'Error: Data frequency not defined!' + '\u001b[0m')

    if freq in ['M','Q','Y']:
        try:
            if freq == 'M':
                dateRange = pd.date_range(start, periods = dfPeriod, freq='MS', normalize = True)  
            elif freq == 'Q':
                dateRange = pd.date_range(start, periods = dfPeriod, freq='QS', normalize = True)
            elif freq == 'Y':
                dateRange = pd.date_range(start, periods = dfPeriod, freq='AS', normalize = True)
            
            dataFrame = dataFrame.set_index(dateRange)
            
#             print('Info: Data frequency assigned!')
                
        except:
            print('\u001b[31m' + 'Error: Data frequency not assigned!' + '\u001b[0m')
    
    return dataFrame


# --------------------------------------------------------------------------- #

def getTseriesFromARAD(tseries
                       ):
    
    '''
    This function exports data from ARAD database into a DataFrame object. It uses
    it's own dictionary to access individual time series (see below).
    
    Inputs of the function are:
        tseries:  name of the time series (as given by dictionary) in the form 
                  of a string within a list; if more than one time series is 
                  needed, the input is separated by a comma as follows 
                  ['tseries_1','tseries_2',...]
    
    Dictionary:
        Repo2T_endMonth: Oficiální úrokové sazby ČNB (ke konci měsíce) (%) ### Repo sazba - 2 týdny (%) \ Official CNB's interest rates (end of month)  (%) ### Repo rate - 2 weeks (%)
        Repo2T_avgMonth: Oficiální úrokové sazby ČNB (měsíční průměr) (%) ### Repo sazba - 2 týdny (%) \ Official CNB's interest rates (monthly average)  (%) ### Repo rate - 2 weeks (%)            
        PRIBOR:          PRIBOR (měsíční průměr)  (%) ### 3 měsíce  (%) \ PRIBOR (monthly average)  (%) ### 3 months (%)
        CZEONIA:         CZEONIA (měsíční průměr)  (%) ### Sazba (%) \ CZEONIA (monthly average)  (%) ### Rate (%)
        IR_HHsNPISH:     Tabulka B1.1.2: Úrokové sazby korunových úvěrů poskytnutých bankami domácnostem v ČR - nové obchody (%) ### Domácnosti a NISD (S.14+S.15) - na spotřebu, bydlení a ostatní - celkem \ Table B1.1.2: Bank interest rates on CZK-denominated loans by Czech households - new business ### Households and NPISH (S.14+S.15) - consumer credit,  loans for house purchase and other loans - total 
        IR_NFCs:         Tabulka B1.1.3: Úrokové sazby korunových úvěrů poskytnutých bankami nefinančním podnikům v ČR - nové obchody (%) ### Nefinanční podniky (S.11) - úvěry celkem bez kontokorentů, revolvingových úvěrů a pohledávek z kreditních karet \ Table B1.1.3: Bank interest rates on CZK-denominated loans by Czech non-financial corporations - new business (%) ### Non-financial corporations (S.11) - loans without overdraft, credit card and revolving                                                      
        IRQ_HHsNPISH:    Tabulka B1.1.2Q: Čtvrtletní úrokové sazby korunových úvěrů poskytnutých bankami domácnostem v ČR - nové obchody (%) ### Domácnosti a NISD (S.14+S.15) - na spotřebu, bydlení a ostatní - celkem \ Table B1.2: Bank interest rates on CZK-denominated loans by Czech households - new business ### Households and NPISH (S.14+S.15) - consumer credit,  loans for house purchase and other loans - total
        IRQ_NFCs:        Tabulka B1.1.3Q: Čtvrtletní úrokové sazby korunových úvěrů poskytnutých bankami nefinančním podnikům v ČR - nové obchody (%) ### Nefinanční podniky (S.11) - úvěry celkem bez kontokorentů, revolvingových úvěrů a pohledávek z kreditních karet \ Table B1.1.3Q: Quarterly bank interest rates on CZK-denominated loans by Czech non-financial corporations - new business (%) ### Non-financial corporations (S.11) - loans without overdrafts, revolving loans and credit cards                                                                                                               
        IRvol_HHsNPISH:  Objemy pro B1.1.2: Úrokové sazby korunových úvěrů poskytnutých bankami domácnostem v ČR - nové obchody ### Domácnosti a NISD (S.14+S.15) - na spotřebu, bydlení a ostatní - celkem \ Volumes for B1.1.2: Bank interest rates on CZK-denominated loans by Czech households - new business ### Households and NPISH (S.14+S.15) - consumer credit,  loans for house purchase and other loans - total                                                                  
        IRvol_NFCs:      Objemy pro B1.1.3: Úrokové sazby korunových úvěrů poskytnutých bankami nefinančním podnikům v ČR - nové obchody ### Nefinanční podniky (S.11) - úvěry celkem bez kontokorentů, revolvingových úvěrů a pohledávek z kreditních karet \ Volumes for B1.1.3: Bank interest rates on CZK-denominated loans by Czech non-financial corporations - new business ### Non-financial corporations (S.11) - loans without overdraft, credit card and revolving
    
    Example:
        getTseriesFromARAD(['Repo2T_endMonth'])
        The function returns the time series named 'Repo2T_endMonth' (see the dictionary above) from the ARAD database.
         
    Apr 2020 Jan Zacek    
    '''
    
    tseriesCount  = len(tseries)
    sqlSelectQuery = [0]*tseriesCount
    
    i = 0
    while i < tseriesCount:
        
        if tseries[i] == 'Repo2T_endMonth':
            sestID     = 1061
            sestTab    = 'CNBIRA'
            tableID    = 1
        elif tseries[i] == 'Repo2T_avgMonth':
            sestID     = 1062
            sestTab    = 'CNBIRA'
            tableID    = 1
        elif tseries[i] == 'PRIBOR':
            sestID     = 1064
            sestTab    = 'PRIB'
            tableID    = 6
        elif tseries[i] == 'CZEONIA':
            sestID     = 1066
            sestTab    = 'CZEON'
            tableID    = 1
        elif tseries[i] == 'IR_HHsNPISH':
            sestID     = 934
            sestTab    = 'IRUNO'
            tableID    = 1
        elif tseries[i] == 'IR_NFCs':
            sestID     = 935
            sestTab    = 'IRUNO'
            tableID    = 1
        elif tseries[i] == 'IRQ_HHsNPISH':
            sestID     = 937
            sestTab    = 'IRUNOQ'
            tableID    = 1
        elif tseries[i] == 'IRQ_NFCs':
            sestID     = 938
            sestTab    = 'IRUNOQ'
            tableID    = 1
        elif tseries[i] == 'IRvol_HHsNPISH':
            sestID     = 942
            tableID    = 1
        elif tseries[i] == 'IRvol_NFCs':
            sestID     = 943
            tableID    = 1
        else:
            print('\u001b[31m' + 'Error: Time series not defined in the dictionary!' + '\u001b[0m')
        
        if tseries[i] in ['Repo2T_endMonth', 'Repo2T_avgMonth','PRIBOR','CZEONIA','IR_HHsNPISH', 'IR_NFCs','IRQ_HHsNPISH','IRQ_NFCs']:
            
            if tseries[i] in ['Repo2T_endMonth', 'Repo2T_avgMonth','PRIBOR','CZEONIA']:
                sqlSubstring = ''
            elif tseries[i] in ['IR_HHsNPISH', 'IR_NFCs','IRQ_HHsNPISH','IRQ_NFCs']:
                sqlSubstring = 'AND d.mena=r.mena'
            
            sqlSelectQuery[i] = "SELECT TO_DATE(data.obdobi,'YYYYMMDD') AS RADA, \
                                MAX(DECODE(c_uka," + str(tableID) + ",hodnota)) AS KOD FROM \
                                ( \
                                SELECT \
                                s.c_uka, \
                                TO_CHAR (d.obdobi,'YYYYMMDD') obdobi, \
                                SUM(d.hodnota*r.znam*s.znam*POWER(10,-r.nasob)) hodnotaD, \
                                SUM(d.hodnota*r.znam*s.znam*POWER(10,-r.nasob)) AS hodnota \
                                ,d.conf \
                                FROM arady.TP_" + sestTab + " d, \
                                (SELECT u.id as c_uka,i.cislo AS radek,i.znamenko znam,u.mena_data mena,u.nasobek nasob,u.desetiny dec,u.conf \
                                FROM arady.p_ukars i,arady.p_ukazatele u \
                                WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id= " + str(sestID) + " AND  SYSDATE BETWEEN plati_od AND plati_do) \
                                AND u.u_id=i.uka_uid \
                                AND u.zdroj_id='" + sestTab + "' \
                                AND i.rs=1) r, \
                                (SELECT u.id as c_uka,i.cislo AS sloupec,i.znamenko znam \
                                FROM arady.p_ukars i,arady.p_ukazatele u \
                                WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id= " + str(sestID) + " AND  SYSDATE BETWEEN plati_od AND plati_do) \
                                AND u.u_id=i.uka_uid \
                                AND u.zdroj_id='" + sestTab + "' \
                                AND i.rs=2)s \
                                WHERE r.c_uka=s.c_uka AND d.radek=r.radek AND d.sloupec=s.sloupec \
                                AND s.c_uka IN (" + str(tableID) + ") \
                                "+ sqlSubstring + "\
                                GROUP BY s.c_uka,d.obdobi,d.conf \
                                ) data,  \
                                arady.p_ukazatele uka, \
                                arady.p_sestavy sest \
                                WHERE sest.u_id=uka.sest_uid \
                                AND sest.id= " + str(sestID) + " \
                                AND data.c_uka=uka.id \
                                AND SYSDATE BETWEEN sest.plati_od AND sest.plati_do \
                                GROUP BY data.obdobi,sest.id \
                                ORDER BY RADA ASC"        
        
        elif tseries[i] in ['IRvol_HHsNPISH', 'IRvol_NFCs']:
            sqlSelectQuery[i] = "SELECT TO_DATE(data.obdobi,'YYYYMMDD') AS RADA, \
                                MAX(DECODE(c_uka," + str(tableID) + ",hodnota)) AS KOD \
                                FROM \
                                ( \
                                SELECT \
                                s.c_uka, \
                                TO_CHAR (d.obdobi,'YYYYMMDD') obdobi, \
                                SUM(d.hodnota*r.znam*s.znam*POWER(10,-r.nasob)) hodnotaD, \
                                SUM(d.hodnota*r.znam*s.znam*POWER(10,-r.nasob)) AS hodnota \
                                ,d.conf \
                                FROM arady.TP_GUIA1 d, \
                                (SELECT u.id as c_uka,i.cislo AS radek,i.znamenko znam,u.mena_data mena,u.nasobek nasob,u.desetiny dec,u.conf \
                                FROM arady.p_ukars i,arady.p_ukazatele u \
                                WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id=" + str(sestID) + " AND  SYSDATE BETWEEN plati_od AND plati_do) \
                                AND u.u_id=i.uka_uid \
                                AND u.zdroj_id='GUIA1' \
                                AND i.rs=1) r, \
                                (SELECT u.id as c_uka,i.cislo AS sloupec,i.znamenko znam \
                                FROM arady.p_ukars i,arady.p_ukazatele u \
                                WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id=" + str(sestID) + " AND  SYSDATE BETWEEN plati_od AND plati_do) \
                                AND u.u_id=i.uka_uid \
                                AND u.zdroj_id='GUIA1' \
                                AND i.rs=2)s \
                                WHERE r.c_uka=s.c_uka AND d.radek=r.radek AND d.sloupec=s.sloupec \
                                AND s.c_uka IN (" + str(tableID) + ") \
                                GROUP BY s.c_uka,d.obdobi,d.conf \
                                UNION ALL \
                                SELECT \
                                s.c_uka, \
                                TO_CHAR (d.obdobi,'YYYYMMDD') obdobi, \
                                SUM(d.hodnota*r.znam*s.znam*POWER(10,-r.nasob)) hodnotaD, \
                                SUM(d.hodnota*r.znam*s.znam*POWER(10,-r.nasob)) AS hodnota \
                                ,d.conf \
                                FROM arady.TP_IRUNO d, \
                                (SELECT u.id as c_uka,i.cislo AS radek,i.znamenko znam,u.mena_data mena,u.nasobek nasob,u.desetiny dec,u.conf \
                                FROM arady.p_ukars i,arady.p_ukazatele u \
                                WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id=" + str(sestID) + " AND  SYSDATE BETWEEN plati_od AND plati_do) \
                                AND u.u_id=i.uka_uid \
                                AND u.zdroj_id='IRUNO' \
                                AND i.rs=1) r, \
                                (SELECT u.id as c_uka,i.cislo AS sloupec,i.znamenko znam \
                                FROM arady.p_ukars i,arady.p_ukazatele u \
                                WHERE u.sest_uid=(SELECT DISTINCT u_id FROM arady.p_sestavy WHERE id= " + str(sestID) + " AND  SYSDATE BETWEEN plati_od AND plati_do) \
                                AND u.u_id=i.uka_uid \
                                AND u.zdroj_id='IRUNO' \
                                AND i.rs=2)s \
                                WHERE r.c_uka=s.c_uka AND d.radek=r.radek AND d.sloupec=s.sloupec \
                                AND s.c_uka IN (" + str(tableID) + ") \
                                AND d.mena=r.mena \
                                GROUP BY s.c_uka,d.obdobi,d.conf \
                                ) data, \
                                arady.p_ukazatele uka, \
                                arady.p_sestavy sest \
                                WHERE sest.u_id=uka.sest_uid \
                                AND sest.id= " + str(sestID) + " \
                                AND data.c_uka=uka.id  \
                                AND SYSDATE BETWEEN sest.plati_od AND sest.plati_do \
                                GROUP BY data.obdobi,sest.id \
                                ORDER BY RADA ASC"
        
        i += 1
    
    dsn_tns = '(DESCRIPTION_LIST= (LOAD_BALANCE=off)(FAILOVER=on)(DESCRIPTION= \
               (CONNECT_TIMEOUT=5)(TRANSPORT_CONNECT_TIMEOUT=3)(RETRY_COUNT=3) \
               (ADDRESS_LIST=(LOAD_BALANCE=on)(ADDRESS=(PROTOCOL=TCP)(HOST=exa1prodscan.cnb.cz) \
               (PORT=4103)))(CONNECT_DATA=(SERVICE_NAME=F_STATIC.cnb.cz))) \
               (DESCRIPTION=(CONNECT_TIMEOUT=5)(TRANSPORT_CONNECT_TIMEOUT=3)(RETRY_COUNT=3) \
               (ADDRESS_LIST=(LOAD_BALANCE=on)(ADDRESS=(PROTOCOL=TCP)(HOST=exa0prodscan.cnb.cz)\
               (PORT=4103)))(CONNECT_DATA=(SERVICE_NAME=F_STATIC.cnb.cz))))'
    conn = cx_Oracle.connect(dsn=dsn_tns)                           

    if conn.ping() == None:
        print('Info: SQL database connected!')
    else:
        print('\u001b[31m' + 'Error: SQL database not connected!' + '\u001b[0m')
        
    try:
        k = 0
        while k < tseriesCount:                
            outDataPom = pd.read_sql_query(sqlSelectQuery[k], conn, index_col = 'RADA')
            outDataPom = outDataPom.rename(columns={"KOD": tseries[k]})
            if tseries[k] in ['IRvol_HHsNPISH', 'IRvol_NFCs']:
                outDataPom = outDataPom*1000000
            vars()["outData" + str(k)] = outDataPom
            k += 1
    
        conn.close()
        print('Info: SQL database disconnected!')
        
        if tseriesCount == 1:
            outData = eval(("outData"+ str(0)))
        else:
            outDataForMerge = [0]*tseriesCount
            l = 0
            while l < tseriesCount:
                outDataForMerge[l] = eval(("outData" + str(l)))
                l += 1
                
            outData = max(outDataForMerge, key=len) 
            outDataCheck = outData
            
            m = 0
            while m < len(outDataForMerge):
                if outDataCheck.columns.values.tolist() != outDataForMerge[m].columns.values.tolist():
                    outData = outData.join(outDataForMerge[m], lsuffix='', rsuffix='')
                m += 1
        
        print('Info: Time series successfully exported!')
        
    except:
        conn.close()
        print('Info: SQL database disconnected!')
        print('\u001b[31m' + 'Error: Time series not exported!' + '\u001b[0m')
    
    return outData