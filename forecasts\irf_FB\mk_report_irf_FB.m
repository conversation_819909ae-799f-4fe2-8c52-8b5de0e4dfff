function mk_report_irf(irf,shockType,shockList,specShockList,responseList,specResponseList, ...
                  modelNick,sr_opt,expectations,reportName)

% Reports impulse responses in pdf/ps format

%% Open report

% x = report('heading', 'Impulse Response Functions', 'dateformat', 'YY:P', 'color', true, ...
%     'visible', false, 'fontsize', 10);

if ~length(sr_opt.formatSubplot)==2
	sr_opt.formatSubplot = [1,1];
end
numSubplot = sr_opt.formatSubplot(1)*sr_opt.formatSubplot(2);

x = report.new('Impulse Response Functions');

modelID     = fieldnames(irf);
typeShockID = fieldnames(irf.(modelID{1}));
hitPeriodID = fieldnames(irf.(modelID{1}).(typeShockID{1}));
hitPeriodID = hitPeriodID(1:length(sr_opt.hitPeriod));

colors_def = [0 0 1;
              0 0.5 0;
              1 0 0;
              0.75 0 0.75;              
              0.5273    0.8047    0.9792
              0 0 0];

sty = struct(); 
sty.line.lineWidth = 1;
sty.line.Color = {}; 
for i1 = 1:length(modelID)
    for i2 = 1:length(hitPeriodID)
        sty.line.Color{(i1-1)*length(hitPeriodID)+i2}=colors_def(i1,:)+(i2-1)/(length(hitPeriodID))*([1 1 1]-colors_def(i1,:));
    end
end
sty.line.Color{length(modelID)*length(hitPeriodID)+1}=colors_def(end,:);
sty.legend.location = 'NorthEast';
% sty.legend.Orientation = 'Horizontal';

% expectation scheme definition

for ii = 2:length(expectations.(modelID{1}).cluster)
    expect_scheme_list = [expectations.(modelID{1}).cluster{ii-1} expectations.(modelID{1}).cluster{ii}];
end

if sr_opt.varTransform
	strVarTrans = 'zz_';
else
	strVarTrans = '';
end

if sr_opt.bible ~= 1 % NOT a Bible
   post = [ ...
            'leg = findobj(gcf,''Location'',''SouthOutside'');'...
            'set(leg,''location'',''none'');',...
            'posleg = get(leg,''position'');', ...
            'pos = get(H,''position'');', ...
            'k = 0.77;', ...
            'set(leg,''position'',[posleg(1)+0.4*k,pos(2)-k,posleg(3),posleg(4)]);']; 
    if sr_opt.one_graph_compare ~= 1
        
        for i = 1 : length(shockList)
            for l = 1 : length(typeShockID)
                
                if length(modelID) > 1
                    
                    for j = 1 : length(responseList)
                        modID = '';
                        for k = 1 : length(modelID)
                            if ~ismember(shockList{i},specShockList.(modelID{k})) && ...
                                    ~ismember(responseList{j},specResponseList.(modelID{k}))
                                modID = ['m',num2str(k,'%02.0f')];
                            end
                        end
                        if ~isempty(modID)
                            if sr_opt.varnamedisp
                                if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                                pageTitle = ['Fully ' typeShockID{l}(1) typeShockID{l}(2:end) 'ed ' ...
                                    shockList{i}  ' to ' responseList{j}];    
                                else
                                pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                                    shockList{i}  ' to ' responseList{j}];
                                end
                            else
                                if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                                 pageTitle = ['Fully ' typeShockID{l}(1) typeShockID{l}(2:end) 'ed ' ...
                                    char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ' to ' ...
                                    char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment)];   
                                else
                                pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                                    char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ' to ' ...
                                    char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment)];
                                end
                            end
                            numEmptyPlot = 0;
                            for k = 1 : length(modelID)
                                if ~ismember(shockList{i},specShockList.(modelID{k})) && ...
                                        ~ismember(responseList{j},specResponseList.(modelID{k}))
                                    if isequal(mod(k-numEmptyPlot,numSubplot),1)
                                        x.pagebreak();
                                        x.figure(pageTitle,'subplot',sr_opt.formatSubplot,'style', sty,'range',1:sr_opt.responseLength);
                                    end
                                    subTitle = modelNick{k};
                                    if isequal(mod(k-numEmptyPlot,numSubplot),sr_opt.lgndInSubplot) % legend in subtitle
                                        x.graph(subTitle,'legend',true);
                                    else
                                        x.graph(subTitle,'legend',false);
                                    end
                                    for ix = 1 : length(hitPeriodID)
                                        x.series(strrep(hitPeriodID{ix},'_','\_'),irf.(modelID{k}).(typeShockID{l}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]));
                                    end
                                else
                                    numEmptyPlot = numEmptyPlot+1;
                                end
                            end
                        end
                        ts_pom = irf.(modelID{k}).(typeShockID{l}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]);
                        x.series('SS',tseries(startdate(ts_pom):enddate(ts_pom),zeros(size(ts_pom))),'legend',NaN);
                    end
                    
                else
                    
                    modID = ['m',num2str(1,'%02.0f')];
                    if sr_opt.varnamedisp
                        if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                        pageTitle = ['Fully ' typeShockID{l}(1) typeShockID{l}(2:end) 'ed ' ...
                            shockList{i} ];    
                        else
                        pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            shockList{i} ];
                        end
                    else
                        if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                        pageTitle = ['Fully ' typeShockID{l}(1) typeShockID{l}(2:end) 'ed ' ...
                            char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ];
                        else
                        pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ];    
                        end
                    end
                    for j = 1 : length(responseList)
                        if sr_opt.varnamedisp
                            subTitle = responseList{j};
                        else
                            subTitle = char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment);
                        end
                        if isequal(mod(j,numSubplot),1)
                            x.pagebreak();
                            x.figure(pageTitle,'subplot',sr_opt.formatSubplot,'style', sty,'range',1:sr_opt.responseLength);
                        end
                        if isequal(mod(j,numSubplot),sr_opt.lgndInSubplot) % legend in subtitle
                            x.graph(subTitle,'legend',true,'range', 0:40);
                        else
                            x.graph(subTitle,'legend',false,'range', 0:40);
                        end
                        for ix = 1 : length(hitPeriodID)
                            x.series(strrep(hitPeriodID{ix},'_','\_'),irf.(modID).(typeShockID{l}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]));
                        end
%                         ts_pom = irf.(modelID{k}).(typeShockID{l}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]);
%                         x.series('SS',tseries(startdate(ts_pom):enddate(ts_pom),zeros(size(ts_pom))),'legend',NaN);
                    end
                    
                end
                
            end
        end
        
    else
        
        for i = 1 : length(shockList)
            for l = 1 : length(typeShockID)
                for k = 1 : length(modelID)
                    if ~ismember(shockList{i},specShockList.(modelID{k}))
                        modID = ['m',num2str(k,'%02.0f')];
                    end
                end
                if sr_opt.varnamedisp
                    if length(hitPeriodID) == 1
                        if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                        pageTitle = ['Fully ' typeShockID{l}(1) typeShockID{l}(2:end) 'ed ' ...
                            shockList{i} ' in period ' hitPeriodID{1}(4:end)];    
                        else
                        pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            shockList{i} ' in period ' hitPeriodID{1}(4:end)];
                        end
                    else
                        if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                        pageTitle = ['Fully ' upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            shockList{i} ' in periods ' hitPeriodID{1}(4:end) ' to ' hitPeriodID{end}(4:end)];
                        else
                        pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            shockList{i} ' in periods ' hitPeriodID{1}(4:end) ' to ' hitPeriodID{end}(4:end)];    
                        end
                    end
                else
                    if length(hitPeriodID) == 1
                        if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                        pageTitle = ['Fully ' typeShockID{l}(1) typeShockID{l}(2:end) 'ed ' ...
                            char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ...
                            ' in period ' hitPeriodID{1}(4:end)];
                        else
                        pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ...
                            ' in period ' hitPeriodID{1}(4:end)];    
                        end
                    else
                        if any(strcmpi({'ef','expfull','expectfull','expectedfull'},shockType))
                        pageTitle = ['Fully' upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ...
                            ' in periods ' hitPeriodID{1}(4:end) ' to ' hitPeriodID{end}(4:end)];
                        else
                        pageTitle = [upper(typeShockID{l}(1)) typeShockID{l}(2:end) 'ed ' ...
                            char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ...
                            ' in periods ' hitPeriodID{1}(4:end) ' to ' hitPeriodID{end}(4:end)];    
                        end
                    end
                end
                numEmptyPlot = 0;
                for j = 1 : length(responseList)
                    modID = '';
                    for k = 1 : length(modelID)
                        if ~ismember(shockList{i},specShockList.(modelID{k})) && ...
                                ~ismember(responseList{j},specResponseList.(modelID{k}))
                            modID = ['m',num2str(k,'%02.0f')];
                        end
                    end
                    if isempty(modID)
                        numEmptyPlot = numEmptyPlot+1;
                    else
                        if sr_opt.varnamedisp || isempty(modID)
                            subTitle = responseList{j};
                        else
                            subTitle = char(irf.(modID).(typeShockID{l}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment);
                        end
                        if isequal(mod(j-numEmptyPlot,numSubplot),1)
                            x.pagebreak();
                            x.figure(pageTitle,'subplot',sr_opt.formatSubplot,'style', sty,'range',1:sr_opt.responseLength);
                        end
                        if isequal(mod(j-numEmptyPlot,numSubplot),sr_opt.lgndInSubplot) % legend in subtitle
                            x.graph(subTitle,'legend',true);
                        else
                            x.graph(subTitle,'legend',false);
                        end
                        for k = 1 : length(modelID)
                            for ix = 1 : length(hitPeriodID)
                                if ix == 1
                                    x.series(modelNick{k},irf.(modelID{k}).(typeShockID{l}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]));
                                else
                                    x.series(modelNick{k},irf.(modelID{k}).(typeShockID{l}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]),'legend',NaN);
                                end
                            end
                        end
                    end
                    ts_pom = irf.(modelID{k}).(typeShockID{l}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]);
                    x.series('SS',tseries(startdate(ts_pom):enddate(ts_pom),zeros(size(ts_pom))),'legend',NaN);
                end
            end
        end
        
    end
    
else % is Bible
    %%
    if length(typeShockID) ~= 2
        error('Option "bible" works only for shockType "b"!');
    else
        sty.line.lineWidth = 1.4;
        sty.line.Color = {[1 0 0] [0.96 0.35 0.35] [0 0.5 0] [0.4667 0.6745 0.1882],[0.25 0.25 0.25]};
%         sty.line.Color = {[1 0 0] [1 0.5 0.5] [0 0.5 0] [0.5 0.75 0.5],[0.25 0.25 0.25]};
        sty.highlight.faceColor = {[0.92,0.92,0.92],[0.85,0.85,0.85]};
        sty.legend.location = 'SouthOutside';
        sty.legend.Orientation = 'Horizontal';
        sty.line.lineStyle = {'-.', '-', '-.','-','-'};
        
        post = [ ...
            'leg = findobj(gcf,''Location'',''SouthOutside'');'...
            'set(leg,''location'',''none'');',...
            'posleg = get(leg,''position'');', ...
            'pos = get(H,''position'');', ...
            'k = 0.77;', ...
            'set(leg,''position'',[posleg(1)+0.4*k,pos(2)-k,posleg(3),posleg(4)]);'];
        
        warning('Graph titles set manually!')
        
        graph_titles = {'F. Output Gap', 'F. Fund. Output Trend', 'F. Output Trend','F. Output Growth','F. One-off Output Shock',...
                'F. Policy Rate','F. Neutral Policy Rate','F. Policy Effective Rate','F. Real Interest Rate Gap','F. Shadow Rate Gap',...
                'F. Core PPI Inflation (EUR)','F. Energy PPI Inflation (EUR)','F. PPI Inflation (EUR)','F. CPI Inflation (EUR)','F. Rel. Energy Price (EUR)', 'F. Energy Share of PPI Gap'...
                'Nom. Depreciation USD/EUR','USD/EUR Cross Rate','EUR/USD Exchange Rate Gap','USD/EUR Premium','RER EUR/USD Trend'};
        
        for i = 1 : length(shockList)
%             if ~any(strcmp(expect_scheme_list,char(shockList{i})))
%                 display(['Shock', char(shockList{i}),'not included in the expecations scheme, anticipated and unanticipated shocks are the same. IRF not reported.'])
%             else
                for k = 1 : length(modelID)
                    if ~ismember(shockList{i},specShockList.(modelID{k}))
                        modID = ['m',num2str(k,'%02.0f')];
                    end
                end
                pageTitle = [char(irf.(modID).(typeShockID{2}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ' (' shockList{i} ')'];
                numEmptyPlot = 0;
                for j = 1 : length(responseList)
                    modID = '';
                    for k = 1 : length(modelID)
                        if ~ismember(shockList{i},specShockList.(modelID{k})) && ...
                                ~ismember(responseList{j},specResponseList.(modelID{k}))
                            modID = ['m',num2str(k,'%02.0f')];
                        end
                    end
                    if isempty(modID)
                        numEmptyPlot = numEmptyPlot+1;
                    else
                        if sr_opt.varnamedisp || isempty(modID)
                            subTitle = responseList{j};
                        else
                            %                 subTitle = char(irf.(modID).(typeShockID{1}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment);
                            subTitle = graph_titles(j);
                        end
                        if isequal(mod(j-numEmptyPlot,numSubplot),1)
                            x.pagebreak();
                            x.figure(pageTitle,'subplot',sr_opt.formatSubplot,'style', sty,'range',1:sr_opt.responseLength);
                        end
                        if isequal(mod(j-numEmptyPlot,numSubplot),sr_opt.lgndInSubplot) % legend in subtitle
                            x.graph(subTitle,'legend',true, 'postprocess', post, 'tight', true);
                        else
                            x.graph(subTitle,'legend',false,'tight',true);
                        end
                        for k = 1 : length(modelID)
                            for ix = 1 : length(hitPeriodID)
%                                 shock_names_pom = fieldnames(irf.(modelID{k}).(typeShockID{1}).(hitPeriodID{ix}));
%                                 if length(shock_names_pom{i})>7 && sum(shock_names_pom{i}(5:7) == char('exp'))
%                                     if ix == 1
%                                         x.series([modelNick{k},' anticipated (no scheme)'],irf.(modelID{k}).(typeShockID{1}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]));
%                                         x.series([modelNick{k},' unanticipated'],irf.(modelID{k}).(typeShockID{2}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]));
%                                         x.highlight('',1:4);
%                                     else
%                                         x.series([modelNick{k},' anticipated (no scheme)'],irf.(modelID{k}).(typeShockID{1}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]),'legend',NaN);
%                                         x.series([modelNick{k},' unanticipated'],irf.(modelID{k}).(typeShockID{2}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]),'legend',NaN);
%                                     end
%                                 else
                                    if ix == 1
                                        x.series([modelNick{k},' anticipated'],irf.(modelID{k}).(typeShockID{1}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]));
                                        x.series([modelNick{k},' unanticipated'],irf.(modelID{k}).(typeShockID{2}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]));
                                        x.highlight('',1:4);
                                    else
                                        x.series([modelNick{k},' anticipated'],irf.(modelID{k}).(typeShockID{1}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]),'legend',NaN);
                                        x.series([modelNick{k},' unanticipated'],irf.(modelID{k}).(typeShockID{2}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]),'legend',NaN);
                                    end
%                                 end
                                
                            end
                        end
                    end
                    ts_pom = irf.(modelID{k}).(typeShockID{1}).(hitPeriodID{ix}).(shockList{i}).([strVarTrans responseList{j}]);
                    x.series('SS',tseries(startdate(ts_pom):enddate(ts_pom),zeros(size(ts_pom))));
                end
%             end
        end
    end
end

%% Compile report

x.publish([reportName],'maketitle=',true, 'paperSize','a4paper','display',false);


