%**************************************************************************
% DRIVER_SETTINGS
% This script sets dates, ranges, databases, ....
%**************************************************************************

%% 
close all
clear all
disp([sprintf('\n'), 'DRIVER_SETTINGS_ZB']);


%% ACTUAL FORECAST label
% prefix 
settings.report_prefix	= 'develop_estim_ppi_new-ZBestim_new';
% optional prefix extension(s)
settings.prefix_ext = { ...	
%	'NAME'				'PREFIX EXTENSION'
					};

%% OLD FORECAST label (optional)
% prefix 
settings.oldfrc_prefix	= 'develop_estim_ppi_new';

%% Databases 
% Baseline data
settings.histcore_name	= 'histcore-2022-07-19.csv';
% Adhoc data
settings.adhoc_data_fb	= '';

%% Dates
settings.shist			= qq(1996,1);		% start of history in the data set
settings.ehist			= qq(2019,4);		% end of history in the histcore database for filtering
settings.end_pred		= qq(2024,4);		% end of forecast prediction range 
settings.end_pred_long  = qq(2028,4);		% end of prediction conditioning range 
settings.end_comp		= qq(2040,4);		% end of computations of prediction
settings.start_pred		= settings.ehist+1;

%% Model Toggles
settings.pouzij_odhad	= 0;				% on/off - estimate parameters
%settings.RERlevel		= 1;				% RER version: 1 - ER level, 0 - change in ER
settings.PPI412	        = 0;	

%% Forecast expectation scheme
settings.expectations_scheme = 'setup402';

%% Pre-computed ranges
settings.hrng			= settings.shist:settings.ehist;				% filtering
settings.fcastrng		= settings.start_pred:settings.end_pred;		% forecast
settings.comprng		= settings.start_pred:settings.end_comp;		% computation of prediction

settings.nstarrng     = settings.start_pred:settings.end_pred_long;	% foreign demand
settings.istarrng     = settings.start_pred:settings.end_pred_long;   % foreign interest rate
settings.pstarrng	  = settings.start_pred:settings.end_pred_long;	% foreign inflation rate
settings.potherrng	  = settings.start_pred:settings.end_pred_long;

settings.usdeurrng    = settings.start_pred:settings.end_pred_long;	% USDEUR 
settings.brentrng     = settings.start_pred:settings.end_pred_long;	% Brent USD price

%% Optional prefix extensions
if isfield(settings,'prefix_ext')
    for ix = 1: size(settings.prefix_ext,1);
        settings.extend.(settings.prefix_ext{ix,1}) = [settings.report_prefix settings.prefix_ext{ix,2}];
        disp(['Prefix ' settings.prefix_ext{ix,1} ' for ' [settings.report_prefix settings.prefix_ext{ix,2}] ' was created.']);
    end
    settings = rmfield(settings,'prefix_ext');
end

%% Path
settings.dbpath = '..\database';				% location of folder database for input and output
% settings.outputdata = [settings.dbpath '\Output-data\'];				
% settings.inputdata = [settings.dbpath '\Input-data\'];				
% settings.output_graphs = [settings.dbpath '\Output-graphs\'];				
% settings.output_reports = [settings.dbpath '\Output-reports\'];				

%% Save
ZB_ID = settings;
save([settings.dbpath '\Output-data\' settings.report_prefix '-settings.mat'], 'ZB_ID');
ZB_ID = settings.report_prefix;
disp(['ZB_ID: ', ZB_ID]);
save('GLOBALSETTINGS.mat', 'ZB_ID');
