function [DECOMP] = decomp_all(m, db1,db2, outrng, varargin)
%----------------------------------------------------------------------------------%
% PURPOSE: Calculate Inflation Target Fulfillment (ITF) Decomposition
%
% INPUTS:
%   m                        --  parsed IRIS model object
%   m_old                    --  parsed IRIS model object from old forecast
%   db1, db2                 --  databse for forecast replications, including tunes, etc.
%   outrng                   --  range of decomposition
%
% OUTPUT:
%   DECOMP.store_matrix     --  3D matrix [periods, factors, vars] of contributions
%   DECOMP.truediffmat		--  true differences between d_new and d_old
%   DECOMP.input_datenames  --  factors in the format 'period__type__name'
%   DECOMP.input_vect       --  matrix [time_offset|type]
%   DECOMP.input_names      --  factor names
%   DECOMP.range			--  simulation range 
%   DECOMP.endog_vars		--  reported variables (names)
%   DECOMP.d_old            --  actual simulation of the first  dbase
%   DECOMP.d_new            --  actual simulation of the second dbase
%
% <EMAIL>
% <EMAIL>
%
% First Ver.:   10/06/2008
% Last Change:  08/02/2012
%
% Timing Convetions:  first per. (1) is the first period of simulation,
%                     the 0-th period is the quarter before the start of the sim.
%                     and (-1)-th period is lagged initval, etc.
%                     0-th period is the last filter! period (this is important)
%
% Type Conventions:   0=params, 1=ini, 2=fix/res, 3=eps, 4=obs
%
% TODO:   a] if additivity fails... implement cumulative & grouped reporting directly!
%         b] Fixes to be checked for differences only at periods of plan
%----------------------------------------------------------------------------------%


default = {...
    'plan',                 [], ...			%- plan used for simulations                         
    'anticipate',           true,...		%- default simulation opt (if needed)
	'expectations',			false,...		%- limited expectations design
    'variables',            [],...			%- for which variables to store the results
    'ini2obs',				false,...
    'tunes',				dbempty,...
    'filter_model',         eval('m;'),...
    'filter_range',         [],...
    'trans_range',          [],...
    'simrng',				[],...
    'expect_range',         [],...
    'is_detailed_obs_',     [],...			%- are details needed for obs in filtration?
    'is_detailed_fix_',     [],... 
    'is_detailed_eps_',     [],... 
    'limit',				1e-8,...		%- limit for difference to be evaluated
    };

%--parse options using IRIS --%
options = passopt(default, varargin{1:end});

% fill missing tunes from either database
tune_names = union(dbnames(db1,'nameFilter','^tune_\w*'), ...
	dbnames(db2,'nameFilter','^tune_\w*'));
var_names = strrep(tune_names,'tune_','');
for xx = 1:length(tune_names)
	if isfield(db1,tune_names{xx}) && isfield(db2,tune_names{xx})
		range1 = db1.(tune_names{xx}).range;
		range2 = db2.(tune_names{xx}).range;
		range1 = range1(~isnan(db1.(tune_names{xx})(range1)));
		range2 = range2(~isnan(db2.(tune_names{xx})(range2)));
		range = union(range1,range2);
		range_rest1 = range(isnan(db1.(tune_names{xx})(range)));
		range_rest2 = range(isnan(db2.(tune_names{xx})(range)));
		db1.(tune_names{xx})(range_rest1) = db1.(var_names{xx})(range_rest1);
		db2.(tune_names{xx})(range_rest2) = db2.(var_names{xx})(range_rest2);
        if ~isempty(range)
    		range_rest = setdiff([range(1):range(end)],range);
        else
            range_rest = [];
        end    
   		db1.(tune_names{xx})(range_rest) = NaN;
  		db2.(tune_names{xx})(range_rest) = NaN;
	else
		if ~isfield(db1,tune_names{xx})
			range = db2.(tune_names{xx}).range;
			range_rest = range(isnan(db2.(tune_names{xx})(range)));
			range = range(~isnan(db2.(tune_names{xx})(range)));
			db1.(tune_names{xx}) = tseries;
			db1.(tune_names{xx})(range) = db1.(var_names{xx})(range);
			db1.(tune_names{xx})(range_rest) = NaN;
		end
		if ~isfield(db2,tune_names{xx})
			range = db1.(tune_names{xx}).range;
			range_rest = range(isnan(db1.(tune_names{xx})(range)));
			range = range(~isnan(db1.(tune_names{xx})(range)));
			db2.(tune_names{xx}) = tseries;
			db2.(tune_names{xx})(range) = db2.(var_names{xx})(range);
			db2.(tune_names{xx})(range_rest) = NaN;
		end
	end
end

% fill additional tunes
if ~isempty(fieldnames(options.tunes))
	tune_names = dbnames(options.tunes,'nameFilter','^tune_\w*');
	var_names = strrep(tune_names,'tune_','');
	for xx = 1:length(tune_names)
		range = options.tunes.(tune_names{xx}).range;
		range_rest = range(isnan(options.tunes.(tune_names{xx})(range)));
		range = range(~isnan(options.tunes.(tune_names{xx})(range)));
		if ~isfield(db1,tune_names{xx})
			db1.(tune_names{xx}) = tseries;
			db1.(tune_names{xx})(range) = db1.(var_names{xx})(range);
			db1.(tune_names{xx})(range_rest) = NaN;
		else
			range_cur = db1.(tune_names{xx}).range;
			range_cur = range_cur(~isnan(db1.(tune_names{xx})(range_cur)));
			range_aux = setdiff(range,range_cur);
			db1.(tune_names{xx})(range_aux) = db1.(var_names{xx})(range_aux);
			range_aux = union(range,range_cur);
			range_aux = setdiff([range_aux(1):range_aux(end)],range_aux);
			db1.(tune_names{xx})(range_aux) = NaN;
		end
		if ~isfield(db2,tune_names{xx})
			db2.(tune_names{xx}) = tseries;
			db2.(tune_names{xx})(range) = db2.(var_names{xx})(range);
			db2.(tune_names{xx})(range_rest) = NaN;
		else
			range_cur = db2.(tune_names{xx}).range;
			range_cur = range_cur(~isnan(db2.(tune_names{xx})(range_cur)));
			range_aux = setdiff(range,range_cur);
			db2.(tune_names{xx})(range_aux) = db2.(var_names{xx})(range_aux);
			range_aux = union(range,range_cur);
			range_aux = setdiff([range_aux(1):range_aux(end)],range_aux);
			db2.(tune_names{xx})(range_aux) = NaN;
		end
	end
end

%-- r struct -- time ranges and samples --%
r.outrng		= outrng;
r.simrng		= options.simrng;
r.filter_rng	= options.filter_range;
r.trans_rng		= options.trans_range;
r.kf			= options.filter_model;
if isempty(options.expect_range)
    if isempty(options.trans_range)
        r.expect_rng = options.simrng;     
    else
        r.expect_rng = options.trans_range;     
    end
else
   r.expect_rng = options.expect_range;
end


if isempty(r.simrng) && isempty(options.plan)
    options.plan = plan(m, simrng);
end    

%-- get relevant names (obs,res,fixes) --%
eps_names   = get(m,'eList');
trans_names = get(m,'xList');

if options.ini2obs
	obs_names   = get(r.kf,'yList');
else % initial conditions in the model are in the unpleasant format 
	 % -> transform
	% remove mes_ variables
	init_vars	= get(m,'initCond');
	index		= regexp(init_vars,'mes');
	index		= cellfun(@isempty,index);
	init_vars	= init_vars(index);
	% transform log(varname{lag})-> varname, lag
	init_vars	= strrep(init_vars,'log(','');
	init_vars	= strrep(init_vars,')','');
	lags		= regexp(init_vars,'{-.}','match');
	lags		= cat(2,lags{:});
	lags		= strrep(lags,'{','');
	lags		= strrep(lags,'}','');
	lags		= str2num(str2mat(lags));
	min_lag		= max(lags);
	max_lag		= min(lags);
	inirng		= r.simrng(1)+max_lag:r.simrng(1)+min_lag;
	init_vars	= regexprep(init_vars,'{-.}','');
	% create matrix ini_lags from all [varname, lag]
	[~,ini_index_first] = unique(init_vars,'first');
	[ini_names,ini_index_last] = unique(init_vars,'last');
	ini_lags = [ones(1,length(ini_names));zeros(min_lag-max_lag,length(ini_names))];
	ini_lags(2:min_lag-max_lag+1,(ini_index_first-ini_index_last~=0)) = ones(length(2:min_lag-max_lag+1),sum(ini_index_first-ini_index_last~=0));
end

if ~isempty(options.plan)
    exodb = get_exogenized(options.plan);
    endodb = get_endogenized(options.plan);
    fix_names = fieldnames(exodb);
else
    fix_names  = deal([]);
end

if ~isempty(options.variables)
    endog_vars = options.variables;
else
    endog_vars = trans_names;
end

%--observables, epsilons, fixes diffs --%
[ix_epsdiff, mat_epsdiff] = find_diffs_(eps_names, db1, db2, r.simrng,options.limit);
[ix_fixdiff, mat_fixdiff] = find_diffs_(fix_names, db1, db2, r.simrng,options.limit);
if options.ini2obs
	if (~isempty(options.is_detailed_obs_))
        [ix_obsdiff, mat_obsdiff] = find_diffs_(obs_names, db1, db2, r.filter_rng,options.limit);
        tf = ~ismember(r.filter_rng,options.is_detailed_obs_);
        rest_obsdiff = sum(mat_obsdiff(:,tf),2)>0;
        mat_obsdiff(:,tf) = (zeros(length(ix_obsdiff),sum(tf))==1);
    else
        [ix_obsdiff, ~] = find_diffs_(obs_names, db1, db2, r.filter_rng,options.limit);
        mat_obsdiff = (zeros(length(ix_obsdiff),size(r.filter_rng,2))==1);
        rest_obsdiff = zeros(size(mat_obsdiff,1),1)>0;
	end
else
    [ix_inidiff, mat_inidiff]		= find_diffs_(ini_names, db1, db2, inirng, options.limit);
    [~,index_ini]	= ismember(ix_inidiff,ini_names);
    mat_inidiff		= mat_inidiff.*ini_lags(end:-1:1,index_ini)'==1;
    ix_inidiff		= ix_inidiff(sum(mat_inidiff,2)~=0);
    mat_inidiff		= mat_inidiff((sum(mat_inidiff,2)~=0),:);
    rest_inidiff = zeros(size(mat_inidiff,1),1)>0;
end    

%--check for eps vs fix/eps --%
if ~isempty(options.plan)
    [ix_epsdiff, mat_epsdiff] = eps_collisions_(r, ix_epsdiff, mat_epsdiff, endodb);
    [ix_fixdiff, mat_fixdiff] = fix_collisions_(r, ix_fixdiff, mat_fixdiff, exodb);
end

if (isempty(options.is_detailed_fix_))
    mat_fixdiff = (zeros(length(ix_fixdiff),size(r.simrng,2))==1);
    rest_fixdiff = zeros(size(mat_fixdiff,1),1)>0;
else
    tf=~ismember(r.simrng,options.is_detailed_fix_);
    rest_fixdiff = sum(mat_fixdiff(:,tf),2)>0;
    mat_fixdiff(:,tf) = (zeros(length(ix_fixdiff),sum(tf))==1);
end
if (isempty(options.is_detailed_eps_))
    mat_epsdiff = (zeros(length(ix_epsdiff),size(r.simrng,2))==1);
    rest_epsdiff = zeros(size(mat_epsdiff,1),1)>0;
else
    tf=~ismember(r.simrng,options.is_detailed_eps_);
    rest_epsdiff = sum(mat_epsdiff(:,tf),2)>0;
    mat_epsdiff(:,tf) = (zeros(length(ix_epsdiff),sum(tf))==1);
end

%-- create input_vect types & periods-offset, input_names for all names --%
timeline = (1:length(r.simrng));
[inpvect_eps, inpnames_eps]		= pinput_vect(ix_epsdiff, mat_epsdiff, 3, timeline, options, rest_epsdiff);
[inpvect_fix, inpnames_fix]		= pinput_vect(ix_fixdiff, mat_fixdiff, 2, timeline, options, rest_fixdiff);

if options.ini2obs
         if isempty(r.trans_rng)      
            obs_timeline = [(-1)*(length(r.filter_rng)-1):0]; % [... -3 -2 -1 0]
         else
            trans_len=length(r.trans_rng);
            obs_timeline = [(-1)*(length(r.filter_rng)-1)-trans_len:-trans_len]; % [... -3 -2 -1 0] 
         end        
        [inpvect_obs, inpnames_obs] = pinput_vect(ix_obsdiff, mat_obsdiff, 1, obs_timeline, options, rest_obsdiff);

        input_names = { inpnames_obs{:} inpnames_fix{:} inpnames_eps{:} };
        input_vect  = [ inpvect_obs; inpvect_fix; inpvect_eps ];      
else               
        ini_timeline = [(-1)*(length(inirng)-1):0]; % [... -3 -2 -1 0]
        [inpvect_ini, inpnames_ini] = pinput_vect(ix_inidiff, mat_inidiff, 4, ini_timeline, options, rest_inidiff);

        input_names = { inpnames_ini{:} inpnames_fix{:} inpnames_eps{:} };
        input_vect  = [ inpvect_ini; inpvect_fix; inpvect_eps ];
end
    
input_datenames = make_input_datenames_(input_names, input_vect, r.simrng);

%-- compute contributions --%
% use 3D matrix to store the results [time, factors, endog_variables]
[store_matrix, truediffmat] = calc_(m, db1, db2, r, input_vect, ...
    input_names, endog_vars, options);

%-- output --%
DECOMP.store_matrix    = store_matrix;
DECOMP.truediffmat     = truediffmat;	% d_new - d_old
DECOMP.input_datenames = input_datenames;
DECOMP.input_vect      = input_vect;
DECOMP.input_names     = input_names;
DECOMP.decomp_range    = outrng;
DECOMP.fut_rng         = r.simrng;
DECOMP.trans_rng       = r.trans_rng;
DECOMP.hist_rng        = r.filter_rng;
DECOMP.endog_vars      = endog_vars;
DECOMP.d_old           = db1;   % old simulation with new model (= db1)
DECOMP.d_new           = db2;   % new simulation with new model (= db2)
DECOMP.islog           = get(m, 'log');

if options.ini2obs
	check_decomposition(DECOMP, 'limit', options.limit);
else
	check_decomposition(DECOMP, 'limit', options.limit, 'range', DECOMP.fut_rng);
end

end %--- of the MAIN function -----------------------------------------------------%



function [ixdiffs, matdiffs] = find_diffs_(inputnames, db1, db2, checkrng, limit)
%-- find differences in databases for a particular type of variable (obs, esp, fix)
% mat_xxxdiff = [n,p] matrix, n= nth-item with diffs, p-periods with diffs=1, nodiff=0
% TODO: due to efficiency reasons, avoid mistmatch of checkrng & fix_rng for fixes
%       in principle, the derivative is zero, but it could slow down the calc.

if isstruct(inputnames),
    inames = fieldnames(inputnames); % in case a dbase passed in
else
    inames = inputnames;
end

nitems = length(inames);
is_name_diff = false(nitems); % if a name is different
map_diff = false(nitems, length(checkrng)); % matrix [names, periods] where differences occur

for i = 1 : nitems
    if isfield(db1, char(inames(i)))
        item1 = db1.(char(inames(i)))(checkrng);
    else
        item1 = zeros(length(checkrng),1);
    end
    if isfield(db2, char(inames(i)))
        item2 = db2.(char(inames(i)))(checkrng); 
    else
        item2 = zeros(length(checkrng),1); 
    end    
     
    item1(isnan(item1)) = 0;
    item2(isnan(item2)) = 0;

    isdiff_ = (abs(item1-item2)>limit);
    if any(isdiff_)
        is_name_diff(i) = true;
        map_diff(i,:) = isdiff_;
    end
end

ixdiffs  = inames(is_name_diff); % all names that are not identical in both dbs
matdiffs = map_diff(is_name_diff,:); % only those relevant [those_differnt, periods_of difference

end %-of SUB function find_difs_() ------------------------------------------------%



function [ix_out, mat_diff_out] = eps_collisions_(rng_struct, ix_epsdiff,mat_epsdiff,eps_endonames)
%- checks for collisions of eps_xxx values in the dbase in periods where eps_xxx is
%  endogenized because it is used for fixing
eps_for_fix = fieldnames(eps_endonames);
mat_epsendo = false(length(eps_for_fix),length(rng_struct.simrng));

base_range = rng_struct.simrng(1); % starting point of simulation
for i = 1 : length(eps_for_fix)
    itsrange_  = get(eps_endonames.(eps_for_fix{i}),'range');
    ixperiods_ = (-1)*(base_range - itsrange_(:)) + 1; % vector of shifts [ 1 2 3 4 ], gets the indices where fix is active
    mat_epsendo(i,ixperiods_) = true; % activate
end
%-compare periods, if collision, unregister eps_xxx --%
for i = 1 : length(ix_epsdiff)
    ixmatch_ = strmatch(ix_epsdiff{i}, eps_for_fix, 'exact');
    if ~isempty(ixmatch_)
        ixchange = find(mat_epsendo(ixmatch_,:)==1);
        mat_epsdiff(i,ixchange) = false; % unregister periods, where eps is endogenous
    end
end
%-check if some eps_xxx have become redundant in diffs-%
is_valid = true(length(ix_epsdiff),1);
for i = 1 : length(ix_epsdiff)
    find_zero = sum(mat_epsdiff(i,:));
    if find_zero == 0
        is_valid(i) = false; % there is no '1' for difference!
    end
end
%--output, resized original inputs--%
ix_out       = ix_epsdiff(is_valid);
mat_diff_out = mat_epsdiff(is_valid,:);

end %-of SUB function eps_collisions_ ---------------------------------------------%



function [ix_out, mat_diff_out] = fix_collisions_(rng_struct, ix_fixdiff, mat_fixdiff, fix_names)
%- this is a consequence of poor initial design :(
%  fix_names -- names of fixed vars, struct with tseries and ranges!
fixes_   = fieldnames(fix_names);
mat_fix_ = true(length(fixes_),length(rng_struct.simrng));

base_range = rng_struct.simrng(1); % simul start
for i = 1 : length(fixes_)
    itsrange_  = get(fix_names.(fixes_{i}),'range');
    ixperiods_ = (-1)*(base_range - itsrange_(:)) + 1; % shifts [1 2 3...]
    mat_fix_(i,ixperiods_) = false; % where it is fixed, set to false!
end
for i = 1 : length(ix_fixdiff)
    ixmatch_ = strmatch(ix_fixdiff{i}, fixes_,'exact');
    if ~isempty(ixmatch_)
        ixchange = find(mat_fix_(ixmatch_,:) == 1);
        mat_fixdiff(i,ixchange) = false; % unregister periods where fix is NOT active
    end
end
is_valid = true(length(ix_fixdiff),1);
for i = 1 : length(ix_fixdiff)
    find_zero = sum(mat_fixdiff(i,:));
    if find_zero == 0
        is_valid(i) = false;
    end
end
ix_out       = ix_fixdiff(is_valid);
mat_diff_out = mat_fixdiff(is_valid,:);

end %-of SUB function fix_collisions_ ---------------------------------------------%



function [this_input_vector, this_input_names] = pinput_vect(ix_namediff, mat_diff, type, timeline, opts, rest_diff)
% create partial input vect for a given type
if ~(type == 1 && isempty(opts.is_detailed_obs_)) && ...
   ~((type == 2) && isempty(opts.is_detailed_fix_)) && ...
   ~((type == 3) && isempty(opts.is_detailed_eps_))
    total_diffs = sum(sum(mat_diff)); % total number of diffs (i.e ones)
    nitems = length(ix_namediff);
    switch type
        case 1
            tf=~ismember(opts.filter_range,opts.is_detailed_obs_);
            if sum(tf)>0
                total_diffs =total_diffs + sum(rest_diff);
            end    
        case 2
            tf=~ismember(opts.simrng,opts.is_detailed_fix_);
            if sum(tf)>0
                total_diffs =total_diffs + sum(rest_diff);
            end    
        case 3
            tf=~ismember(opts.simrng,opts.is_detailed_eps_);
            if sum(tf)>0
                total_diffs =total_diffs + sum(rest_diff);
            end    
        otherwise
    end

    this_input_vector = NaN(total_diffs,2);
    this_input_names  = cell(total_diffs,1);

    row_counter = 1;
    for i = 1 : nitems
        diffs_per_var = sum(mat_diff(i,:));
        this_input_vector(row_counter:row_counter+diffs_per_var-1,1) = timeline(mat_diff(i,:));
        this_input_vector(row_counter:row_counter+diffs_per_var-1,2) = type;
        this_input_names(row_counter:row_counter+diffs_per_var-1,1)  = ix_namediff(i);
        row_counter = row_counter + diffs_per_var;
        switch type
            case 1
                tf=~ismember(opts.filter_range,opts.is_detailed_obs_);
                if sum(tf)>0 && rest_diff(i)
                    this_input_vector(row_counter,1) = 1i;
                    this_input_vector(row_counter,2) = type;
                    this_input_names(row_counter,1)  = ix_namediff(i);
                    row_counter = row_counter+1;
                end    
            case 2
                tf=~ismember(opts.simrng,opts.is_detailed_fix_);
                if sum(tf)>0 && rest_diff(i)
                    this_input_vector(row_counter,1) = 1i;
                    this_input_vector(row_counter,2) = type;
                    this_input_names(row_counter,1)  = ix_namediff(i);
                    row_counter = row_counter+1;
                end    
            case 3
                tf=~ismember(opts.simrng,opts.is_detailed_eps_);
                if sum(tf)>0 && rest_diff(i)
                    this_input_vector(row_counter,1) = 1i;
                    this_input_vector(row_counter,2) = type;
                    this_input_names(row_counter,1)  = ix_namediff(i);
                    row_counter = row_counter+1;
                end    
            otherwise
        end
    end

else % no details for ini are required
    nitems = length(ix_namediff);
    this_input_vector = NaN(nitems,2);
    this_input_names  = cell(nitems,1);
    for i = 1 : nitems
        this_input_vector(i,2) = type;
        this_input_names(i,1)  = ix_namediff(i);
    end
end

end %-of SUB function pintput_vect() -----------------------------------------------%



function [input_datenames] = make_input_datenames_(input_names, input_vect, simrng)
%- create the vector of input_datenames, i.e. 'period__type__name'
input_datenames = cell(length(input_names),1);
for i = 1:length(input_names)
    if input_vect(i,1)==1i
        variable_date = 'rest';
    else
        if isnan(input_vect(i,1))
            variable_date = 'all';
        else    
            date_ = simrng(1) - 1 + input_vect(i,1); %TODO: calculating NaNs :) not a perfect coding style :(
            variable_date = char(dat2str(date_));
        end
    end   
    switch input_vect(i,2)
        case 1  
            vartype = 'obs';
        case 2
            vartype = 'fix';
        case 3
            vartype = 'res';
        case 4
            vartype = 'ini'; 
    end
    input_datenames{i} = sprintf('%s__%s__%s', variable_date, vartype, char(input_names(i)));
end
end %-of SUB function make_input_datenames_  ---------------------------------------%




function [store_matrix, truediffmat] = ...
    calc_(m, db1, db2, r, input_vect, input_names, endog_vars, opts)
%- calculates the matrix store_matrix [periods, factors, variables_endog] with particular
%  simulations and filtrations for the factors at hand...

islog = get(m, 'log');

%-- allocate the storage space --%
store_matrix = NaN(length(r.outrng), size(input_vect,1), length(endog_vars));

%-- set base_period as the first of the simulation --%
base_per = r.simrng(1); % note 0 - last filter, 1-first of the simulation, 2-second of the sim...
% a relevant range is always [base_per + offset - 1]
alt_db   = db1;

h = waitbar(0,'Please wait...');
t=0;
for i = 1 : size(input_vect,1)
    tic;
    if ~isnan(input_vect(i,1)) && input_vect(i,1)~=1i
        ef_range = base_per + input_vect(i,1) - 1;
    else  
      if input_vect(i,1)==1i 
        switch input_vect(i,2)
            case 1
                tf=~ismember(r.filter_rng,opts.is_detailed_obs_);
                ef_range=r.filter_rng(tf);
            case 2
                tf=~ismember(r.simrng,opts.is_detailed_fix_);
                ef_range=r.simrng(tf);   
            case 3
                tf=~ismember(r.simrng,opts.is_detailed_eps_);
                ef_range=r.simrng(tf);     
            otherwise
        end
      else    
        if input_vect(i,2)==1
            ef_range = r.filter_rng;
        else    
            ef_range = r.simrng;
        end    
      end  
    end
    
    %--change the data required --%
    if (input_vect(i,2)<=4)
        if (input_vect(i,2) == 1)
            alt_db.(char(input_names(i)))(ef_range) = db2.(char(input_names(i)))(ef_range); 
            f      = kfilter_(r.kf, alt_db, r.filter_rng);
            alt_db = dboverlay_(alt_db, f, r.filter_rng);

            if ~isempty(r.trans_rng)
				if isa(opts.expectations,'expDesign')
                    if r.expect_rng(1)>r.trans_rng(1)
                        if r.expect_rng(1)>r.trans_rng(end)
                            sim_trans = simulate(m, alt_db, r.trans_rng,'anticipate',false);
                        else
                            s1 = simulate(m, alt_db, r.trans_rng(1):r.expect_rng(1)-1,'anticipate',false);
                            alt_db = dboverlay_(alt_db,s1,r.trans_rng(1):r.expect_rng(1)-1);
                            sim_trans = simulate(m, alt_db, r.expect_rng(1):r.trans_rng(end),...
                            'expectations', opts.expectations);
                        end
                    else
                        sim_trans = simulate(m, alt_db, r.trans_rng,...
						'expectations', opts.expectations);
                    end					
				else
                    sim_trans = simulate(m, alt_db, r.trans_rng);
				end
                alt_db = dboverlay_(alt_db, sim_trans, r.trans_rng);
            end  
			if isempty(r.simrng)
				s = alt_db;
			else                
				if isa(opts.expectations,'expDesign')
                    if r.expect_rng(1)>r.simrng(1)
                        s1 = simulate(m, alt_db, r.simrng(1):r.expect_rng(1)-1, ...
                            'plan', opts.plan,...
                            'anticipate', false);
                        alt_db = dboverlay_(alt_db,s1,r.simrng(1):r.expect_rng(1)-1);
                        s = simulate(m, alt_db, r.expect_rng, ...
                            'plan', opts.plan,...
                            'anticipate', opts.anticipate, ...
                            'expectations', opts.expectations);                        
                    else
                        s = simulate(m, alt_db, r.simrng, ...
                            'plan', opts.plan,...
                            'anticipate', opts.anticipate, ...
                            'expectations', opts.expectations);
                    end                        
				else
					s = simulate(m, alt_db, r.simrng, ...
						'plan', opts.plan,...
						'anticipate', opts.anticipate);
				end
				s = dbextend(alt_db,s);
			end
        else
            alt_db.(char(input_names(i)))(ef_range) = db2.(char(input_names(i)))(ef_range);
            if isa(opts.expectations,'expDesign')
                if r.expect_rng(1)>r.simrng(1)
                    s1 = simulate(m, alt_db, r.simrng(1):r.expect_rng(1)-1, ...
                        'plan', opts.plan,...
                        'anticipate', false);
                    alt_db = dboverlay_(alt_db,s1,r.simrng(1):r.expect_rng(1)-1);
                    s = simulate(m, alt_db, r.expect_rng, ...
                        'plan', opts.plan,...
                        'anticipate', opts.anticipate, ...
                        'expectations', opts.expectations);
                else
                    s = simulate(m, alt_db, r.simrng,...
                        'plan', opts.plan,...
                        'anticipate', opts.anticipate, ...
                        'expectations', opts.expectations);
                end
            else
                s = simulate(m, alt_db, r.simrng,...
                    'plan', opts.plan,...
                    'anticipate', opts.anticipate);
            end
            s = dbextend(alt_db,s);                        
        end
    else
        alt_db.(char(input_names(i)))(ef_range) = db2.(char(input_names(i)))(ef_range);
		if isa(opts.expectations,'expDesign')
			s = simulate(m, alt_db, r.simrng,...
				'plan', opts.plan,...
				'anticipate', opts.anticipate, ...
				'expectations', opts.expectations);
		else
			s = simulate(m, alt_db, r.simrng,...
				'plan', opts.plan,...
				'anticipate', opts.anticipate);
		end
        s = dbextend(alt_db,s);
    end    
    for k = 1 : length(endog_vars)
            if islog.(endog_vars{k})
                kth_diff = log(s.(char(endog_vars(k)))(r.outrng) ./ db1.(char(endog_vars(k)))(r.outrng));
                store_matrix(:,i,k) = kth_diff;
            else
                kth_diff = s.(char(endog_vars(k)))(r.outrng) - db1.(char(endog_vars(k)))(r.outrng);
                store_matrix(:,i,k) = kth_diff;
            end
    end
    %-- reset the database --%
    alt_db = db1;
    %-service the waitbar 
    t=t+toc;
    waitbar(i/size(input_vect,1),h,['Processing: ' char(strrep(input_names(i),'_','\_')) ', ETA: ' num2str((size(input_vect,1)-i)*t/i/60,'%4.2f') ' minutes.']);
end
close(h);

%-- calculate true differences for check --%
truediffmat  = NaN(length(r.outrng), length(endog_vars));
for k = 1 : length(endog_vars)
        if islog.(endog_vars{k})
            truediffmat(:,k) = log(db2.(endog_vars{k})(r.outrng) ./ db1.(endog_vars{k})(r.outrng)) ;
        else
            truediffmat(:,k) = db2.(endog_vars{k})(r.outrng) - db1.(endog_vars{k})(r.outrng) ;
        end
end

end %-of SUB function calc_ ---------------------------------------------------------%



function [ dba ] = dboverlay_(dba, dbb, rng)
%- helper function, it is differetnt from dboverlay
to_transfer = intersect(dbnames(dbb,'classFilter','tseries'),dbnames(dba,'classFilter','tseries'));
for i = 1 : length(to_transfer)
    try
        %figure; plotg(rng,[dba.(char(to_transfer(i)))-dbb.(char(to_transfer(i)))],'Title',char(to_transfer(i)),'Type','ppt');
        %pause
        %disp(char(to_transfer(i)));
        dba.(char(to_transfer(i)))(rng) = dbb.(char(to_transfer(i)))(rng);
	catch
        error('itf_calc: something messed up in dboverlay_');
    end
end

end %-of SUB function dboverlay_() --------------------------------------------------%




function [dbout] = kfilter_(m, db, range)
%- kalman filtering, no checks for tunes, these should be already present
obs_names=get(m,'yList');
data = db*obs_names;
tunes_names = dbnames(data,'nameFilter','^tune_\w*');
for i=1:length(tunes_names)
   if isempty(data.(char(tunes_names{i})).data)
       data=rmfield(data, char(tunes_names{i}));
   end    
end    
[M,OUTP] = filter(m, data, range);
tunes_names = dbnames(OUTP.mean,'nameFilter','^tune_\w*');
OUTP.mean = dbbatch(OUTP.mean,'$0','tseries(range,nan([length(range),1]))','nameList',tunes_names);
tunes_names = dbnames(db,'nameFilter','^tune_\w*');
OUTP.mean = dbbatch(OUTP.mean,'$0','db.$0','nameList',tunes_names);
% eps_names = dbnames(OUTP.mean,'nameFilter','^eps_\w*');
% all_names = dbnames(OUTP.mean)-eps_names;
% db_pom_eps = OUTP.mean*eps_names;
% db_pom_other = OUTP.mean*all_names;
% OUTP.mean = dbextend(OUTP.mean,dbfun(@(x) x+x*1i,db_pom_other,'classFilter','tseries'));
% OUTP.mean = dbextend(OUTP.mean,dbfun(@(x) 0+x*1i,db_pom_eps,'classFilter','tseries'));
dbout = OUTP.mean;
end %-of SUB function kfilter_ ------------------------------------------------------%


