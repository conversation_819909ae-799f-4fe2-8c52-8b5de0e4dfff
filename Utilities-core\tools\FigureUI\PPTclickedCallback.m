function PPTclickedCallback(fhandle)

% keyboard;

%% Output file name

outfile = outfilename('Figure export to PPT', ...
                      'Enter PPT file name:', ...
                      'slide1', ...default suggestion
                      '.pptx');

if isempty(outfile)
    return
end

%% Figure positioning
set(fhandle,'Units','pixels');
set(fhandle,'Position',[680 601 958 497/1]);

%% PPT template
% file_template = [dynammo_root '\Utilities\FigureUI\slide_prezentace.pptx'];
file_template = which('slide_prezentace.pptx');
outfile = [cd filesep outfile];
[SUCCESS,MESSAGE,MESSAGEID] = copyfile(file_template,outfile);

%% PowerPoint session
ppt = actxserver('PowerPoint.Application');
ppt.visible = 1;

try
    op = invoke(ppt.Presentations,'Open',outfile);%,[],[],0);
catch
   error_msg('PPT export',['File "' outfile '" cannot be openned...']);
end

slide = op.Slides.Item(1);
invoke(slide,'Select');

%% Copy image to clipboard

openGL_engine = 1;

try % >>> 2014b graphics
   
    % Test the Matlab version this way: <fhandle.Number only in later version>
    print('-dmeta',['-f' num2str(fhandle.Number)],'-r150','-painters') %#ok<MCPRT>
 
catch % >>> 2014a and older
    
    openGL_engine = 0;
    print('-dmeta',['-f' num2str(fhandle)],'-r0') %#ok<MCPRT>
   
end

%% Paste image to .pptx and reposition

if openGL_engine % >>> 2014b graphics
    
    saveas(fhandle,'pic1.emf','emf');
    pic1_file=[pwd '\pic1.emf'];
    
    % Get height and width of slide:
    slide_H = op.PageSetup.SlideHeight;
    slide_W = op.PageSetup.SlideWidth;
    leftpos=0.05;
    toppos=0.10;
    metaWidth=0.85;
    metaHeight=0.7;
        
    pic1 = invoke(slide.Shapes,'AddPicture',pic1_file, ...
                'msoFalse','msoTrue', ...
                leftpos*slide_W, ...single(double(leftpos*slide_W)), ...
                toppos*slide_H, ...
                metaWidth*slide_W, ...
                metaHeight*slide_H); %'left, top, width, height
    delete(pic1_file);
    
else % >>> 2014a and older
    
    pic = invoke(slide.Shapes,'PasteSpecial',3);
    
    % Set position
    toppos = 80;
    leftpos = 0;
    metaWidth = 700;
    
    set(pic,'Left',leftpos,'Top',toppos,'Width',metaWidth);
    
end

%% Drop PPT session
delete(ppt);

end %<eof>