%-----------report-data--------------------%
function dbout = yearly_data_FB(d)
% compute yearly data and the yearly growth rates

%% Alternative computations:
%-- Yearly averages of growth rates--%

list_ = {'ne_zz_dot_y_star4', 'ne_zz_dot_pstar_tilde4','ne_zz_dot_pstar_other_tilde4','ne_zz_dot_pstar_energy_tilde4',...
    'ne_zz_i_star_eu','ne_zz_i_star','ne_zz_i_star_shadow','ne_zz_p_brentUSD_tilde'};

for i = 1:length(list_)
    dbout.(['yy_' list_{i}]) = convert(d.(list_{i}),1);
end


%% Computations for reports:
% Yearly cummulatives (growth rates from levels)

list_ = {'ne_zz_y_star','ne_zz_dot_pstar_tilde','ne_zz_dot_pstar_other_tilde','ne_zz_dot_pstar_energy_tilde'};

for i = 1:length(list_)
    dbout.(['yy_' list_{i}]) = convert(d.(list_{i}),1,Inf,'function',@sum);
    dbout.(['yy_ne_zz_dot' list_{i}(6:end)])=(dbout.(['yy_' list_{i}])/dbout.(['yy_' list_{i}]){-1})*100-100;
    dbout.(['yy_ne_zz_dot' list_{i}(6:end)])=comment(dbout.(['yy_ne_zz_dot' list_{i}(6:end)]),comment(dbout.(['yy_' list_{i}])));
end

list_ = {'zz_y_star','zz_dot_pstar_tilde','zz_dot_pstar_other_tilde','zz_dot_pstar_energy_tilde'};

for i = 1:length(list_)
    dbout.(['yy_' list_{i}]) = convert(d.(list_{i}),1,Inf,'function',@sum);
    dbout.(['yy_zz_dot' list_{i}(3:end)])=(dbout.(['yy_' list_{i}])/dbout.(['yy_' list_{i}]){-1})*100-100;
    dbout.(['yy_zz_dot' list_{i}(3:end)])=comment(dbout.(['yy_zz_dot' list_{i}(3:end)]),comment(dbout.(['yy_' list_{i}])));
end

end

