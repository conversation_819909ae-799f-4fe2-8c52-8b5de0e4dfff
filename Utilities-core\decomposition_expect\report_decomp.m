function report_decomp(decomp, decomp_list, varargin)
%----------------------------------------------------------------%
% inputs: 	
% decomp		-- decomposition of two forecasts (including filters)
% decomp_list	-- endogenous variables to report
%----------------------------------------------------------------%
% @ last revision: za, CNB, Apr 2012

%---defaults and varargin---%
default =	{	...
    'fname',          'Decomposition', ...
    'plot_rng',	      decomp.decomp_range,  ...
    'colormap',       'default', ...   
    'graph_per_page', 2, ...
    'heading',       'Forecast Decomposition', ...
    'display',        false, ...
	'zz_transform',   false, ...
	'endhist',		  decomp.decomp_range(1), ...
    'firstgroups',    [], ...
    };

options = passopt(default,varargin{1:end});

display('Reporting decomposition...');

% adjust plot_rng and others
options.plot_rng = max(decomp.decomp_range(1),options.plot_rng(1)) : ...
	min(decomp.decomp_range(end),options.plot_rng(end));
if ~isempty(decomp.hist_rng)
	hist_rng = max(decomp.hist_rng(1),options.plot_rng(1)) : ...
		min(decomp.hist_rng(end),options.plot_rng(end));
end
if ~isempty(decomp.trans_rng)
	trans_rng = max(decomp.trans_rng(1),options.plot_rng(1)) : ...
		min(decomp.trans_rng(end),options.plot_rng(end));
end
if ~isempty(decomp.fut_rng)
	fut_rng = max(decomp.fut_rng(1),options.plot_rng(1)) : ...
		min(decomp.fut_rng(end),options.plot_rng(end));
end

% check decomp_list
[~,all_loc] = ismember('all',decomp_list);
[~,allexp_loc] = ismember('all_exp',decomp_list);
if any(allexp_loc)
	decomp_list	= decomp.endog_vars;
elseif any(all_loc)
	index		= regexp(decomp.endog_vars,'_exp');
	index		= cellfun(@isempty,index);
	decomp_list	= decomp.endog_vars(index);
end
[~,decomp_loc] = ismember(decomp_list,decomp.endog_vars);
if ~all(decomp_loc)
	error('Wrong decomp_list!');
end

% prepare tseries
varname = cell(length(decomp_loc),1);
varnamecom = cell(length(decomp_loc),1);
um = cell(length(decomp_loc),1);
truediff = cell(length(decomp_loc),1);
checksum = cell(length(decomp_loc),1);
for ivar = 1:length(decomp_loc)
	varname{ivar} = decomp.endog_vars{decomp_loc(ivar)};
	varnamecom{ivar} = comment(decomp.d_new.(varname{ivar}));
    um{ivar} = tseries(options.plot_rng, ...
		decomp.store_matrix(options.plot_rng-decomp.decomp_range(1)+1,:,decomp_loc(ivar)));
	truediff{ivar} = tseries(options.plot_rng, ...
		decomp.truediffmat(options.plot_rng-decomp.decomp_range(1)+1,decomp_loc(ivar)));
	checksum{ivar} = sum(um{ivar},2);
	if options.zz_transform
		if strcmp(varname{ivar}(end),'4') || strcmp(varname{ivar},'y_star_gap') % annual or quarter data
			annual = 1;
		else annual = 4;
		end
		um{ivar} = 100*annual*um{ivar};
		truediff{ivar} = 100*annual*truediff{ivar};
		checksum{ivar} = 100*annual*checksum{ivar};
	end
end

if options.graph_per_page > 6
	error('Only 6 plots per page is allowed');
end
ncols		= ceil(options.graph_per_page/3);
nrows		= ceil(options.graph_per_page/ncols);
no_pages	= ceil(length(decomp_list)/options.graph_per_page);

% draw 
x = report.new(options.heading);

j = 1;
for p=1:no_pages
    if p>1
        x.pagebreak();
    end 
    k = 1;
    x.figure('','decomp_range', options.plot_rng, 'subplot',[nrows ncols], ...
		'dateformat','R/YY','datetick',options.plot_rng(1):4:options.plot_rng(end));      
    while ((j)<=length(decomp_list)) && (k<=options.graph_per_page)
        ivar = j;
		x.graph(regexprep(sprintf('%s',char(varnamecom{ivar})),'_','\_'), ...
			'legend',true,'style',prepare_style());
        
        tser_tmp = um{ivar};
        factor_names = decomp.input_datenames;
        if ~isempty(options.firstgroups) && options.firstgroups>0
            [~,sort_index] = sort(sum(abs(tser_tmp),1),'descend');
            if length(factor_names)>options.firstgroups
                 tser_tmp = tseries(options.plot_rng,[tser_tmp(:,sort_index(1:options.firstgroups-1)) ...
                                                   sum(tser_tmp(:,sort_index(options.firstgroups:end)),2)]);
                 factor_names = [factor_names(sort_index(1:options.firstgroups-1)); {'Rest'}];            
            else
                tser_tmp = tseries(options.plot_rng,tser_tmp(:,sort_index));
                factor_names = factor_names(sort_index);
            end    
        end
        
		x.series('',tser_tmp,'plotfunc',@conbar, ...
			'legend',factor_names);            
		x.series('',truediff{ivar},'legend',NaN);
		x.series('',checksum{ivar},'legend',NaN);
        x.highlight('',fut_rng);
        k=k+1;
        j=j+1;
    end
end

x.publish([options.fname '.pdf'],'paperSize','a4paper','display',false);


end %-of the MAIN FUNCTION-------------------------------------------%



function sty = prepare_style()

sty=struct();
sty.line.Color = {'white', 'black'};
sty.line.LineWidth = {3 , 1.5};
sty.line.LineStyle = {'-'};
sty.legend.location = 'EastOutside';
sty.legend.Orientation = 'Vertical';
sty.highlight.faceColor = {[0.8,0.8,0.8]};
sty.axes.xgrid = 'on';
sty.axes.ylim=[ ...
                   '!! ylim = get(H,''ylim'');', ...
                    'k = 0.05*(ylim(2)-ylim(1));', ...
                    'SET = [ylim(1)-k,ylim(2)+k];'];

end % of SUB function ----------------------------------------------------%
