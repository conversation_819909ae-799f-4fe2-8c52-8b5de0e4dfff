function [] = check_decomposition(decomp, varargin)

%- MAIN FUNCTION ---------------------------------------------------------%

default = {...
	'limit',			1e-15, ...		% computation error limit
    'range',            qq(1990,1):qq(2050,4), ... % range on which check decomposition
    'filter_names',     ''    % use regexp - matched expressions are excluded from check, e.g. '\w+(_exp)$'
    };

options     = passopt(default,varargin{1:end});
[~,index]   = ismember(decomp.decomp_range,options.range);
index       = index > 0;
check_vars  = regexp(decomp.endog_vars,options.filter_names);
check_vars  = cellfun('isempty',check_vars);

decomp_sum  = squeeze(sum(decomp.store_matrix,2));
sum_diff    = 0;
max_diff    = 0;
for i = 1:length(decomp.endog_vars)
    if check_vars(i)
        sum_diff = sum_diff + sum(abs(decomp.truediffmat(index,i)-decomp_sum(index,i)));
        max_diff = max([max_diff; abs(decomp.truediffmat(index,i)-decomp_sum(index,i))]);
    end    
end
if max_diff > options.limit
	warning(['Suma rozdilu je ' num2str(sum_diff)]);
	warning(['Maximalni rozdil v jednom obdobi je ' num2str(max_diff)]);
end

end %-of the MAIN FUNCTION------------------------------------------------%
