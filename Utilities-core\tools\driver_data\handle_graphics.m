function handle_graphics(sr_opt)
% >>> vlozeny kus kodu

box on;grid on;
axis 'tight';
ylim_ = get(gca,'Ylim');
xlim_ = get(gca,'Xlim');
seda_plocha = patch([dat2dec(sr_opt.ehist+1) dat2dec(sr_opt.ehist+1) xlim_(end) xlim_(end)], ...
                    [ylim_(1) ylim_(2) ylim_(2) ylim_(1)],sr_opt.finta_3D,sr_opt.RGB_barva);
set(seda_plocha,'linestyle','none');
nastav_grid('x-ova','y-ova');
uplny_ramec;

%%% --> sub-fn()
function nastav_grid(varargin)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% 'nastav_grid' do grafu prida horizontalni nebo vertikalni grid pomoci
% primeho vykresleni carkovanych car podle zarazek na osach
%
% na vstupu muze byt parametr 'x-ova', 'y-ova', nebo oboji
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

for i=1:nargin
     souradnice=varargin{i};
     switch souradnice
        case 'x-ova'
             rozpeti_y = get(gca,'ylim');
             zarazky_x = get(gca,'xtick');
%              zarazky_x = zarazky_x(2:end-1);
             pozice_grid=repmat(rozpeti_y,length(zarazky_x),1);
             line([zarazky_x;zarazky_x],pozice_grid','linestyle',':','color','k');
        case 'y-ova'
             rozpeti_x = get(gca,'xlim');
             zarazky_y=get(gca,'ytick');
%              zarazky_y = zarazky_y(2:end-1);
             pozice_grid=repmat(rozpeti_x,length(zarazky_y),1);
             line(pozice_grid',[zarazky_y;zarazky_y],'linestyle',':','color','k');
        otherwise
             error('nastav_grid.m: nazev souradnice muze byt pouze ''x-ova'' nebo ''y-ova''...');
    end
end

end

function uplny_ramec();
%zcela ohranici graf cernou carou...

rozpeti_y = get(gca,'ylim');
rozpeti_x = get(gca,'xlim');

line([rozpeti_x(1) rozpeti_x(1)],[rozpeti_y(1) rozpeti_y(2)],'color','k');
line([rozpeti_x(2) rozpeti_x(2)],[rozpeti_y(1) rozpeti_y(2)],'color','k');
line([rozpeti_x(1) rozpeti_x(2)],[rozpeti_y(1) rozpeti_y(1)],'color','k');
line([rozpeti_x(1) rozpeti_x(2)],[rozpeti_y(2) rozpeti_y(2)],'color','k');
end

end