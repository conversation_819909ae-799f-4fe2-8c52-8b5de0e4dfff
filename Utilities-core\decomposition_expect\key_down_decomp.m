function key_down_decomp(hObj,eventdata,decomp,legend_names,prec)
% intercept the key press in decomposition (bar contribution) graphs and
% allowes the user to move the datatip between individual contributions

% input arguments:
%  decomp - IRIS time series data matrix with decomposition
%  legend_names - structure containing the string names of all the individual contributions
%  precision - large number, used to avoid indeterminacy in graph when identifying the contribution by clicking 

pos = get(0,'userdata'); % shared variable, custom_datatip_decomp update function creates this 
key = eventdata.Key; % what key was pressed by the user
  
% round the position in the graph to given precision

pres = prec;

if pos(2) > 0
    pos(2) = round(abs(pos(2))*pres)/pres;
else
    pos(2) = -round(abs(pos(2))*pres)/pres;
end

% identify the selected period (in IRIS terms) from clicking in the graph

year=floor(pos(1));
quarter=pos(1)-year;
if quarter < 0.75
    if quarter < 0.5
        if quarter < 0.25
            quarter = 1;
        else
            quarter = 2;
        end
    else
        quarter = 3;
    end
else
    quarter = 4;
end
            
period = qq(year,quarter);

% split the decomposition in current period to positive and negative
% contributions

dec_in_per = decomp(period); 

dec_in_per_pos = dec_in_per;
dec_in_per_neg = dec_in_per;

for i = 1:length(dec_in_per)
    if dec_in_per(i)>0;
        dec_in_per_neg(i) = 0;
    elseif dec_in_per(i)<0;
        dec_in_per_pos(i) = 0;
    end
end

dec_in_per_pos = round(dec_in_per_pos*pres)/pres;
dec_in_per_neg = -round(abs(dec_in_per_neg)*pres)/pres;

% take cumulative sums that are in fact plotted in the bar plot

cmsm_pos = cumsum(dec_in_per_pos);
cmsm_neg = cumsum(dec_in_per_neg);

% find out which contribution was selected before the key was pressed

% note that the datatip is located always to side of the
% individual bar that is further from the zero line

curInd = 0;
ind_found = 0;
leg_str = 'not found';
if pos(2) == 0
    leg_str = 'zeroline';
elseif pos(2) > 0
    srch = abs(cmsm_pos-pos(2));
    [x1 x2] = find(srch==min(srch));
    crit = cmsm_pos(x2(1));
    for i = 1:length(dec_in_per)        
        if ind_found == 0
            if round(dec_in_per_pos(i)*pres) ~= 0 % ignore contributions smaller than given precision
                if crit <= cmsm_pos(i)
                    ind_found = 1;
                    curInd = i;
                    leg_str = char(legend_names{i});
                end
            end
        end
    end
elseif pos(2) < 0
	srch = abs(cmsm_neg-pos(2));
    [x1 x2] = find(srch==min(srch));
    crit = cmsm_neg(x2(1));
	for i = 1:length(dec_in_per)  
        if ind_found == 0
            if round(dec_in_per_neg(i)*pres) ~= 0 % ignore contributions smaller than given precision
                if crit >= cmsm_neg(i)
                    ind_found = 1;
                    curInd = i;
                    leg_str = char(legend_names{i});
                end
            end
        end
    end  
end

% create a vector of ordered contribution indexes and ther values in the
% same order in which they are actually plotted in the bar graph

pos_index   = [];
pos_contrib = [];
neg_index   = [];
neg_contrib = [];
zer_index   = [];
zer_contrib = [];

for i = 1:size(decomp,2)
    if abs(decomp(period,i)) < 1/prec
        zer_index = [zer_index, i];
        zer_contrib = [zer_contrib, 0];
    elseif decomp(period,i) > 0
        pos_index = [pos_index, i];
        pos_contrib = [pos_contrib, decomp(period,i)];
    elseif decomp(period,i) < 0
        neg_index = [neg_index, i];
        neg_contrib = [neg_contrib, decomp(period,i)];
    end
end

neg_index = fliplr(neg_index);
neg_contrib = fliplr(neg_contrib);

contrib_order = [neg_index zer_index pos_index]; % order of contributions - indexes
ordered_contrib = [neg_contrib zer_contrib pos_contrib]; % ordered contributions - values

% zero contributions are dropped

contrib_order_no_zero = [neg_index pos_index]; % order of contributions - indexes
ordered_contrib_no_zero = [neg_contrib pos_contrib]; % ordered contributions - values

       if strcmpi(key,'leftarrow') % moving left and right makes the datatip stay with the same contribution in previous or next period
           if period > min(range(decomp)) 
               pos(1) = pos(1) - 0.25;
               period = period - 1;
               dec_in_per_prev = decomp(period,:);
               if curInd == 0 % if zeroline was selected previously
                   pos(2) = 0;
               else
                   if decomp(period,curInd)>=0
                       dec_in_per_prev(dec_in_per_prev<0)=0;
                       pos(2) = sum(dec_in_per_prev(1:curInd));
                   else
                       dec_in_per_prev(dec_in_per_prev>0)=0;
                       pos(2) = sum(dec_in_per_prev(1:curInd));
                   end
               end
           end
       elseif strcmpi(key,'rightarrow') % moving left and right makes the datatip stay with the same contribution in previous or next period
           if period < max(range(decomp))
               pos(1) = pos(1) + 0.25;
               period = period + 1;
               dec_in_per_next = decomp(period,:);
               if curInd == 0 % if zeroline was selected previously
                   pos(2) = 0;
               else
                   if decomp(period,curInd)>=0
                       dec_in_per_next(dec_in_per_next<0)=0;
                       pos(2) = sum(dec_in_per_next(1:curInd));
                   else
                       dec_in_per_next(dec_in_per_next>0)=0;
                       pos(2) = sum(dec_in_per_next(1:curInd));
                   end
               end
           end
       elseif strcmpi(key,'uparrow') % moving up and down makes the datatip stay in current period and switch between contributions
           if curInd == 0 % if zeroline was selected previously
               curInd = pos_index(1);
           else
               [x y]=find(contrib_order_no_zero==curInd);

               if y<length(contrib_order_no_zero)
                   curInd = contrib_order_no_zero(y+1);
               end
           end

               if decomp(period,curInd)>0
                    pos(2) = sum(dec_in_per_pos(1:curInd));   
                    leg_str = char(legend_names{curInd});
               else
                    pos(2) = sum(dec_in_per_neg(1:curInd));   
                    leg_str = char(legend_names{curInd});
               end

        elseif strcmpi(key,'downarrow') % moving up and down makes the datatip stay in current period and switch between contributions
            if curInd == 0 % if zeroline was selected previously
                curInd = neg_index(end);
            else
               [x y]=find(contrib_order_no_zero==curInd);

               if y>1
                   curInd = contrib_order_no_zero(y-1);
               end
            end
            
               if decomp(period,curInd)>0
                    pos(2) = sum(dec_in_per_pos(1:curInd));   
                    leg_str = char(legend_names{curInd});
               else
                    pos(2) = sum(dec_in_per_neg(1:curInd));   
                    leg_str = char(legend_names{curInd});
               end

       end
       
if curInd == 0       
    output_txt = {
    ['Period: ',char(dat2str(period))]...
    ['legend: ', 'zeroline']...
    ['contrib:', num2str(0)]...
    ['X: ', num2str(pos(1),4)]...
    ['Y: ', num2str(pos(2),4)] ...
    };       
else
    output_txt = {
    ['Period: ',char(dat2str(period))]...
    ['legend: ', leg_str]...
    ['contrib:', num2str(decomp(period,curInd))]...
    ['X: ', num2str(pos(1),4)]...
    ['Y: ', num2str(pos(2),4)] ...    
    };       
end

      set(0,'userdata',pos); % update the shared variable with cursor position
      
       try
           cursorMode = datacursormode(gcf);
           cursorMode.removeAllDataCursors
           hDataTip = cursorMode.createDatatip(gca);
           set(hDataTip,'Position',pos,'String',output_txt);
       catch
       end
end