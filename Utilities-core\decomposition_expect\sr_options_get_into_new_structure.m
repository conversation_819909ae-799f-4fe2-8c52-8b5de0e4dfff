function [sr_ID] = sr_options_get_into_new_structure(sr_ID)

% This function creates a data structure that stores data for forecast
% specification like: input data, labels, dates etc.
% this function is called by drivers for filtering and forecasting, also
% drivers for reporting are using it.
% Syntax
% function sr_opt = sr_options()

% @ last revision fk apr-2012
%% --------------------------------------------------------------------- %%
% prefix for the actual forecast and filtering
sr_opt.report_prefix    = sr_ID.report_prefix;
% prefix for the old forecast database
sr_opt.oldfrc_prefix    = sr_ID.oldfrc_prefix;
%sr_opt.oldfrc_prefix    = '2011sz01-base';
% Legend labels for plotting graphs - czech version
sr_opt.lgnd             = sr_ID.lgnd;
% Legend labels for plotting graphs - english version
sr_opt.lgnd_eng         = sr_ID.lgnd_eng;
% Labels for plotting alternative scenarios
sr_opt.lgnd_alt         = sr_ID.lgnd_alt;

%% Reporting and storing the data
% options: true, false
sr_opt.filter            = sr_ID.filter;
sr_opt.filter_book       = sr_ID.filter_book;
sr_opt.filter_book_br    = sr_ID.filter_book_br;
sr_opt.forecast          = sr_ID.forecast;
sr_opt.forecast_book     = sr_ID.forecast_book;
sr_opt.forecast_book_br  = sr_ID.forecast_book_br;
sr_opt.forecast_inputs   = sr_ID.forecast_inputs;
sr_opt.forecast_detailed = sr_ID.forecast_detailed;

%% Databases 
% name of histcore databases
pos=strfind(sr_ID.histcore_name, '\');
sr_opt.histcore_name     = sr_ID.histcore_name(pos(end)+1:end);
pos=strfind(sr_ID.histcore_name_pbs, '\');
sr_opt.histcore_name_pbs = sr_ID.histcore_name_pbs(pos(end)+1:end);

% name of expert judgement database
pos=strfind(sr_ID.adhoc_data , '\');
sr_opt.adhoc_data        = sr_ID.adhoc_data(pos(end)+1:end);

%% Dates
% start of history in the data set
sr_opt.shist        = sr_ID.shist; 
% end of history in the histcore database for filtering
sr_opt.ehist        = sr_ID.ehist;
% end of prediction range 
sr_opt.end_pred     = sr_ID.end_pred;
% end of computations of prediction
sr_opt.end_comp     = sr_ID.end_comp;

%% Ranges - judgments for forecast
sr_opt.start_pred  = sr_ID.start_pred;
sr_opt.nstarrng  = sr_ID.nstarrng;    % foreign demand
sr_opt.istarrng  = sr_ID.istarrng;       % foreign interest rate
sr_opt.pistarrng = sr_ID.pistarrng;       % foreign inflation rate
sr_opt.regrng    = sr_ID.regrng;       % regulated prices
sr_opt.dotgrng   = sr_ID.dotgrng;     % real government
sr_opt.dotgprng  = sr_ID.dotgprng;     % nom. government
sr_opt.targetrng = sr_ID.targetrng;       % inflation target

%% Plotting options
% start of default plot range
sr_opt.start_plot   = sr_ID.start_plot;
% start of extended plot range
sr_opt.starte_plot  = sr_ID.starte_plot;

sr_opt.cmp_rng   = sr_ID.cmp_rng;     % comparison of actual vs. previous forecast
sr_opt.plot_rng  = sr_ID.plot_rng;  % default graph range
sr_opt.extd_rng  = sr_ID.extd_rng; % extended graph range
sr_opt.shrt_rng  = sr_ID.shrt_rng;     % for PP
sr_opt.shex_rng  = sr_ID.shex_rng;    % for extended PP
sr_opt.whole_rng = sr_ID.whole_rng;       % overall data

% sr_opt.rng_db = sr_ID.rng_db;         % database range for book forecast xls file, default is +4
sr_opt.rng_g   = sr_ID.rng_g; % graphs in analytical report
sr_opt.rng_t   = sr_ID.rng_t;  % tables in analytical report

%% Additional automatic settings
% end of ouf outlooks data for filtering
sr_opt.end_exo      = sr_ID.end_exo;
% Ranges
sr_opt.hrng      = sr_ID.hrng;          % filtering
sr_opt.fcastrng  = sr_ID.fcastrng;  % forecast
sr_opt.comprng   = sr_ID.comprng;  % computation of prediction
%%
% ulozit do
clear sr_ID;sr_ID=sr_opt;clear sr_opt;
save(['..\database\Input-data\' sr_ID.report_prefix '-sr_opt.mat'], 'sr_ID');  
end % function
