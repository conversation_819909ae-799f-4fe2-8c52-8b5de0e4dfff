 
clear all;
close all;

%% Computation prefix for future reference

% Results .mat file name:
spaghetti_label= 'nY';
%  ...loading previous results also searches for this name tag...
%  ...empty string ('') also possible for a name tag :)

%% Model names to be displayed on graphs (legend entries)

% Baseline model:
model_names{1} = 'g3';
% Alternative - use empty string ('') if only baseline model exists
model_names{2} = 'new_model';

%% Computation ranges

% Filter starts from:
sr_opt_base.shist        = qq(1996,1); 
% Fcast terminates at:
sr_opt_base.end_comp     = qq(2015,4);
% Plot range:
plotlim{1} = qq(1999,1);	% min
plotlim{2} = qq(2013,4);	% max

%% Spaghetti setup

endhist_start = qq(2002,1);% --> "oldest endhist" 
endhist_end   = qq(2012,2);% --> "newest endhist"

hair_length = 8; %    Do not exceed hair_length =8!!...
                 % ...otherwise reloading previous results may crash
 
%% Actual data (should be fresh)

% Forecast output including transformations:
forecast_csv = '../database/Output-data/2012sz05-forecast.csv';
% Newest DB:
histcore_csv = '../database/Input-data/histcore-16-Jul-2012.csv';

%% Computation launcher
recompute = 0;	% 2 = computation over again (both baseline & alternative)
				% 1 = compute alternative ONLY & load previous results for...
				%     ...the baseline (spaghetti_label used for reference)
				% 0 = plot previous results without ever computing
								
%% Data - baseline
indata.baseline.model_filter = '../database/Output-data/2012sz05-kalman.mat';
indata.baseline.model_fast   = '../database/Output-data/2012sz05-model.mat';
indata.baseline.ss           = '../database/Output-data/2012sz05-SS.mat';

%% Data - alternative (if no alternative -> model_names{2} should be an empty string)
indata.alternative.model_filter = '../database/Output-data/2012sz05_nY-kalman.mat';
indata.alternative.model_fast   = '../database/Output-data/2012sz05_nY-model.mat';
indata.alternative.ss           = '../database/Output-data/2012sz05_nY-SS.mat';

%% Variables to plot
%  decomp_plot_vars = {'ne_zz_i'};	
 decomp_plot_vars = {'ne_zz_i','ne_zz_dot_gdp4','ne_zz_s', ...
					 'ne_zz_dot_c4','ne_zz_dot_w4','ne_zz_dot_j4', ...'ne_zz_dot_g4', ...
					 'ne_zz_dot_x4','ne_zz_dot_n4','zz_dot_cpi4','ne_zz_dot_pC4', ...
					 'ne_zz_dot_pJ4','ne_zz_dot_pX4','ne_zz_dot_pN4'};










%% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%% >>> DO NOT TOUCH BELOW <<< %%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%% Data loading...

% if 1 model to plot, reset the 'recompute' option 
if isempty(model_names{2}) && recompute==1
    recompute = 2;
end

if recompute==2
    disp([sprintf('\n'), '||| Loading data (baseline)...']);
    load(indata.baseline.model_filter);
    fmodel1=m;
    load(indata.baseline.model_fast);
    model1=m;
    % SS1=
    load(indata.baseline.ss);
    SS1=SS;
else
    fmodel1 = '';
    model1  = '';
    SS1     = '';
end

if ~isempty(model_names{2}) && (recompute==1 || recompute==2)
	disp([sprintf('\n'), '||| Loading data (alternative)...']);
	load(indata.alternative.model_filter);
	fmodel2=m;
	load(indata.alternative.model_fast);
	model2=m;
	SS2=load(indata.alternative.ss);
else
    fmodel2 = '';
    model2  = '';
    SS2     = '';
end

%% Main function
% in the first rows are function and structures for the first model:
% function changing data, transforming data for filter, transforming data for forecast, filtering model, forecasting model, steady state parameters, function creating forecast plans, 
% function for filtering, function for forecasting, function for transformations
%                         
% in the third and fourth rows the same fore second model
% in fifth row histcore and database with observed real values for comparison
% in sixth row start, end and lenght of the hair simulations

clear m;
model_names = strrep(model_names,'_','\_');

if ~isempty(model_names{2})
	hairs_model_comparison(@changedata,@histdata   ,@fcastdata   ,fmodel1,model1,SS1,@model1_plan,...
		                   @filterhistory,@esimulate,@mk_transformations1,... 
	                       @changedata,@histdata_nY,@fcastdata_nY,fmodel2,model2,SS2,@model2_plan,...
	                       @filterhistory,@esimulate,@mk_transformations1,... 
	                        histcore_csv, ...
							forecast_csv,...
                            endhist_start,endhist_end,hair_length, ...
	                        recompute,model_names,plotlim,spaghetti_label, ...
                            decomp_plot_vars,sr_opt_base);
else
	disp('...to be resolved later...');
end



