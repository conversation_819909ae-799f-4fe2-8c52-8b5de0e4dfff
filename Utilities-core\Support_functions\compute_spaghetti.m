function  compute_spaghetti(fmodel1,model1,SS1,model1_plans,...
                               esimulate1,mixed_expect1,trans,...
                               endhist_first,endhist_last,...
                               spaghetti_label, ...
                               sr_opt_base)
              
% keyboard;
%% Error checking 
n_per = endhist_last-endhist_first+1;
if n_per<1
   n_per %#ok<NOPRT>
   error('"<endhist_last-endhist_first>" difference must be strictly positive...'); 
end

%% Prep
% Standard computation hair_length = 8 ...can be reset here
hair_max = 8;
% keyboard;
hair_length = hair_max;

% load settings
load('GLOBALSETTINGS.mat');
settings = load_settings(ID);

sr_opt_base.ehist        = endhist_first;
settings.shist             = sr_opt_base.shist;

%% Load pre-filter database
dfull = dbload(settings.prefilter_fb_name);
           
dfcast = dbload(settings.kalman_fb_name);	% database made from histcore

%% Computation
s1=[];

for ii=1:n_per
    disp([sprintf('\n'), ['||| Simulating [' num2str(ii) '/' num2str(n_per) ']...']]);
    disp(dat2str(endhist_first+ii-1));
		
		try 
% keyboard;
            settings.ehist        = sr_opt_base.ehist+ii-1;
            settings.start_pred   = settings.ehist+1;
            settings.end_pred     = settings.start_pred+7;
            settings.fcastrng     = settings.start_pred:settings.end_pred;
            settings.hrng         = settings.shist:settings.ehist;
            settings.end_comp     = settings.start_pred+40;
            settings.comprng      = settings.start_pred:settings.end_comp;

            % [1] Filtering
            SS1.filtering = true;
            
            % Pre-filter Db
            d = dfull;

            % remove forecast from database
            d = dbclip(d,settings.hrng);

            dtunes = dbempty();

            % Run the filtering step
            dbfilter = filterhistory(fmodel1, d, dtunes, SS1, settings.hrng);
            f = dbfilter.mean;
            
            % [2] Forecast
            SS1.filtering = false;
            
            db_ejud = dbempty();
            %db_ejud.dot_n_star = exp(dfull.obs_N_STAR/100)/exp(dfull.obs_N_STAR{-1}/100);
            
           
            % merge databases
            %     fcast_plan  = exogenize( fcast_plan, 'usdeur',           fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_USDEUR',       fcastrng);
%     
%     fcast_plan  = exogenize( fcast_plan, 'dot_p_BrentUSD_tilde', fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_p_BrentUSD_tilde',       fcastrng);
%     
%     fcast_plan  = exogenize( fcast_plan, 'dot_p_other_tilde',fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_p_other',      fcastrng);
%     
% %     fcast_plan  = exogenize( fcast_plan, 'dot_p_star_tilde' ,fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_p_ener_ex_oil_tilde'        ,fcastrng);
%     
%     fcast_plan  = exogenize( fcast_plan, 'dot_cpi_star_tilde',fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_dot_cpi_star_tilde',      fcastrng);
%     
%     fcast_plan  = exogenize( fcast_plan, 'y_star_gap',       fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_Nstar',        fcastrng);
%      
%     fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend',       fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_y_star_trend',        fcastrng);   
%     
%     fcast_plan  = exogenize( fcast_plan, 'i_star',           fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_Istar',        fcastrng);
% 
% 	fcast_plan  = exogenize( fcast_plan, 'i_star_eq',           fcastrng);
%     fcast_plan  = endogenize(fcast_plan, 'eps_i_star_eq',       fcastrng);       
            
            d_help.usdeur = exp(dfull.obs_USDEUR/100);
            d_help.dot_p_other_tilde = exp(dfull.obs_P_OTHER_TILDE/100)/exp(dfull.obs_P_OTHER_TILDE{-1}/100);
            d_help.dot_p_BrentUSD_tilde = exp(dfull.obs_P_BRENTUSD_TILDE/100)/exp(dfull.obs_P_BRENTUSD_TILDE{-1}/100);
            d_help.dot_p_star_tilde = exp(dfull.obs_P_STAR_TILDE/100)/exp(dfull.obs_P_STAR_TILDE{-1}/100);
            d_help.i_star = dfull.obs_I_STAR/400+1;
            
            d = dbextend(d_help, f);
         


            % remove structural shocks over forecasting horizon
            list = get(model1,'eList');
            for i = 1:length(list);
                d.(list{i})(settings.comprng) = zeros(size(settings.comprng));
            end

            d = dbextend(d, db_ejud);

                emptyPlans = 0;
            if emptyPlans
                fcast_plan1 = plan(model1, settings.comprng);
                fcast_plan_surprise1 = plan(model1, settings.comprng);
            else
                [fcast_plan1] = model1_plans(model1,settings,SS1);
            end
            
           
            % SIMULATE 
            dbfcast1 = esimulate1(model1, d, settings.comprng,'plan', fcast_plan1,'settings',settings);
			            
            
            dd1 = trans.transformations1(dbfcast1,SS1,settings,f,trans);
            
		catch error_
			% On error - save actual results and quit
			disp([sprintf('\n')]);
			fprintf('||| ERROR OCURRED, saving contemporaneous results...');
			save(['Output' filesep 'computed_spaghetti_[' spaghetti_label '].mat'],'s1');
			fprintf('DONE!\n');
			disp(['||| Results saved to: Output/computed_spaghetti_' spaghetti_label '.mat']);
			rethrow(error_);
		end
		
        %% Expand the results struct() with current simulation

        s_pom = dbclip(dd1,settings.ehist:settings.ehist+hair_length);
        if ~isempty(s1)
            s1 = dbfun (@(x,y) [x y], s1, s_pom);
        else
            s1 = s_pom;
        end    

        
		%% Save contemporaneous results
		save([settings.outdata_dir filesep 'computed_spaghetti_[' spaghetti_label '].mat'],'s1');
		
end

%% Saving .mat

    disp([sprintf('\n')]);
    fprintf('||| Saving spaghetti...');
    save([settings.outdata_dir filesep 'computed_spaghetti_[' spaghetti_label '].mat'],'s1');
    fprintf('DONE!\n');

    %disp(['||| Results saved to: Output/computed_spaghetti_[' spaghetti_label '].mat']);
end
