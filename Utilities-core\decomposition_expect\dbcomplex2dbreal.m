function [db] = dbcomplex2dbreal(d)

%----------------------------------------------------------------%
% Transforms forecast database in complex format ("pure") to 
% real format
% INPUT: 	
%		d -- database in complex format
% OUTPUT: 	
%		db -- database in real format
%
%----------------------------------------------------------------%
% @ last revision: za, CNB, Feb 2012

pocet_rad = length(dbnames(d));
eps_names = dbnames(d,'nameFilter','^eps_\w*');
db = dbfun(@(x) imag(x),d);
if pocet_rad == 1
    if strcmp(eps_names,dbnames(d))
        d_eps = d;
    else
        d_eps = struct;
    end
else    
   d_eps = d*eps_names;
end    
d_eps = dbfun(@(x) imag(x)+real(x),d_eps);
db = dbextend(db,d_eps);
db = dbfun(@(x,y) comment(x,comment(y)),db,d);


