close all
clear all
disp([sprintf('\n'), 'DRIVER_FCAST']);

%% Set options

do_forecast_book     = 0;      % do forecast book options
                               % 0 - do not create forecast book
                               % 1 - do simple forecast book 
                               % 2 - do forecast book with filter comparison 
do_compare_book      = 0;

% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('GLOBALSETTINGS.mat');

disp(['FB_ID: ', FB_ID]);

% load settings
settings = load_settings_march(FB_ID);


%%
SS.filtering = false;

SS              = setparam_FB(settings, 'filtering', SS.filtering);
m = readmodel('../../Utilities-core/model_march/inc_g3_FB.model',SS);

% load expectations scheme
try
    expect_scheme = expDesign(m,settings.expectations_scheme);
catch
    msgbox('Expectation scheme not defined!!!','Expectations scheme error','error');
    disp('Expectation scheme not defined.');
end

%%

simu = dbload([settings.outdata_dir '\' settings.report_prefix '-forecast-fb.csv']);

%%

simu.eps_Istar(settings.istarrng(1)) = simu.eps_Istar(settings.istarrng(1)) + 0.001;

     
     
%%

tic
[sim_out,~,~,~,expectations] = simulate(m, simu, settings.comprng, ...
	'expectations',expect_scheme);

disp('simulate time:');
toc
if isequal(exist('expectations','var'),1)
	disp(expectations);
end