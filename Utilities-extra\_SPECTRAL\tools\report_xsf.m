function report_xsf(d_m, varargin)

% Create XSF report.
% Calling: report_xsf(d_m, varargin)
%         where d_m is struct outcome of a data_moments function or
%         a model_moments funtion
% Options: 'fname' ... file name (without extension)
%          'ext'   ... file type (ps or pdf)
%          'cmp'   ... model acf (outcome of model_moments) for comparison


%% default
default =	{'fname', 'xsf',  ...
    'ext',    'pdf', ...
    'cmp',    [], ...
    'periods',[4 12],...
    'pggraph', 3};



options = passopt(default,varargin{1:end});
colors = {'red','black','green','yellow'};
markers = {'square','diamond','*','+'};

%% compare flag
if isempty(options.cmp)
    flag = 0;
else
    flag = 1;
end

%% test if variables in list are present in both databases
if flag == 1
    for ii =1:length(options.cmp)
        if ~strcmp(d_m.listvar, options.cmp(ii).listvar)
            error(['The databases have to have same variables including ordering.'])
        end;
    end;
end;

%% compare data and data is not available
if flag
    if strcmp(d_m.type, 'data')
        for ii =1:length(options.cmp)
            if strcmp(options.cmp(ii).type,'data')
                error('Compare data moments is not possible.')
            end
        end;
    else
        for ii =1:length(options.cmp)
            if strcmp(options.cmp(ii).type , 'data')
                pom = d_m;
                d_m = options.cmp{ii};
                options.cmp{ii} = pom;
            end
        end;
    end;
end;



%% number of variables
n = length(d_m.listvar);

%% legends
if strcmp(d_m.type, 'data')
    legend_str{1} = 'Sample';   
    shift =1;
else
    legend_str{1} = d_m.label;
    shift =1;
end;
if flag == 1
    for ii=1:length(options.cmp)
        legend_str{ii+shift} = options.cmp(ii).label;
    end;
end;


%% reporting
x = report.new('XCF Results');

%% spectral density
f=presetfigure('Visible','off');

ngraph = 0;

for i = 1 : n
    ngraph = ngraph + 1;
    if ngraph>options.pggraph^2
        x.figure('Spectral density',f);
        x.pagebreak();
        f=presetfigure('Visible','off');
        ngraph = 1;
    end
    subplot(options.pggraph,options.pggraph,ngraph);
    plot(d_m.freq, squeeze(real(d_m.sd(i,i,:))),'color','blue');
    hold on
    if flag
        for ii=1:length(options.cmp)
            plot(d_m.freq,squeeze(real(options.cmp(ii).sd(i,i,:))),'color',colors{ii});
        end;
    end
    if mod(ngraph,options.pggraph^2)==1, legend(legend_str); end;
    plotper(options.periods);
    xlim([d_m.freq(1) d_m.freq(end)]);
    title([strrep(d_m.listvar{i},'_','_')]);
end

x.figure('Spectral density',f);
x.pagebreak();

%% Coherence

ngraph = 0;

f=presetfigure('Visible','off');

for i = 1 : n-1
    for j = i+1:n
        ngraph = ngraph + 1;
        if ngraph>options.pggraph^2
            x.figure('Coherence',f);
            x.pagebreak();
            f=presetfigure('Visible','off');
            ngraph = 1;
        end
        subplot(options.pggraph,options.pggraph,ngraph);
        plot(d_m.freq, squeeze(d_m.coher(i,j,:)));
        hold on;
        if flag
            for ii = 1:length(options.cmp)
                plot(d_m.freq, squeeze(options.cmp(ii).coher(i,j,:)),colors{ii});
            end;
        end
        title([strrep(d_m.listvar{i},'_','_') ' and ' strrep(d_m.listvar{j},'_','_')]);
        if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
        plotper(options.periods);
        xlim([d_m.freq(1) d_m.freq(end)]);
    end
end

x.figure('Coherence',f);
x.pagebreak();

%% gain
ngraph = 0;

f=presetfigure('Visible','off');

for i = 1 : n
    for j = 1:n
        if j == i
        else
            ngraph = ngraph + 1;
            if ngraph > options.pggraph^2
                x.figure('Gain',f);
                x.pagebreak();
                f=presetfigure('Visible','off');
                ngraph = 1;
            end
            subplot(options.pggraph,options.pggraph,ngraph);
            plot(d_m.freq, squeeze(d_m.gain(i,j,:)));
            hold on
            if flag
                for ii = 1:length(options.cmp)
                    plot(d_m.freq, squeeze(options.cmp(ii).gain(i,j,:)),colors{ii});
                end;
            end
            title([ strrep(d_m.listvar{i},'_','_') ' -> ' strrep(d_m.listvar{j},'_','_')]);
            if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
            plotper(options.periods);
            xlim([d_m.freq(1) d_m.freq(end)]);
        end
    end
end

x.figure('Gain',f);
x.pagebreak();

%% time shift
ngraph = 0;

f=presetfigure('Visible','off');


for i = 1 : n-1
    for j = i+1:n
        ngraph = ngraph + 1;
        if ngraph > options.pggraph^2
            x.figure('Phase',f);
            x.pagebreak();
            f=presetfigure('Visible','off');
            ngraph = 1;
        end
        subplot(options.pggraph,options.pggraph,ngraph);
        plot(d_m.freq, squeeze(d_m.phaset(i,j,:)));
        hold on
        if flag
            for ii = 1:length(options.cmp)
                plot(d_m.freq, squeeze(options.cmp(ii).phaset(i,j,:)),colors{ii});
            end;
        end
        title([strrep(d_m.listvar{i},'_','_') ' -> ' strrep(d_m.listvar{j},'_','_')]);
        if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
        plotper(options.periods);
        xlim([d_m.freq(1) d_m.freq(end)]);
    end
end

x.figure('Phase',f);
x.pagebreak();
x.publish([options.fname '.' options.ext],'maketitle',false,'textscale',0.8);
