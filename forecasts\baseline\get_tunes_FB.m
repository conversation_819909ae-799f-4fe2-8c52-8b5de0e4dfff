%**************************************************************************
function [dtunes] = get_tunes_FB(SS)
%**************************************************************************

% Input:	SS		: structure of steady state values and parameters of the model
% Output:	dtunes	: database of tuned tseries 

%**************************************************************************

% Initialize database
dtunes = dbempty();

%% Tunes
% tune_dot_y_star_trend_shift, SS = 1
dtunes.tune_dot_y_star_trend_shift = tseries();
dtunes.tune_dot_y_star_trend_shift(qq(2020,1)) = 0.98;
dtunes.tune_dot_y_star_trend_shift(qq(2020,2)) = 0.91;
dtunes.tune_dot_y_star_trend_shift(qq(2020,3)) = 1.08;

% y_star_gap, SS = 1
dtunes.tune_y_star_gap = tseries();
dtunes.tune_y_star_gap(qq(2014,1)) = 0.9905;
dtunes.tune_y_star_gap(qq(2016,1)) = 0.9902546;
dtunes.tune_y_star_gap(qq(2017,4)) = 1.004;
dtunes.tune_y_star_gap(qq(2018,4)) = 1.003;

% tune_dot_y_star_trend_fund, SS = 1.0039
% dtunes.tune_dot_y_star_trend_fund = tseries();
% dtunes.tune_dot_y_star_trend_fund(qq(2019,1)) = 1.0041;
% dtunes.tune_dot_y_star_trend_fund(qq(2019,2)) = 1.004;
% dtunes.tune_dot_y_star_trend_fund(qq(2019,3)) = 1.0038;
% dtunes.tune_dot_y_star_trend_fund(qq(2019,4)) = 1.0037;
% dtunes.tune_dot_y_star_trend_fund(qq(2020,1)) = 0.987887616;
% dtunes.tune_dot_y_star_trend_fund(qq(2020,2)) = 0.905856895;
% dtunes.tune_dot_y_star_trend_fund(qq(2020,3)) = 1.07969294;
% dtunes.tune_dot_y_star_trend_fund(qq(2020,4)) = 1.00172057;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,1)) = 0.992740139;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,2)) = 1.01871669;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,3)) = 1.01560467;
% dtunes.tune_dot_y_star_trend_fund(qq(2021,4)) = 0.999830427;
% dtunes.tune_dot_y_star_trend_fund(qq(2022,1)) = 1.00352878;
% dtunes.tune_dot_y_star_trend_fund(qq(2022,2)) = 1.00727249;
% dtunes.tune_dot_y_star_trend_fund(qq(2022,3)) = 1.0035918;
% dtunes.tune_dot_y_star_trend_fund(qq(2022,4)) = 1.00431752;


end