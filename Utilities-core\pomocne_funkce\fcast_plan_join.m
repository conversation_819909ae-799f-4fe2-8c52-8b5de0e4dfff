function plan_out = fcast_plan_join(plan1,plan2,m,range)

exog1 =  get_exogenized(plan1);
endog1 = dbextend(get_endogenized(plan1),get_endogenized(plan1,'flag','imag'));
exog1_names=dbnames(exog1);
endog1_names=dbnames(endog1);

exog2 =  get_exogenized(plan2);
endog2 = dbextend(get_endogenized(plan2),get_endogenized(plan2,'flag','imag'));
exog2_names=dbnames(exog2);
endog2_names=dbnames(endog2);

plan_out = plan(m, range);

for now = range(1):range(end)
        for i = 1:length(exog1_names)
            if (exog1.(exog1_names{i})(now) == 1) 
                plan_out = exogenise(plan_out,exog1_names{i},now);
            end
        end
        for i = 1:length(endog1_names)
            if (endog1.(endog1_names{i})(now) == 1) 
                plan_out = endogenise(plan_out,endog1_names{i},now);
            end
        end
        for i = 1:length(exog2_names)
            if isfield(exog1,exog2_names{i})
                if (~(exog1.(exog2_names{i})(now) == 1) && (exog2.(exog2_names{i})(now) == 1))
                    plan_out = exogenise(plan_out,exog2_names{i},now);
                end
            else
                if (exog2.(exog2_names{i})(now) == 1)
                    plan_out = exogenise(plan_out,exog2_names{i},now);
                end               
            end 
        end 
        for i = 1:length(endog2_names)
            if isfield(endog1,endog2_names{i})
                if (~(endog1.(endog2_names{i})(now) == 1) && (endog2.(endog2_names{i})(now) == 1))
                    plan_out = endogenise(plan_out,endog2_names{i},now);
                end
            else
                if (endog2.(endog2_names{i})(now) == 1)
                    plan_out = endogenise(plan_out,endog2_names{i},now);
                end
            end    
        end    
end


end