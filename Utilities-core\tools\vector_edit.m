%-----------------------------------------------------------------------%
% VECTOR EDIT -- GUI for Editing Time-Series Vectors
%-----------------------------------------------------------------------%
% Very SIMPLE routine to edit vectors or time-series usig mouse-drag.
% 
% INPUT:	vector of IRIS time series 
%		Note: full length of the vector/series is plotted
%
% OUTPUT: 	none -- after pressing 'Save and Quit'
%               the full vector/series is stored into workspace
%		under the new name 'edited_series' using original form               
%
%
% Last change: 26/07/2007
% michal.andrle(at)cnb.cz
%-----------------------------------------------------------------------%
function  vector_edit(input_vector)

global d
global mouse_free

if ~ischar(input_vector)

   %--CREATING THE GRAPHICAL USER INTERFACE--%
   %--------not too fancy, but usable--------%
   monitor    = get(0,'ScreenSize');

   fig         = figure( 'Units',       'Pixels', ...
                         'Name',        'Vector Edit ', ...
                         'Tag',         'main_fig', ...
		         'NumberTitle', 'off', ...
		         'Resize',      'on', ...
		         'Menubar',     'none', ...
		         'Position',    [0.37*monitor(3) 0.49*monitor(4) 0.45*monitor(3) 0.4*monitor(4)] ...
		         );
			 
  ax          = axes(   'Units',       'Normalized', ...
                        'Tag',         'main_axes', ...
		        'Color',       'white', ...
		        'Position',    [ 0.1 0.2 0.8 0.7] ...
		        );

	       uicontrol('Units',       'Normalized', ...
	                 'Style',       'Text', ...
	                 'Tag',         'y_text', ...
	                 'Position',    [0.1 0.05 0.1 0.05] ...
	                 );
            
	       uicontrol('Units',       'Normalized', ...
	                 'Style',       'Text', ...
	                 'Tag',         'x_text', ...
	                 'Position',    [0.21 0.05 0.1 0.05] ...
	                 );
          
               uicontrol('Units',       'Normalized', ...
	                 'Style',       'Push', ...
	                 'String',      'Save & Quit!', ...
	                 'Tag',         'save_', ...
	                 'Position',    [0.8 0.05 0.15 0.05], ...
	                 'CallBack',    'vector_edit save_' ...
	                 );
                
  set(findobj('Tag','main_fig'),'WindowButtonMotionFcn','vector_edit mouse_motion_');
  set(findobj('Tag','main_fig'),'Pointer','crosshair');

  set(findobj('Tag','main_fig'),'WindowButtonDownFcn', 'vector_edit click_');
  set(findobj('Tag','main_fig'),'WindowButtonUpFcn', 'vector_edit unclick_');

d.h = draw_figure(input_vector);
d.orig_vector = input_vector;


else

  switch(input_vector)
   case('mouse_motion_')
	   d = get_points(d);
	   set(findobj('Tag','x_text'),'String',num2str(d.x_point));
	   set(findobj('Tag','y_text'),'String',num2str(d.y_point));

   case('click_')
	   mouse_free = false;
	   xfig_data = get(d.h, 'xdata');
	   min_dist = min(diff(xfig_data))/2;
	   d.x_dist = find(abs(xfig_data - d.x_point) <= min_dist);

   case('unclick_')

	   mouse_free = true; % Mickey-the-Mouse is free!

	   yfig_data = get(d.h, 'ydata');
	   yfig_data(d.x_dist) = d.y_point;
	   set(d.h, 'ydata', yfig_data);

   case('save_')

	   yfig_data = get(d.h, 'ydata');

	   if ~istseries(d.orig_vector)
	     assignin('base','edited_series',yfig_data);
	     close;
	   else
	     iris_series = tseries(get(d.orig_vector,'range'), yfig_data);	
	     assignin('base','edited_series', iris_series);
	     close;
	   end

   case('resize_')

 end%-of switch
 
end%-of the initial if 



end%-of main function





function [plot_handle] = draw_figure(to_plot)
  plot_handle = plot(to_plot,'Marker','o');
  grid on;
  if istseries(to_plot)
	  title_string=get(to_plot,'Comment');
	  title(title_string);
  end
  
end


%--initialize axis into global container---------------------------
function [d] = initialize_position(d)

end

%--calculate actual mouse position in units of graph--%
function [d] = get_points(d)

  coordinates = get(findobj('Tag','Figure'),'CurrentPoint');
  coordinates = get(gcf,'CurrentPoint');
  d.x_point   = coordinates(1);
  d.y_point   = coordinates(2);

  d.position_axes = get(gca, 'Position');
  d.position_fig  = get(gcf, 'Position');


  d.position_axes(1) = d.position_fig(3)*d.position_axes(1);
  d.position_axes(2) = d.position_fig(4)*d.position_axes(2);
  d.position_axes(3) = d.position_fig(3)*d.position_axes(3);
  d.position_axes(4) = d.position_fig(4)*d.position_axes(4);
 
  d.xplot         = get(gca, 'Xlim');
  d.yplot         = get(gca, 'ylim');


  d.xmod          = (d.xplot(2)-d.xplot(1))/d.position_axes(3);
  d.ymod          = (d.yplot(2)-d.yplot(1))/d.position_axes(4);
  

  d.x_point   = d.x_point - d.position_axes(1);
  d.y_point   = d.y_point - d.position_axes(2);


  d.x_point   = d.xmod * d.x_point + d.xplot(1);
  d.y_point   = d.ymod * d.y_point + d.yplot(1);

end






