%% Output gap identification/decomposition - new proposed design
%   - This script provides main steps in output gap identification and subsequent decomposition 
%   - The main driver is driver_outputgap
%
% @JanZacek412

% 1] Run settings
%   - driver_settings sets the initial settings for all other drivers
%   - set settings in line with the current forecasting round

driver_settings;

% 2] Run endogenous decomposition of real GDP
%   - driver_outputgap runs filter on the prolonged fcast horizon
%   - use get_tunes to tune GDP decomposition on the whole prolonged horizon
%   - this driver produces adjusted histcore with GDP components (gap, trend, trend shift) 

driver_outputgap;

% 3] Run filter and fcast using the output from the second step
%   - driver_filter and driver_fcast are usual drivers without any changes
%   - you can run them "mechanically"

driver_filter; driver_fcast;

% 4] Run fcast/SS decompositions

driver_decomposition;
