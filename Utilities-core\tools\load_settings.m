%**************************************************************************
function [settings, varargout] = load_settings(ID, varargin)
%**************************************************************************

% Input:		ID		- string (usually report prefix) for the main forecast
% 
%	options:
%				'old_ID'		- string (usually report prefix) for the compared forecast
%				'extend'		- prefix extension - must be defined in settings
%				'extend_old'	- prefix extension for the compared forecast -
%									must be defined in settings for old forecast
%				'path'			- location of folder database for input and output
%
% Output:		settings, settings_old, settings_cmp	- structure containing all revelant data	
%
% This function loads a data structure for one (and optionally two) forecasts specification(s) like: 
% output data, labels, dates, file names etc.

% @ last revision ZH feb-2018

%**************************************************************************


%% READ INPUT

default = { ...
		'old_id',		'', ...
		'extend',		'', ...
		'extend_old',	'', ...
		'dbpath',		'..\database', ...
			};
options = passopt(default,varargin{:});

% Load options - prefer indata
if ischar(ID)
	if isequal(exist([options.dbpath '\Input-data\' ID '-settings.mat'], 'file'), 2)
		load([options.dbpath '\Input-data\' ID '-settings.mat']);
		settings = FB_ID; clear ID;
		settings.ID_name = [options.dbpath '\Input-data\' settings.report_prefix '-settings.mat'];
	elseif isequal(exist([options.dbpath '\Output-data\' ID '-settings.mat'], 'file'), 2)
		load([options.dbpath '\Output-data\' ID '-settings.mat']);
		settings = FB_ID; clear ID;
		settings.ID_name = [options.dbpath '\Output-data\' settings.report_prefix '-settings.mat'];
	else
		error(['load_settings: Basic settings for ' ID ' not found!']);
	end
else
	disp('load_settings: First input is not a string!');
end
settings.indata_dir       = [options.dbpath '\Input-data'];
settings.outdata_dir      = [options.dbpath '\Output-data'];
settings.outreport_dir    = [options.dbpath '\Output-reports'];
settings.outgraph_dir     = [options.dbpath '\Output-graphs'];

% Find second ID (optional) 
if ~isempty(options.old_id)
	if ischar(options.old_id)
		settings.oldfrc_prefix = options.old_id;
	else
		disp('load_settings: Second input is not a string!');
	end
end
if  isfield(settings,'oldfrc_prefix')
	old_ID = settings.oldfrc_prefix;
end

% Load old options - prefer indata (optional)
if exist('old_ID','var')
	if isequal(exist([options.dbpath '\Input-data\' old_ID '-settings.mat'], 'file'), 2)
		load([options.dbpath '\Input-data\' old_ID '-settings.mat']);
		settings_old = FB_ID; clear FB_ID;
		settings_old.ID_name = [options.dbpath '\Input-data\' settings_old.report_prefix '-settings.mat'];
	elseif isequal(exist([options.dbpath '\Output-data\' old_ID '-settings.mat'], 'file'), 2)
		load([options.dbpath '\Output-data\' old_ID '-settings.mat']);
		settings_old = FB_ID; clear FB_ID;
		settings_old.ID_name = [options.dbpath '\Output-data\' settings_old.report_prefix '-settings.mat'];
	else
		warning(['Basic settings for ' old_ID ' not found!']);
		clear old_ID;
	end
	settings_old.indata_dir       = [options.dbpath '\Input-data'];
	settings_old.outdata_dir      = [options.dbpath '\Output-data'];
	settings_old.outreport_dir    = [options.dbpath '\Output-reports'];
	settings_old.outgraph_dir     = [options.dbpath '\Output-graphs'];
end

% Analyse extend (optional)
if ~isempty(options.extend) && isfield(settings,'extend')
	if isfield(settings.extend,options.extend)
		suffix = options.extend;
	else 
		error(['load_settings: Unknown prefix extension ', options.extend, '!']);
	end
elseif ~isempty(options.extend)
	error(['load_settings: settings for extension ', options.extend, ' not found!']);
else
	suffix  = '';
end

% Analyse old extend (optional)
if ~isempty(options.extend_old) && isfield(settings_old,'extend')
	if isfield(settings_old.extend,options.extend_old)
		suffix_old = options.extend_old;
	else 
		error(['load_settings: Unknown old prefix extension ', options.extend_old, '!']);
	end
elseif ~isempty(options.extend_old)
	error(['load_settings: old settings for extension ', options.extend_old, ' not found!']);
else
	suffix_old  = '';
end

%% FILE NAMES

special_name = { ...
	'histcore_name'
	'adhoc_data_fb'
				};
            
standard_name = { ...
% 	'NAME'							'-FILE_EXTENTION'
% Filter
    'origdata_fb_name'				'-data-fb.csv'	
    'filter_fb_name'				'-filter-fb.csv'
	'filterdata_fb_name'			'-filterdata-fb.mat'
	'prefilter_fb_name'				'-pre-filter-fb.csv'
	'kalman_fb_name'				'-kalman-fb.mat'
    'tunes_fb_name'					'-tunes-fb.mat'    
    'filterss_fb_name'				'-filterSS-fb.mat'        
% 	'prefilterextend_fb_name'		'-pre-filter-extend-fb.csv'    
% Forecast    
	'model_fb_name'					'-model-fb.mat'
    'forecastss_fb_name'			'-forecastSS-fb.mat'
    'plan_fb_name'					'-plan-fb.mat'    
	'expectations_fb_name'			'-expectations-fb.mat'    
	'preforecast_fb_name'			'-pre-forecast-fb.csv'
	'data_fb_name'					'-forecast-fb.csv'
    'ss_fb_name'					'-SS-fb.mat'
% 	'data_pure_fb_name'				'-forecast-pure-fb.csv'
	'book_fb_name'					'-forecast-book-fb.csv'
% 	'plan_surprise_fb_name'			'-plan-surprise-fb.mat'	
% 	'yy_data_name'					'-yearly-forecast.csv'
				};

%% NEW

% find special name files:
for ix = 1:length(special_name)
	if isfield(settings, special_name{ix})
		if isequal(exist([settings.indata_dir '\' settings.(special_name{ix})], 'file'), 2)
				settings.(special_name{ix}) = [settings.indata_dir '\' settings.(special_name{ix})];
		elseif isequal(exist([settings.outdata_dir '\' settings.(special_name{ix})], 'file'), 2)
				settings.(special_name{ix}) = [settings.outdata_dir '\' settings.(special_name{ix})];
		else
			warning(['New ' special_name{ix} ' file not found!']);
		end
	else
		warning([special_name{ix} ' not defined in new settings!']);
	end
end

% find standard name files:
for ix = 1:length(standard_name)
	if ~isempty(suffix)
		if isequal(exist([settings.indata_dir '\' settings.extend.(suffix) standard_name{ix,2}], 'file'), 2)
			settings.(standard_name{ix,1}) = [settings.indata_dir '\' settings.extend.(suffix) standard_name{ix,2}];
		elseif isequal(exist([settings.outdata_dir '\' settings.extend.(suffix) standard_name{ix,2}], 'file'), 2)
			settings.(standard_name{ix,1}) = [settings.outdata_dir '\' settings.extend.(suffix) standard_name{ix,2}];
		elseif isequal(exist([settings.indata_dir '\' settings.report_prefix standard_name{ix,2}], 'file'), 2)
			settings.(standard_name{ix,1}) = [settings.indata_dir '\' settings.report_prefix standard_name{ix,2}];
		elseif isequal(exist([settings.outdata_dir '\' settings.report_prefix standard_name{ix,2}], 'file'), 2)
			settings.(standard_name{ix,1}) = [settings.outdata_dir '\' settings.report_prefix standard_name{ix,2}];
		else
			warning(['New ' standard_name{ix,1} ' file not found!']);
		end
	else
		if isequal(exist([settings.indata_dir '\' settings.report_prefix standard_name{ix,2}], 'file'), 2)
			settings.(standard_name{ix,1}) = [settings.indata_dir '\' settings.report_prefix standard_name{ix,2}];
		elseif isequal(exist([settings.outdata_dir '\' settings.report_prefix standard_name{ix,2}], 'file'), 2)
			settings.(standard_name{ix,1}) = [settings.outdata_dir '\' settings.report_prefix standard_name{ix,2}];
		else
			warning(['New ' standard_name{ix,1} ' file not found!']);
		end
	end
end

settings.headline_prefix = settings.report_prefix;
if ~isempty(suffix)
	settings.report_prefix = settings.extend.(suffix);
end

% Histcore extended for GDP decomposition
settings.histcore_name_adj	= strrep(settings.histcore_name,'.csv','-adj.csv');

%% OLD

if exist('old_ID','var')
	
	% find special name files:
	for ix = 1:length(special_name)
		if isfield(settings_old, special_name{ix})
			if isequal(exist([settings_old.indata_dir '\' settings_old.(special_name{ix})], 'file'), 2)
					settings_old.(special_name{ix}) = [settings_old.indata_dir '\' settings_old.(special_name{ix})];
			elseif isequal(exist([settings_old.outdata_dir '\' settings_old.(special_name{ix})], 'file'), 2)
					settings_old.(special_name{ix}) = [settings_old.outdata_dir '\' settings_old.(special_name{ix})];
			else
				warning(['New ' special_name{ix} ' file not found!']);
			end
		else
			warning([special_name{ix} ' not defined in old settings!']);
		end
	end

	% find standard name files:
	for ix = 1:length(standard_name)
		if ~isempty(suffix_old)
			if isequal(exist([settings_old.indata_dir '\' settings_old.extend.(suffix_old) standard_name{ix,2}], 'file'), 2)
				settings_old.(standard_name{ix,1}) = [settings_old.indata_dir '\' settings_old.extend.(suffix_old) standard_name{ix,2}];
			elseif isequal(exist([settings_old.outdata_dir '\' settings_old.extend.(suffix_old) standard_name{ix,2}], 'file'), 2)
				settings_old.(standard_name{ix,1}) = [settings_old.outdata_dir '\' settings_old.extend.(suffix_old) standard_name{ix,2}];
			elseif isequal(exist([settings_old.indata_dir '\' settings_old.report_prefix standard_name{ix,2}], 'file'), 2)
				settings_old.(standard_name{ix,1}) = [settings_old.indata_dir '\' settings_old.report_prefix standard_name{ix,2}];
			elseif isequal(exist([settings_old.outdata_dir '\' settings_old.report_prefix standard_name{ix,2}], 'file'), 2)
				settings_old.(standard_name{ix,1}) = [settings_old.outdata_dir '\' settings_old.report_prefix standard_name{ix,2}];
			else
				warning(['Old ' standard_name{ix,1} ' file not found!']);
			end
		else
			if isequal(exist([settings_old.indata_dir '\' settings_old.report_prefix standard_name{ix,2}], 'file'), 2)
					settings_old.(standard_name{ix,1}) = [settings_old.indata_dir '\' settings_old.report_prefix standard_name{ix,2}];
			elseif isequal(exist([settings_old.outdata_dir '\' settings_old.report_prefix standard_name{ix,2}], 'file'), 2)
					settings_old.(standard_name{ix,1}) = [settings_old.outdata_dir '\' settings_old.report_prefix standard_name{ix,2}];
			else
				warning(['Old ' standard_name{ix,1} ' file not found!']);
			end
		end
	end
	
	settings_old.headline_prefix = settings_old.report_prefix;
	settings.oldhead_prefix = settings_old.headline_prefix;
	if ~isempty(suffix_old)
		settings_old.report_prefix = settings_old.extend.(suffix_old);
		settings.oldfrc_prefix = settings_old.report_prefix;
	end
	
end


%% CMP

if exist('old_ID','var')

	settings_cmp.cmpreport_prefix = [settings.report_prefix '-v-' settings_old.report_prefix]; % define compare report prefix
	settings_cmp.cmpheadline_prefix = [settings.headline_prefix '-v-' settings_old.headline_prefix];
	
	settings_cmp.end_pred		= settings.end_pred;

	settings_cmp.hist_rng		= settings_old.hrng;
	settings_cmp.trans_rng		= settings_old.start_pred:settings.ehist;
	settings_cmp.fut_rng		= settings.comprng;

	settings_cmp.indata_dir       = [options.dbpath '\Input-data'];
	settings_cmp.outdata_dir      = [options.dbpath '\Output-data'];
	settings_cmp.outreport_dir    = [options.dbpath '\Output-reports'];
	settings_cmp.outgraph_dir     = [options.dbpath '\Output-graphs'];

end


%% OPTIONAL OUTPUT

if nargout > 1
	varargout = cell(1,nargout-1);
	if exist('old_ID','var')
		varargout{1} = settings_old;
		varargout{2} = settings_cmp;
	end
end 

end % of main function
