function [ savedOK ] = rename_variables_in_CSV(file_CSV, forecastlabel)
%% FUNCTION NAME:
%  rename_variables_in_CSV
%
% DESCRIPTION
%    This function removes the 'forecastlabel' suffix from variables in .csv
%    file extracted from DPSZ database
%
% INPUT
%    file_CSV      - (string) .csv file with variables to rename
%    forecastlabel - (string, version number) that will be removed
%                     from the variables name 
%
% OUTPUT
%    savedOK - (double) 1/0 flag for successful file creation
%
% Nov 2020, <PERSON><PERSON><PERSON>

sheet_old      = dbload(file_CSV);
names_old      = fieldnames(sheet_old);
names_new      = strrep(names_old, ['_',forecastlabel], '');

for k = 1:length(names_old)
    sheet_new.(names_new{k}) = sheet_old.(names_old{k});
end

dbsave(sheet_new, file_CSV, Inf, 'decimal', 16);
savedOK = length(sheet_old) ==  length(sheet_new);

clearvars sheet_new sheet_old names_new names_old;
end

