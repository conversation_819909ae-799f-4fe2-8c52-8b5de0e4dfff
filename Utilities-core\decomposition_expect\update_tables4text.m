function update_tables4text(Tablesfile, odpocet, sr_opt, plot_range, ...
	old, mod, unexp, full, now)

disp(['Exporting to ' Tablesfile '.']);

simlist = {	'old'			'Original'
			'mod'			'Model'
			'unexp'			'Unexpected'
			'full'			'Full'
			'now'			'Actual'
				};

SS = load(sr_opt.new.ss_name);


%% Define lists
% It is important to add new series at the end, otherwise calculations in
% adjacent Excel file will be destroyed.
          
listlongNOW = {  
          'ne_zz_dot_cpi_tax4',...
            'ne_zz_dot_p_tax4',...
             'ne_zz_dot_pREG4',...
              'ne_zz_dot_cpi4',...
                'ne_zz_dot_p4',...
                     'ne_zz_i',...
              'ne_zz_dot_gdp4',...
                     'ne_zz_s',...
                'ne_zz_dot_w4',...
                'ne_zz_dot_c4',...
                'ne_zz_dot_j4',...
                'ne_zz_dot_x4',...
                'ne_zz_dot_n4',...
                'ne_zz_dot_g4',...
           'ne_zz_dot_n_star4',...
     'ne_zz_dot_p_star_tilde4',...
                'ne_zz_i_star',...
               'taxes_effects',...
                 'zz_dot_cpi4',...  
				 'ne_zz_i_star_shadow',...
                 'brent'
              };          

listlong = listlongNOW;
          
listshortNOW = { 
              'ne_zz_dot_cpi_tax4',...
		   	    'ne_zz_dot_p_tax4',...
                         'ne_zz_i',...
                     'zz_dot_cpi4',...   
				  'ne_zz_dot_cpi4',...
               };           
          

%% Export

now.taxes_effects = (1-SS.wdot_reg_)*(now.ne_zz_dot_p_tax4-now.ne_zz_dot_p4);
old.taxes_effects = (1-SS.wdot_reg_)*(old.ne_zz_dot_p_tax4-old.ne_zz_dot_p4);

old = dbclip(old*listlong,plot_range);
now = dbclip(now*listlongNOW,plot_range);
mod = dbclip(mod*listshortNOW,plot_range);
unexp = dbclip(unexp*listshortNOW,plot_range);
full = dbclip(full*listshortNOW,plot_range);

for ix = 1 : size(simlist,1)
	dbsave(eval(simlist{ix,1}), ['..\database\Output-data\table_' simlist{ix,1} '.csv']);
	[data,~]= readtext(['..\database\Output-data\table_' simlist{ix,1} '.csv'],',','','"');
	for ii = 1 : size(data,2)
		data(3,ii) = strrep(data(3,ii),'"','');
		data(2,ii) = strrep(data(2,ii),'"','');
		data(1,ii )= strrep(data(1,ii),'"','');
	end;
	for ii = 1:size(data,1)
		data(ii,1) = strrep(data(ii,1),'"','');
	end;
	xlswrite(Tablesfile, data, simlist{ix,2});
end

dbsave(odpocet,'..\database\Output-data\ODPOCET.csv');
[data,~]= readtext('..\database\Output-data\ODPOCET.csv',',','','"');
for ii = 1 : size(data,2)
    data(3,ii) = strrep(data(3,ii),'"','');
    data(2,ii) = strrep(data(2,ii),'"','');
    data(1,ii) = strrep(data(1,ii),'"','');
end;
for ii = 1 : size(data,1)
    data(ii,1) = strrep(data(ii,1),'"','');
end;
xlswrite(Tablesfile, data, 'ODPOCET');

