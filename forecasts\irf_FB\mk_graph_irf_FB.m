function mk_graph_irf(irf,shockList,specShockList,responseList,specResponseList, ...
                      modelNick,sr_opt)
                  

if ~length(sr_opt.formatSubplot)==2
	sr_opt.formatSubplot = [1,1];
end
numSubplot = sr_opt.formatSubplot(1)*sr_opt.formatSubplot(2);

modelID     = fieldnames(irf);
typeShockID = fieldnames(irf.(modelID{1}));
hitPeriodID = fieldnames(irf.(modelID{1}).(typeShockID{1}));
hitPeriodID = hitPeriodID(1:length(sr_opt.hitPeriod));

% if length(shockList)*length(responseList)*length(typeShockID)/numSubplot > 50
%     error('Too many graphs to draw. Shrink the set of shocks and/or responses!');
% end

typslide = 'ppt2';
if sr_opt.doPPT
    abs_path  = absolute_path(sr_opt.new.outgraph_dir);
    pfilename = [abs_path '\' 'IRF.pptx'];
	if isequal(exist(pfilename,'file'),2)
		delete(pfilename);
	end
end

if sr_opt.varTransform
	strVarTrans = 'zz_';
else
	strVarTrans = '';
end

if sr_opt.one_graph_compare ~= 1
	
	for i = 1 : length(shockList)
		for ii = 1 : length(typeShockID)
			
			if length(modelID) > 1
				
				for j = 1 : length(responseList)
					modID = '';
					for k = 1 : length(modelID)
						if ~ismember(shockList{i},specShockList.(modelID{k})) && ...
								~ismember(responseList{j},specResponseList.(modelID{k}))
							modID = ['m',num2str(k,'%02.0f')];
						end
					end
					if ~isempty(modID)
						if sr_opt.varnamedisp
							graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
								shockList{i}  ' to ' responseList{j}]; 
                        else
                            if length(hitPeriodID) == 1
                                graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
                                    char(irf.(modID).(typeShockID{ii}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment)];
                            else
                                graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
                                    char(irf.(modID).(typeShockID{ii}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ' to ' ...
                                    char(irf.(modID).(typeShockID{ii}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment)];
                            end
						end
						numEmptyPlot = 0;
						for k = 1 : length(modelID)
							if ~ismember(shockList{i},specShockList.(modelID{k})) && ...
								~ismember(responseList{j},specResponseList.(modelID{k}))
								if isequal(mod(k-numEmptyPlot,numSubplot),1)
									figure('Name',graphTitle);
								end
								subTitle = modelNick{k}; 
								subplot(sr_opt.formatSubplot(1),sr_opt.formatSubplot(2), ...
									isequal(mod(k-numEmptyPlot,numSubplot),0)*numSubplot+mod(k-numEmptyPlot,numSubplot));
								strTSeries = '[';
								subLegend = '{';
								for ix = 1 : length(hitPeriodID)
									strTSeries = [strTSeries ' irf.' modelID{k} '.' typeShockID{ii} ...
										'.' hitPeriodID{ix} '.' shockList{i} '.' strVarTrans responseList{j}];
									subLegend = [subLegend ' ''' hitPeriodID{ix} ''' '];
								end
								strTSeries = [strTSeries ' ]'];
								subLegend = [subLegend ' }'];
								if isequal(mod(k-numEmptyPlot,numSubplot),mod(sr_opt.lgndInSubplot,numSubplot)) % legend in subtitle
									plotg(eval(strTSeries),1:sr_opt.responseLength, ...
										'palette',1,'colors',11, ...
										'type',typslide,'title',subTitle,'legend',eval(subLegend));
								else
									plotg(eval(strTSeries),1:sr_opt.responseLength, ...
										'palette',1,'colors',11, ...
										'type',typslide,'title',subTitle);
								end
								if sr_opt.doPPT && (isequal(mod(k-numEmptyPlot,numSubplot),0) || isequal(k,length(modelID)))
									saveppt('cnb_eng',pfilename,graphTitle,graphTitle);
								end
							else
								numEmptyPlot = numEmptyPlot+1;
								if sr_opt.doPPT && isequal(k,length(modelID))
									saveppt('cnb_eng',pfilename,graphTitle,graphTitle);
								end
							end
						end
                    end            
				end
				
			else
				
				modID = ['m',num2str(1,'%02.0f')];
				if sr_opt.varnamedisp
					graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
						shockList{i} ]; 
				else
					graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
						char(irf.(modID).(typeShockID{ii}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ]; 
				end
				for j = 1 : length(responseList)
					if sr_opt.varnamedisp
						subTitle = strrep(responseList{j},'_','\_');
					else
						subTitle = char(irf.(modID).(typeShockID{ii}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment);
					end
					if isequal(mod(j,numSubplot),1)
						figure('Name',graphTitle);
					end
					subplot(sr_opt.formatSubplot(1),sr_opt.formatSubplot(2),isequal(mod(j,numSubplot),0)*numSubplot+mod(j,numSubplot));
					strTSeries = '[';
					subLegend = '{';
					for ix = 1 : length(hitPeriodID)
						strTSeries = [strTSeries ' irf.' modID '.' typeShockID{ii} ...
							'.' hitPeriodID{ix} '.' shockList{i} '.' strVarTrans responseList{j}];
						subLegend = [subLegend ' ''' hitPeriodID{ix} ''' '];
					end
					strTSeries = [strTSeries ' ]'];
					subLegend = [subLegend ' }'];
					if isequal(mod(j,numSubplot),mod(sr_opt.lgndInSubplot,numSubplot)) % legend in subtitle
						plotg(eval(strTSeries),1:sr_opt.responseLength, ...
							'type',typslide,'title',subTitle,'legend',eval(subLegend));
					else
						plotg(eval(strTSeries),1:sr_opt.responseLength, ...
							'type',typslide,'title',subTitle);
					end
					if sr_opt.doPPT && (isequal(mod(j,numSubplot),0) || isequal(j,length(responseList)))
						saveppt('cnb_eng',pfilename,graphTitle,graphTitle);
					end
				end
				
			end
			
		end
	end
	
else
	
	for i = 1 : length(shockList)
		
		if length(modelID)==1 && length(typeShockID)==2
			if sr_opt.varnamedisp
				graphTitle = [shockList{i} ' in period ' hitPeriodID{1}(4:end)]; 
			else
				graphTitle = [char(irf.(modelID{1}).(typeShockID{1}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ...
					' in period ' hitPeriodID{1}(4:end)]; 
			end
			for j = 1 : length(responseList)
				if sr_opt.varnamedisp
					subTitle = strrep(responseList{j},'_','\_');
				else
					subTitle = char(irf.(modelID{1}).(typeShockID{1}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment);
				end
				if isequal(mod(j,numSubplot),1)
					figure('Name',graphTitle);
				end
				subplot(sr_opt.formatSubplot(1),sr_opt.formatSubplot(2),isequal(mod(j,numSubplot),0)*numSubplot+mod(j,numSubplot));
				strTSeries = '[';
				subLegend = '{';
				for ii = 1 : length(typeShockID)
					strTSeries = [strTSeries ' irf.' modelID{1} '.' typeShockID{ii} ...
						'.' hitPeriodID{1} '.' shockList{i} '.' strVarTrans responseList{j}];
					subLegend = [subLegend ' ''' typeShockID{ii} 'ed' ''' '];
				end
				strTSeries = [strTSeries ' ]'];
				subLegend = [subLegend ' }'];
				if isequal(mod(j,numSubplot),mod(sr_opt.lgndInSubplot,numSubplot)) % legend in subtitle
					plotg(eval(strTSeries),1:sr_opt.responseLength, ...
						'type',typslide,'title',subTitle,'legend',eval(subLegend));
				else
					plotg(eval(strTSeries),1:sr_opt.responseLength, ...
						'type',typslide,'title',subTitle);
				end
				if sr_opt.doPPT && (isequal(mod(j,numSubplot),0) || isequal(j,length(responseList)))
					saveppt('cnb_eng',pfilename,graphTitle,graphTitle);
				end
			end
		else
			for ii = 1 : length(typeShockID)
				for k = 1 : length(modelID)
					if ~ismember(shockList{i},specShockList.(modelID{k}))
						modID = ['m',num2str(k,'%02.0f')];
					end
				end
				if sr_opt.varnamedisp
                    if length(hitPeriodID) == 1
                        graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
                            shockList{i} ' in period ' hitPeriodID{1}(4:end)];
                    else
                        graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
                            shockList{i} ' in period ' hitPeriodID{1}(4:end) ' to ' hitPeriodID{end}(4:end)];
                    end
				else
					graphTitle = [upper(typeShockID{ii}(1)) typeShockID{ii}(2:end) 'ed ' ...
						char(irf.(modID).(typeShockID{ii}).(hitPeriodID{1}).(shockList{i}).(shockList{i}).comment) ...
						' in period ' hitPeriodID{1}(4:end)]; 
				end
				numEmptyPlot = 0;
				for j = 1 : length(responseList)
					modID = '';
					for k = 1 : length(modelID)
						if ~ismember(shockList{i},specShockList.(modelID{k})) && ...
								~ismember(responseList{j},specResponseList.(modelID{k}))
							modID = ['m',num2str(k,'%02.0f')];
						end
					end
					if isempty(modID)
						numEmptyPlot = numEmptyPlot+1;
						if sr_opt.doPPT && ~isequal(mod(j-numEmptyPlot,numSubplot),0) ...
								&& isequal(j,length(responseList))
							saveppt('cnb_eng',pfilename,graphTitle,graphTitle);
						end
					else
						if sr_opt.varnamedisp || isempty(modID)
							subTitle = strrep(responseList{j},'_','\_');
						else
							subTitle = char(irf.(modID).(typeShockID{ii}).(hitPeriodID{1}).(shockList{i}).(responseList{j}).comment);
						end
						if isequal(mod(j-numEmptyPlot,numSubplot),1)
							figure('Name',graphTitle);
						end
						subplot(sr_opt.formatSubplot(1),sr_opt.formatSubplot(2),isequal(mod(j-numEmptyPlot,numSubplot),0)*numSubplot+mod(j-numEmptyPlot,numSubplot));
						strTSeries = '[';
						subLegend = '{';
						for k = 1 : length(modelID)
                                for jj = 1:length(hitPeriodID)
                                    strTSeries = [strTSeries ' irf.' modelID{k} '.' typeShockID{ii} ...
                                        '.' hitPeriodID{jj} '.' shockList{i} '.' strVarTrans responseList{j}];
                                    subLegend = [subLegend ' ''' modelNick{k} ' ' hitPeriodID{jj}(4:end) ''' '];
                                end
                        end
						strTSeries = [strTSeries ' ]'];
						subLegend = [subLegend ' }'];
                        strTSeriesEval = eval(strTSeries);
                        colorscheme = [0 0 0];
                        if isequal(mod(j-numEmptyPlot,numSubplot),mod(sr_opt.lgndInSubplot,numSubplot)) % legend in subtitle
                            plotg2(strTSeriesEval,1:sr_opt.responseLength, length(modelID), ...
                                'type',typslide,'title',subTitle,'legend',eval(subLegend),'colors',11);
                        else
                            plotg2(strTSeriesEval,1:sr_opt.responseLength, length(modelID),...
                                'type',typslide,'title',subTitle,'legend',eval(subLegend),'colors',11);
                        end
						if sr_opt.doPPT && (isequal(mod(j-numEmptyPlot,numSubplot),0) || isequal(j,length(responseList)))
							saveppt('cnb_eng',pfilename,graphTitle,graphTitle);
						end
					end
				end
			end
		end
	end

end
