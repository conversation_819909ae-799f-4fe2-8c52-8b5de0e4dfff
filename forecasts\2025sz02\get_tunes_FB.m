%**************************************************************************
function [dtunes] = get_tunes_FB(SS)
%**************************************************************************

% Input:	SS		: structure of steady state values and parameters of the model
% Output:	dtunes	: database of tuned tseries 

%**************************************************************************

% Initialize database
dtunes = dbempty();
if SS.FB_gdp_decomp == 1
    %% Perma Tunes
    % tune_dot_y_star_trend_shift, SS = 1
    dtunes.tune_dot_y_star_trend_shift = tseries();

    %original
    dtunes.tune_dot_y_star_trend_shift(qq(2020,1)) = 0.98;%0.975; %0.98
    dtunes.tune_dot_y_star_trend_shift(qq(2020,2)) = 0.915;%0.91; %0.91
    dtunes.tune_dot_y_star_trend_shift(qq(2020,3)) = 1.087;%1.076; % 1.08
    dtunes.tune_dot_y_star_trend_shift(qq(2020,4)) = 1.01;
    dtunes.tune_dot_y_star_trend_shift(qq(2021,1)) = 0.995; %0.985
    dtunes.tune_dot_y_star_trend_shift(qq(2021,2)) = 1.02;

    % y_star_gap, SS = 1
    dtunes.tune_y_star_gap = tseries();

  
    dtunes.tune_y_star_gap(qq(2005,1)) = 0.99;

    dtunes.tune_y_star_gap(qq(2009,1)) = 0.962;
    dtunes.tune_y_star_gap(qq(2011,1)) = 1.00809;
    dtunes.tune_y_star_gap(qq(2011,2)) = 1.0070905;
    dtunes.tune_y_star_gap(qq(2011,3)) = 1.0074905;
    dtunes.tune_y_star_gap(qq(2011,4)) = 1.0050905;
    dtunes.tune_y_star_gap(qq(2012,1)) = 1.0040905;
    dtunes.tune_y_star_gap(qq(2021,4)) = 1.01690517;

    % tune_dot_y_star_trend_fund, SS = 1.0039515
    dtunes.tune_dot_y_star_trend_fund = tseries();

    dtunes.tune_dot_y_star_trend_fund(qq(2019,4)) = 1.003541;
    dtunes.tune_dot_y_star_trend_fund(qq(2020,2)) = 1.0001;
    dtunes.tune_dot_y_star_trend_fund(qq(2020,3)) = 0.999;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,1)) = 0.9995;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,2)) = 1.0008;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,3)) = 1.0013;
    % dtunes.tune_dot_y_star_trend_fund(qq(2021,4)) = 1.0017;
    dtunes.tune_dot_y_star_trend_fund(qq(2022,1)) = 1.0032;
    dtunes.tune_dot_y_star_trend_fund(qq(2022,2)) = 1.0046; %
    dtunes.tune_dot_y_star_trend_fund(qq(2022,3)) = 1.0049; %
    dtunes.tune_dot_y_star_trend_fund(qq(2022,4)) = 1.0043; %
    dtunes.tune_dot_y_star_trend_fund(qq(2023,1)) = 1.00388 ;%1.0037
    dtunes.tune_dot_y_star_trend_fund(qq(2023,2)) = 1.00355 ;%1.0034
    dtunes.tune_dot_y_star_trend_fund(qq(2023,3)) = 1.00348 ;% 1.0032
    %% Temp Tunes
    
  %   dtunes.tune_y_star_gap(qq(2021,4)) = 1.01690517;

% 
%  
  dtunes.tune_dot_y_star_trend_fund(qq(2024,1)) = 1.003052 ;
%   
   dtunes.tune_dot_y_star_trend_fund(qq(2024,3)) = 1.002379 ;
% %   
  dtunes.tune_dot_y_star_trend_fund(qq(2024,4)) = 1.0019902 ;
  
   dtunes.tune_dot_y_star_trend_fund(qq(2025,1)) = 1.00173352 ; %1.00172452 ;
   
   dtunes.tune_dot_y_star_trend_fund(qq(2025,2)) = 1.00170617 ;
% %  
  dtunes.tune_dot_y_star_trend_fund(qq(2025,3)) = 1.00170149 ; %002099 ;
% %  
   dtunes.tune_dot_y_star_trend_fund(qq(2025,4)) = 1.001850049  ;
% 

    dtunes.tune_dot_y_star_trend_fund(qq(2026,1)) = 1.0019454049  ;
  dtunes.tune_dot_y_star_trend_fund(qq(2026,2)) = 1.002098510452 ; 
% %  

    dtunes.tune_dot_y_star_trend_fund(qq(2026,3)) = 1.0023010452 ; 
   dtunes.tune_dot_y_star_trend_fund(qq(2026,4)) = 1.0025793 ; 
   
   
   dtunes.tune_dot_y_star_trend_fund(qq(2027,2)) = 1.0029193 ;

end


 
%  
% 
%  


% 
% 
%   dtunes.tune_dot_y_star_trend_fund(qq(2024,1)) = 1.003132 ;
%   
%  dtunes.tune_dot_y_star_trend_fund(qq(2024,3)) = 1.002329 ;
%   
%  dtunes.tune_dot_y_star_trend_fund(qq(2025,1)) = 1.00190452 ; %1.001994 ;
%  
%  dtunes.tune_dot_y_star_trend_fund(qq(2025,3)) = 1.001959 ; %002099 ;
%  
%  
%  dtunes.tune_dot_y_star_trend_fund(qq(2026,2)) = 1.00240452 ; 
%  
%  dtunes.tune_dot_y_star_trend_fund(qq(2026,4)) = 1.002753 ; 
%  
 


end