function report_acf(d_m, varargin)

% Create ACF report for data or a model.
% Calling: report_acf(d_m, varargin)
%         where d_m is struct outcome of data_moments function or
%         model_moments function
% Options: 'fname' ... file name (without extension)
%          'ext'   ... file type (ps or pdf)
%          'cmp'   ... database for a comparison
%          'order' ... order, default is the highest present 

%% default
default =	{'fname', 'acf',  ...
             'ext',    'pdf', ...
             'cmp',    [],...
             'pggraph', 3,...
             'order',[]};


options = passopt(default,varargin{1:end});
colors = {'red','black','green','yellow'};
markers = {'square','diamond','*','+'};

if isempty(options.order)
    options.order = size(d_m.corr,3);
end;

%% compare flag
if isempty(options.cmp)
    flag = 0;
else
    flag = 1;
end

%% test if variables in list are present in both databases
if flag == 1
    
    for ii =1:length(options.cmp)
        if ~strcmp(d_m.listvar, options.cmp(ii).listvar)
            error(['The databases have to have same variables including ordering.'])
        end;
        
    end;
    
end;

%% compare data and data is not available
if flag
    if strcmp(d_m.type, 'data')
        for ii =1:length(options.cmp)
            if strcmp(options.cmp(ii).type , 'data')
                error('Compare data moments is not possible.')
            end
        end;
        
    else
        for ii =1:length(options.cmp)
            if strcmp(options.cmp(ii).type , 'data')
                pom = d_m;
                d_m = options.cmp(ii);
                options.cmp(ii) = pom;
            end
        end;
    end;
end;

%% number of variables
n = length(d_m.listvar);

%% legends
if strcmp(d_m.type, 'data')
    legend_str{1} = 'Bootstrap';
    legend_str{2} = 'Sample';
    shift =2;
else
    legend_str{1} = d_m.label;
    shift =1;
end;
if flag == 1
    for ii=1:length(options.cmp)
        legend_str{ii+shift} = options.cmp(ii).label;
    end;
end;
%% reporting
x = report.new(['Analysis of moments: Order ', num2str(options.order-1)] ,'visible',true);

f = presetfigure('Visible','off');

ngraph = 0;

%% Relative std errors

for i = 1 : n-1
    ngraph = ngraph + 1;
    if ngraph>options.pggraph^2
        x.figure('Relative std',f);
        x.pagebreak();
        f=presetfigure('Visible','off');
        ngraph = 1;
    end
    subplot(options.pggraph,options.pggraph,ngraph);
    if strcmp(d_m.type, 'data')
        ncorr = sqrt(d_m.covb(i+1,i+1,options.order,:))./sqrt(d_m.covb(1,1,options.order,:));
        hist(real(squeeze(ncorr)),20,'edgecolor','black','facecolor','none','linewidth',1);
        h = findobj(gca,'Type','patch');
        set(h,'FaceColor','w')
        hold on
        ylim = get(gca,'ylim');
        stem(sqrt(d_m.cov(i+1,i+1,options.order))/sqrt(d_m.cov(1,1,options.order)),ylim(2),'color','blue');
        if flag
            pom = zeros(n,(length(options.cmp)+1)); % added
            for ii=1:length(options.cmp)
                stem(sqrt(options.cmp(ii).cov(i+1,i+1,options.order))/sqrt(options.cmp(ii).cov(1,1,options.order)),ylim(2),'color',colors{ii},'Marker',markers{ii});
                pom(i,ii) = sqrt(options.cmp(ii).cov(i+1,i+1,options.order))/sqrt(options.cmp(ii).cov(1,1,options.order)); % added
            end;
        pom(i,end) = sqrt(d_m.cov(i+1,i+1,options.order))/sqrt(d_m.cov(1,1,options.order)); % added
        end
        if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
        set(gca,'xlim',[0, round(max(pom(i,:)))+1],'ylimmode','manual');  %modified for setting xlim for each graph according the maximum value
        title(sprintf('%s rel.to %s', strrep(d_m.listvar{i+1},'_','_'), strrep(d_m.listvar{1},'_','_')))        
    else
        ylim(2) = 1;
        if flag
            stem(sqrt(d_m.cov(i+1,i+1,options.order))/sqrt(d_m.cov(1,1,options.order)),ylim(2),'color','blue');
            hold on
            for ii=1:length(options.cmp)
                stem(sqrt(options.cmp(ii).cov(i+1,i+1,options.order))/sqrt(options.cmp(ii).cov(1,1,options.order)),ylim(2),'color',colors{ii},'Marker',markers{ii});
            end;
            
            
            if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
            title(sprintf('%s rel.to %s', strrep(d_m.listvar{i+1},'_','_'), strrep(d_m.listvar{1},'_','_')))
        else
            stem(sqrt(d_m.cov(i+1,i+1,1))/sqrt(d_m.cov(1,1,1)),ylim(2),'color','blue');
            title(sprintf('%s rel.to %s', strrep(d_m.listvar{i+1},'_','_'), strrep(d_m.listvar{1},'_','_')))
        end
    end
end

x.figure('Relative std',f);
x.pagebreak();

%% Autocorrelations
ngraph = 0;

f=presetfigure('Visible','off');

for i = 1 : n
    ngraph = ngraph + 1;
    if ngraph>options.pggraph^2
        x.figure('Autocorrelations',f);
        x.pagebreak();
        f=presetfigure('Visible','off');
        ngraph = 1;
    end
    subplot(options.pggraph,options.pggraph,ngraph);
    if strcmp(d_m.type, 'data')
        hist(squeeze(d_m.corrb(i,i,options.order,:)),20,'facecolor','w');
        h = findobj(gca,'Type','patch');
        set(h,'FaceColor','w')
        hold on
        ylim = get(gca,'ylim');
        stem(d_m.corr(i,i,options.order),ylim(2),'color','blue');
        if flag
            for ii=1:length(options.cmp)
                stem(options.cmp(ii).corr(i,i,options.order),ylim(2),'color',colors{ii},'Marker',markers{ii});
            end;
        end
        set(gca,'xlim',[-1,1],'ylimmode','manual');
        title(strrep(d_m.listvar{i},'_','_'))
        if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
    else
        ylim(2) = 1;
        if flag
            stem(d_m.corr(i,i,options.order),ylim(2),'color','blue');
            hold on
            for ii=1:length(options.cmp)
                stem(options.cmp(ii).corr(i,i,options.order),ylim(2),'color',colors{ii},'Marker',markers{ii});
            end;
            set(gca,'xlim',[-1,1],'ylimmode','manual');
            title(strrep(d_m.listvar{i},'_','_'))
            if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
        else
            stem(d_m.corr(i,i,options.order),ylim(2),'color','blue');
            title(strrep(d_m.listvar{i},'_','_'))
        end
    end
end

x.figure('Autocorrelations',f);
x.pagebreak();

%% Cross-correlations

ngraph = 0;

f=presetfigure('Visible','off');

for i = 1 : n-1
    for j = i+1:n
        ngraph = ngraph + 1;
        if ngraph>options.pggraph^2
            x.figure('Cross-correlations',f);
            x.pagebreak();
            f=presetfigure('Visible','off');
            ngraph = 1;
        end
        subplot(options.pggraph,options.pggraph,ngraph);
        if strcmp(d_m.type, 'data')
            hist(squeeze(d_m.corrb(i,j,options.order,:)),20,'edgecolor','black','facecolor','none','linewidth',1);
            h = findobj(gca,'Type','patch');
            set(h,'FaceColor','w')
            hold on
            ylim = get(gca,'ylim');
            stem(d_m.corr(i,j,options.order),ylim(2),'color','blue');
            
            if flag
                for ii=1:length(options.cmp)
                    stem(options.cmp(ii).corr(i,j,options.order),ylim(2),'color',colors{ii},'Marker',markers{ii});
                end;
            end
            
            set(gca,'xlim',[-1,1],'ylimmode','manual');
            title(sprintf('%s X %s', strrep(d_m.listvar{i},'_','_'), strrep(d_m.listvar{j},'_','_')));
            if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
        else
            ylim(2) = 1;
            if flag
                stem(d_m.corr(i,j,options.order),ylim(2),'color','blue');
                hold on
                for ii=1:length(options.cmp)
                    stem(options.cmp(ii).corr(i,j,options.order),ylim(2),'color',colors{ii},'Marker',markers{ii});
                end;
                set(gca,'xlim',[-1,1],'ylimmode','manual');
                title(sprintf('%s X %s', strrep(d_m.listvar{i},'_','_'), strrep(d_m.listvar{j},'_','_')));
                if mod(ngraph,options.pggraph^2)==1, legend(legend_str,'Location','SouthEast'); end;
            else
                stem(d_m.corr(i,j,options.order),ylim(2),'color','blue');
                title(sprintf('%s X %s', strrep(d_m.listvar{i},'_','_'), strrep(d_m.listvar{j},'_','_')));
            end
        end
    end
end

x.figure('Cross-correlations',f);
x.pagebreak();
x.publish([options.fname '.' options.ext],'maketitle',false,'textscale',0.8);