function dout = get_endogenized(plan,varargin)

opts = cell2struct(varargin(2:2:end),varargin(1:2:end),2);

if ~isfield(opts,'flag')
    opts.flag='real';
end

switch opts.flag
    case 'real'
        dout=struct();
        nper = plan.enddate-plan.startdate+1;
        for i=1:nper
            nlist=plan.nlist(plan.nanchorsreal(:,i)) ;
            for j=1:length(nlist)
                if ~isfield(dout,nlist(j))
                    dout.(nlist{j})=tseries();
                end
                dout.(nlist{j})(plan.startdate+i-1)=1;
            end
        end
    case 'imag'
        dout=struct();
        nper = plan.enddate-plan.startdate+1;
        for i=1:nper
            nlist=plan.nlist(plan.nanchorsimag(:,i)) ;
            for j=1:length(nlist)
                if ~isfield(dout,nlist(j))
                    dout.(nlist{j})=tseries();
                end
                dout.(nlist{j})(plan.startdate+i-1)=1;
            end
        end
    otherwise
        disp('erroooooorr');
end