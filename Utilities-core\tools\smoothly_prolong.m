%
% res = smoothly_prolong(weight, persist, ts, ts_fix)
%
% This prolongs a given timeseries 'ts' to smoothly fit the numbers in
% ts_fix. The fit of both ts and ts_fix is with the given weight
% (relative to one). The persist parameter gives a persitence of changes
% in the resulting timeseries
%

function res = smoothly_prolong(weight, persist, ts, ts_fix)
  
  q1 = get(ts, 'start');
  qN = get(ts, 'end');
  qT = get(ts_fix, 'end');
  N = qN-q1+1;
  T = qT-q1+1;
  % build system y=Xb+eps
  % build first part
  y1 = zeros(T-1,1);
  X1 = -eye(T-1);
  X1(2:T:(T-1)*(T-1)) = persist;
  X1 = [(1-persist)*ones(T-1,1) X1];
  % build second part
  tmp=diff(ts);
  y2 = tmp((q1+1):qN);
  X2 = [zeros(N-1,1) eye(N-1) zeros(N-1,T-N)];
  % build third part
  y3 = [];
  X3 = [];
  for q = qN+1:qT
    if (~isnan(ts_fix(q)))
      y3=[y3; ts_fix(q)-ts(q1)];
      x3=[0 ones(1,q-q1) zeros(1,qT-q)];
      X3=[X3;x3];
    end
  end

  % multiply with weight and build the system
  y=[y1;weight*y2;weight*y3];
  X=[X1;weight*X2;weight*X3];
  % solve
  b = X\y;
  % recover tseries
  rr = [ts(q1) ts(q1)+cumsum(b(2:end))'];
  res = tseries(q1:qT, rr);
  
  
