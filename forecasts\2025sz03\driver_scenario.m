%**************************************************************************
%% DRIVER_SETTINGS
%**************************************************************************
close all
clear all
disp([sprintf('\n'), 'DRIVER_SETTINGS']);

%% ACTUAL FORECAST label
% prefix 
settings.report_prefix	= '2025sz03_fbs_sc_supply';
% optional prefix extension(s)
settings.prefix_ext = { ...	
%	'NAME'				'PREFIX EXTENSION'
					};
                
%% OLD FORECAST label (optional)
% prefix 
settings.oldfrc_prefix	= '2025sz03_fbs';%2022sz05

%% Databases 
% Baseline data
settings.histcore_name	=  'histcore-2025-05-16_switch.csv';%'histcore-2024-12-11_test.csv'; %'histcore-2025-02-10.csv';%
% Adhoc data
settings.adhoc_data_fb	= '';

%% Dates
settings.shist			= qq(1996,1);		% start of history in the data set
settings.ehist			= qq(2025,1);		% end of history in the histcore database for filtering
settings.end_pred		= qq(2026,4);		% end of prediction range 
settings.end_pred_long  = qq(2031,4);		% end of prediction range 
settings.end_comp		= qq(2050,4);		% end of computations of prediction
settings.start_pred		= settings.ehist+1;

%% Model Toggles
settings.pouzij_odhad	= 0;				% on/off - estimate parameters
%settings.RERlevel		= 1;				% RER version: 1 - ER level, 0 - change in ER
settings.FB_gdp_decomp  = 0;                % 1 - foreign GDP decomposed endogenously, 0 - foreign GDP decomposition as observations
settings.FB_prem_decomp =  0;
%% Forecast expectation scheme
settings.expectations_scheme = 'expscheme_fb';

%% Pre-computed ranges
settings.hrng			= settings.shist:settings.ehist;                % filtering
settings.fcastrng		= settings.start_pred:settings.end_pred;		% forecast
settings.comprng		= settings.start_pred:settings.end_comp;		% computation of prediction

settings.nstarrng     = settings.start_pred:settings.end_pred_long;     % foreign demand
settings.istarrng     = settings.start_pred:settings.end_pred_long;     % foreign interest rate
settings.pstarrng	  = settings.start_pred:settings.end_pred_long;     % foreign inflation rate
settings.potherrng	  = settings.start_pred:settings.end_pred_long;

settings.usdeurrng    = settings.start_pred:settings.end_pred_long;     % USDEUR 
settings.brentrng     = settings.start_pred:settings.end_pred_long;     % Brent USD price

%% Optional prefix extensions
if isfield(settings,'prefix_ext')
    for ix = 1: size(settings.prefix_ext,1);
        settings.extend.(settings.prefix_ext{ix,1}) = [settings.report_prefix settings.prefix_ext{ix,2}];
        disp(['Prefix ' settings.prefix_ext{ix,1} ' for ' [settings.report_prefix settings.prefix_ext{ix,2}] ' was created.']);
    end
    settings = rmfield(settings,'prefix_ext');
end

%% Path
settings.path = '..\database';				% location of folder database for input and output

%% Save Settings
FB_ID = settings;
save([settings.path '\Output-data\' settings.report_prefix '-settings.mat'], 'FB_ID');
FB_ID = settings.report_prefix;
disp(['FB_ID: ', settings.report_prefix]);
save('GLOBALSETTINGS.mat', 'FB_ID');

%**************************************************************************
%% DRIVER_OUTPUTGAP
%    - This driver decomposes foreign GDP into gap and trend components
%    - The resulting decomposition is saved in new histcore ..\database\Output-data\histcore_name_adj
%    - Decomposition:
%       - Prolonged fcast range is used for history, i.e., the filtering step is applied
%       - Purely endogenous if no ad-hoc expert tunes imposed
%       - Expert tunes imposed via get_tunes_FB (tune_dot_y_star_trend_fund, tune_dot_y_star_trend_shift, tune_y_star_gap)
% 
% @ last revision: JZ CNB, July 2023
%************************************************************************** 

close all
clear all
disp([sprintf('\n'), 'DRIVER_SCENARIO']);

%**************************************************************************
%% Set options
do_filter_book		= 0; 
do_filter_detail	= 0;
do_filter_compare   = 0;
do_filter_pt        = 0; 

% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('GLOBALSETTINGS.mat');

disp(['FB_ID: ', FB_ID]);

% load settings
settings = load_settings(FB_ID);

% modify settings
settings.hrng = settings.shist:settings.end_pred_long;
settings.FB_prem_decomp = 1;

%**************************************************************************
%% Read model and parameters
SS.filtering     = true;
SS               = setparam_FB(settings, 'filtering', SS.filtering);
SS.FB_gdp_decomp = settings.FB_gdp_decomp;
m = readmodel('../../Utilities-core/model/inc_g3_FB.model',SS);

%**************************************************************************
%% Load data
% h histcore database
h = dbload(settings.histcore_name, 'leadingRow', 'date', 'freq=', 4,...
 'delimiter=', ',');

% enrich histcore database by some ad-hoc data sources
h = changedata_FB(h,settings,SS);

% dbm model database
dbm = histdata_FB(h, settings);

% remove forecast from database
d = dbclip(dbm,settings.hrng);                                  

% add tunes
dtunes = get_tunes_FB(SS);

%**************************************************************************
%% Run the filtering step
dbfilter = filterhistory(m, d, dtunes, SS,settings.hrng);
f = dbfilter.mean;
% add out of model core variables
f = make_outofcore_FB(f, SS, settings);
% make transformations
f_model = make_transformations_FB(f, SS, false);
% add reporting layer of data
ff = make_transformations_present_FB(f_model, h, settings, SS);

%**************************************************************************
%% Report
if do_filter_book
    book_filter_FB(ff, m, settings, ...
        ['Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-book-scenario-fb']);
end

if do_filter_detail
    book_filter_detail_FB(ff, m, settings, ...
        ['Detailed Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-detail-scenario-fb']);
end

if do_filter_compare
    ff_old  = dbload([settings.outdata_dir '\' settings.oldfrc_prefix '-scenario-fb.csv']);
    settings_old  = load([settings.outdata_dir '\' settings.oldfrc_prefix '-settings.mat']);
    book_filter_compare_FB(ff, ff_old, m, settings,settings_old, ...
        ['Compare Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-v-' settings.oldfrc_prefix '-filter-scenario-comp-fb']);
end
%**************************************************************************
%% Prepare histcore output databases and check trend/gap decomposition
h_adj = h;

list_add = {{'zz_prem_usdeur','prem_usdeur'}};

for jj = 1:length(list_add)
    
      h_adj.(list_add{jj}{2}) = ff.(list_add{jj}{1});
   
end

list_remove = {'equilibrium_i_star','weight_dot_pstar_energy_tilde','weight_pstar_energy_tilde','p_brentUSD_tilde','p_brent_tilde'};
h_adj = rmfield(h_adj,list_remove);


%**************************************************************************
%% Save 
% histcore with gdp decomposition
dbsave(h_adj,[settings.histcore_name_adj],Inf,'format','%.16e');
% filter db: only model variables
save([settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.mat'],'-struct','f');
% output database: model and reporting level of variables, csv
dbsave(ff,[settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.csv'],Inf,'format','%.16e');

%%

%**************************************************************************
%% DRIVER_FILTER
%**************************************************************************

close all
clear all
disp([sprintf('\n'), 'DRIVER_FILTER']);

%**************************************************************************
%% Set options

do_filter_book		= 0; 
do_filter_detail	= 0;
do_filter_compare   = 0;
do_filter_pt        = 0; 
% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('GLOBALSETTINGS.mat');

disp(['FB_ID: ', FB_ID]);

% load settings
settings = load_settings(FB_ID);
%**************************************************************************
%% Read model and parameters
SS.filtering    = true;
SS              = setparam_FB(settings, 'filtering', SS.filtering);
m = readmodel('../../Utilities-core/model/inc_g3_FB.model',SS);

%**************************************************************************
%% Load data
% h = dbload(settings.histcore_name, 'leadingRow', 'date', 'freq=', 4,...
% 'delimiter=', ','); % loading from non-IRIS data source
h = dbload(settings.histcore_name_adj);
h = changedata_FB(h,settings,SS);
f_outputgap = dbload([settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.csv']);
% dbo original database
% d pre-filter database
dbm = histdata_FB(h, settings);

% remove forecast from database
d = dbclip(dbm,settings.hrng);                                  

% add tunes
dtunes = get_tunes_FB(SS);
% dtunes = dbempty;
% dtunes.tune_dot_y_star_trend_shift = tseries();
% dtunes.tune_dot_y_star_trend_fund = tseries();
% dtunes.tune_y_star_gap = tseries();
% 
% dtunes.tune_dot_y_star_trend_fund(settings.shist:settings.ehist) = f_outputgap.dot_y_star_trend_fund;
% dtunes.tune_y_star_gap(settings.shist:settings.ehist) = f_outputgap.y_star_gap;
% dtunes.tune_dot_y_star_trend_shift(settings.shist:settings.ehist) = f_outputgap.tune_dot_y_star_trend_shift;
%**************************************************************************
%% Run the filtering step
dbfilter = filterhistory(m, d, dtunes, SS,settings.hrng);
f = dbfilter.mean;

% add out of model core variables
f = make_outofcore_FB(f, SS, settings);
% make transformations
f_model = make_transformations_FB(f, SS, false);
% add reporting layer of data
ff = make_transformations_present_FB(f_model, h, settings, SS);

%**************************************************************************
%% Report
if do_filter_book
    book_filter_FB(ff, m, settings, ...
        ['Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-book-fb']);
end

if do_filter_detail
    book_filter_detail_FB(ff, m, settings, ...
        ['Detailed Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-detail-fb']);
end

if do_filter_compare
    ff_old  = dbload([settings.outdata_dir '\' settings.oldfrc_prefix '-filter-fbs.csv']);
    settings_old  = load([settings.outdata_dir '\' settings.oldfrc_prefix '-settings.mat']);
    book_filter_compare_FB(ff, ff_old, m, settings,settings_old, ...
        ['Compare Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-v-' settings.oldfrc_prefix '-filter-comp-fb']);
end

if do_filter_pt
    book_filter_pt_FB(ff, m, settings, ...
        ['Detailed Filter Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-filter-detail-pt']);
end

%**************************************************************************
%% Save 
% model structure
save([settings.outdata_dir '\' settings.report_prefix '-kalman-fb.mat']','m');
% SS of filter model
save([settings.outdata_dir '\' settings.report_prefix '-filterSS-fb.mat'],'-struct', 'SS');
% db with filter tunes
save([settings.outdata_dir '\' settings.report_prefix '-tunes-fb.mat'],'-struct', 'dtunes');
% filter in database: database for filtering
dbsave(dbm,[settings.outdata_dir '\' settings.report_prefix '-pre-filter-fb.csv'],Inf,'format','%.16e');
% filter db: only model variables
save([settings.outdata_dir '\' settings.report_prefix '-filterdata-fb.mat'],'-struct','f');
% filter db: model and reporting level of variables, csv
dbsave(ff,[settings.outdata_dir '\' settings.report_prefix '-filter-fbs.csv'],Inf,'format','%.16e');

%%

%**************************************************************************
%% DRIVER_FCAST
%**************************************************************************

close all
clear all
disp([sprintf('\n'), 'DRIVER_FCAST']);

%% Set options

do_forecast_book     = 0;      % do forecast book options
                               % 0 - do not create forecast book
                               % 1 - do simple forecast book 
                               % 2 - do forecast book with filter comparison 
do_compare_book      = 0;

% load GLOBALSETTINGS (= variable ID with last report prefix) or set ID directly
load('GLOBALSETTINGS.mat');

disp(['FB_ID: ', FB_ID]);

% load settings
settings = load_settings(FB_ID);

%**************************************************************************
%% Read model and parameters

SS.filtering = false;

SS  = setparam_FB(settings, 'filtering', SS.filtering);
SS.FB_only      = false;
m = readmodel('../../Utilities-core/model/inc_g3_FB.model',SS);

% load expectations scheme
try
    expect_scheme = expDesign(m,settings.expectations_scheme);
catch
    msgbox('Expectation scheme not defined!!!','Expectations scheme error','error');
    disp('Expectation scheme not defined.');
end

%% Load database
h = dbload(settings.histcore_name_adj);
h = changedata_FB(h,SS);

f = load(settings.filterdata_fb_name);		% filter results
dbf = dbclip(f, settings.shist:settings.ehist);
dbm = fcastdata_FB(h, f, settings, SS);

if strcmp(settings.adhoc_data_fb,'')
    db_ejud = dbempty;
else
    % expert judgement
    db_ejud = dbload(settings.adhoc_data_fb);
    db_ejud = dbclip(db_ejud, settings.comprng);
	d_pom      = dbfun(@(x,y) comment(x,comment(y)),db_ejud,f);
	db_ejud = dbextend(db_ejud,d_pom);
end

% merge databases
d = dbextend(dbm, dbf);

% remove structural shocks over forecasting horizon
list = get(m,'eList');
for i = 1:length(list);
    d.(list{i})(settings.comprng) = zeros(size(settings.comprng));
end

d = dbextend(d, db_ejud);

replic = 0;


%% MAKE THE PLAN
% getting ready for foreign block inclusion
 
fcast_plan   = plan(m, settings.comprng);
    % fixing expected outlooks
    % fixes are primarily taken from histcore(through changedata)
    % if you want to change some fix, do it in adhocdata, otherwise IT WOULD NOT BE REPORTED!!!!!!
    
     fcast_plan  = exogenize( fcast_plan, 'dot_usdeur',                 settings.usdeurrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_USDEUR',              	settings.usdeurrng);       

     fcast_plan  = exogenize( fcast_plan, 'dot_pstar_other_tilde',      settings.pstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_pstar_other_tilde',      settings.pstarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'dot_pstar_energy_tilde',     settings.pstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_pstar_energy_tilde',    	settings.pstarrng);     
  
     fcast_plan  = exogenize( fcast_plan, 'dot_cpi_star_tilde',         settings.pstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_dot_cpi_star_tilde',    	settings.pstarrng);    
   
     fcast_plan  = exogenize( fcast_plan, 'y_star_gap',                 settings.nstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_y_star_gap',             settings.nstarrng);
    
     fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend_fund',     	settings.nstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_dot_y_star_trend_fund', 	settings.nstarrng);  
   
     fcast_plan  = exogenize( fcast_plan, 'dot_y_star_trend_shift',   	settings.nstarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_dot_y_star_trend_shift',	settings.nstarrng);  
    
 	 fcast_plan  = exogenize( fcast_plan, 'i_star_eq',              settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_i_star_eq',              settings.istarrng);       
   
     fcast_plan  = exogenize( fcast_plan, 'i_star_eu',                  settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_Istar',                  settings.istarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'i_star',                     settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_shadow_rate',            settings.istarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'i_star_us',                  settings.istarrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_i_star_us',                  settings.istarrng);
     
     fcast_plan  = exogenize( fcast_plan, 'prem_usdeur',                 settings.usdeurrng);
     fcast_plan  = endogenize(fcast_plan, 'eps_prem_usdeur',              	settings.usdeurrng);   
    
tic
[dbfcast,~,~,~,expectations] = simulate(m, d, settings.comprng, 'plan', fcast_plan, ...
	'expectations',expect_scheme);

disp('simulate time:');
toc
if isequal(exist('expectations','var'),1)
	disp(expectations);
end

%% Create databases
d = dbextend(d,dbfcast);
% add out of model core variables
d = make_outofcore_FB(d, SS, settings);
% make transformations
d_model = make_transformations_FB(d, SS, false);
% add reporting layer of data
dd     = make_transformations_present_FB(d_model, h, settings, SS);

%% Create reports

if do_forecast_book ==1
    db_fcast_book = book_fcast_FB(dd, m, settings, ...
        ['Forecast Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-forecast-book-fb']);
end

if do_forecast_book ==2
    ff  = dbload([settings.outdata_dir '\' settings.report_prefix '-outputgap-fb.csv']);
    db_fcast_book = book_fcast_filter_comp_FB(ff, dd, m, settings, ...
        ['Forecast Book - Foreign Block ' settings.report_prefix], ...
        [settings.outreport_dir '\' settings.report_prefix '-fcast_filter_comp-fb']);
end

if do_compare_book
	dd_old  = dbload([settings.outdata_dir '\' settings.oldfrc_prefix '-forecast-fb.csv']);
    settings_old  = load([settings.outdata_dir '\' settings.oldfrc_prefix '-settings.mat']);
    db_compare_book = book_fcast_compare_FB(dd, m, SS, dd_old, fcast_plan, settings, settings_old, ...
    ['Analytical Compare Report ' settings.report_prefix 'vs' settings.oldfrc_prefix], ...
    [settings.outreport_dir '\' settings.report_prefix '-' settings.oldfrc_prefix '-anal-comp']);
end

%% Data saving
% complementary databases
save([settings.outdata_dir '\' settings.report_prefix '-model-fb.mat'], 'm');
save([settings.outdata_dir '\' settings.report_prefix '-SS-fb.mat'],'-struct', 'SS');
save([settings.outdata_dir '\' settings.report_prefix '-plan-fb'], 'fcast_plan');
save([settings.outdata_dir '\' settings.report_prefix '-expectations-fb'], 'expect_scheme');
% database with pre-forecast data
dbsave(dbf, [settings.outdata_dir, '\', settings.report_prefix, '-pre-forecast-fb.csv'], Inf, 'format', '%.16e');
% forecast database
dbsave(dd, [settings.outdata_dir '\' settings.report_prefix '-forecast-fb.csv'],Inf,'format','%.16e');

if do_forecast_book
    dbsave(db_fcast_book, [settings.outdata_dir '\' settings.report_prefix ...
        '-forecast-book-fb.csv'],Inf,'format','%.16e');
end



