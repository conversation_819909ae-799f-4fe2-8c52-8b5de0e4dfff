function [out_DB] = getTseriesFromDPSZ(tserieslist, varargin)

%% FUNCTION NAME:
%   getTseriesFromDPSZ
%
% DESCRIPTION:
%   This function connects to DPSZ and extracts data in provided list and
%   it returs database object.
%
% INPUT:
%   tserieslist - (string) file with list of time series to extract from DPSZ
%   OPTIONAL
%   fileSlovnik_name - (string) dictionary file, empty string accepted
%   PrekladIn - (string) sheet name in dictionary file, empty string accepted
%   sdate - (string) starting year for database, empty string accepted,
%   first value used if not specified
%   edate - (string) end year for database, empty string accepted,
%   last value used if not specified
% OUTPUT:
%   out_DB - (IRIS DB) IRIS type database
%
% ASSUMPTIONS AND LIMITATIONS:
%   some of the strings may be empty
%
% EXAMPLE:
%     fileSlovnik_name = absolute_path('PrekladFcastBookNewDB.xlsx');
%     PrekladIn = 'PrekladIn';
%     csv_startyear       = 1996;
%     csv_endyear         = 2026;
%
%     dreport= struct();
%
%     % single query
%     tmp=getTseriesFromDPSZ('HTFKsc_yoy_yy_so_412NTF');
%     dreport=dbmerge(dreport,tmp);
%
%     % multiple query
%     tmp = getTseriesFromDPSZ({'ZamciVSPS_yoy_yy_nso_412ntf','MnezVSPS_lev_yy_nso_412ntf'});
%     dreport=dbmerge(dreport,tmp);
%
%     %% single query with version
%     tmp=getTseriesFromDPSZ('HTFKsc_yoy_yy_so_412NTF','forecastlabel','4');
%     dreport=dbmerge(dreport,tmp);
%     % single query with label
%     tmp=getTseriesFromDPSZ('HTFKsc_yoy_yy_so_412NTF','forecastlabel','2019sz01_database');
%     dreport=dbmerge(dreport,tmp);
%
%     % multiple query
%     tmp = getTseriesFromDPSZ({'ZamciVSPS_yoy_yy_nso_412ntf','MnezVSPS_lev_yy_nso_412ntf'});
%     dreport=dbmerge(dreport,tmp);
%
%     % single query with multiple labels and series
%     tmp=getTseriesFromDPSZ({'HTFKsc_yoy_yy_so_412NTF','ZamciVSPS_yoy_yy_nso_412ntf'},'forecastlabel',{'2019sz01_database','4',''});
%     dreport=dbmerge(dreport,tmp);
%
%     % single query with multiple labels and series
%     tmp=getTseriesFromDPSZ({'HTFKsc_yoy_yy_so_412NTF','ZamciVSPS_yoy_yy_nso_412ntf'},'forecastlabel',{'2019sz01_database','4',''});
%     dreport=dbmerge(dreport,tmp);
%
%     % test s prekladom
%     tmp=getTseriesFromDPSZ({'HTFKsc_yoy_yy_so_412NTF','SDsc_lev_qq_so_412NTF'},'forecastlabel',{'4',''},'fileSlovnik_name',absolute_path('PrekladFcastBookNewDB.xlsx'),'PrekladIn','PrekladIn');
%     dreport=dbmerge(dreport,tmp);
% REVISION HISTORY:
%   $Revision: R2013b$
%   $Author: F. Brazdik$
%   $Date: August 24, 2020$
%       * Initial implementation
%       * Single series clipping


% This function connects to DPSZ and takes tseries and return database
% needs :
% string list of times series to extract
% Optional
% string fileSlovnik_name - dictionary file
% string PrekladIn - sheet name in dictionary file
% string sdate - starting year
% string edate - ending year
% string forecastlabel - forecast label - empty,string,version number

% Apr 2019 Frantisek Brazdik

optParse = inputParser; % Create an instance of the class.
optParse.addParamValue('database', '', @ischar);
optParse.addParamValue('sheetCSV', '', @ischar);
optParse.addParamValue('fileSlovnik_name', '', @ischar);
optParse.addParamValue('PrekladIn', '', @ischar);
optParse.addParamValue('sdate', [], @isnumeric);
optParse.addParamValue('edate', [], @isnumeric);
optParse.addParamValue('forecastlabel', '', @(x) assert(iscell(x) || ischar(x), 'char or cell needed as input'));
optParse.addParamValue('extension', 1, @isnumeric);
optParse.parse(varargin{:});

options = optParse.Results;

% check and open translator, if empty name, no translation
if ~isempty(options.fileSlovnik_name)
    if exist(options.fileSlovnik_name, 'file') > 0
        [~, TranslateTable, ~] = xlsread(options.fileSlovnik_name, options.PrekladIn);
    else
        error('MyFunction:fileNotFound', 'Translator file not found');
    end
else
    %no translation
    TranslateTable = [];
end

% convert to cell for constat referencing
if ~iscell(tserieslist)
    tserieslist = {tserieslist};
end

%parse label for version
data_version = str2double(options.forecastlabel);
% convert to cell for constat referencing
if ~iscell(options.forecastlabel)
    options.forecastlabel = {options.forecastlabel};
end


%test for number of extensions
if (length(options.forecastlabel) > 1) && (options.extension == false)
    options.extension = true;
    warning('No Extension allowed only for singe value of forecast label. Ignoring extension false option');
end

out_DB = struct();

%% DPSZ extract

%Open DB connection
DB = adodb_connect('Provider=OraOLEDB.Oracle;Data Source=DWSSP;OSAuthent=1;');

for ii = 1:length(tserieslist)

    if isempty(options.forecastlabel)
        % empty forecast label means the actual version and no name
        % manipulation
        sql_string = sprintf(['select EXM_DPSZ_LV_V.VERZE, EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.RADA, EXM_DPSZ_LV_V.TYP, EXM_DPSZ_LV_V.HODNOTA, EXM_DPSZ_LV_V.KOMENTAR ', ...
            'from EXM.EXM_DPSZ_LV_V ', ...
            'where (EXM_DPSZ_LV_V.VERZE =', ...
            '(select max(EXM_DPSZ_LV_V.VERZE) from  EXM.EXM_DPSZ_LV_V where EXM_DPSZ_LV_V.KOD = ''%s'')', ...
            'and EXM_DPSZ_LV_V.KOD = ''%s'') ', ...
            'ORDER BY EXM_DPSZ_LV_V.RADA ASC'], tserieslist{ii}, tserieslist{ii});
        out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, '', ''));

    else
        iii = 1;
        if isempty(options.forecastlabel{iii})
            % get the last possible version
            sql_string = sprintf(['select EXM_DPSZ_LV_V.VERZE, EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.RADA, EXM_DPSZ_LV_V.TYP, EXM_DPSZ_LV_V.HODNOTA, EXM_DPSZ_LV_V.KOMENTAR ', ...
                'from EXM.EXM_DPSZ_LV_V ', ...
                'where (EXM_DPSZ_LV_V.VERZE =', ...
                '(select max(EXM_DPSZ_LV_V.VERZE) from  EXM.EXM_DPSZ_LV_V where EXM_DPSZ_LV_V.KOD = ''%s'')', ...
                'and EXM_DPSZ_LV_V.KOD = ''%s'') ', ...
                'ORDER BY EXM_DPSZ_LV_V.RADA ASC'], tserieslist{ii}, tserieslist{ii});
            out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, '', ''));
        elseif ~isnan(data_version(iii))
            sql_string = sprintf(['select EXM_DPSZ_LV_V.VERZE, EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.RADA, EXM_DPSZ_LV_V.TYP, EXM_DPSZ_LV_V.HODNOTA, EXM_DPSZ_LV_V.KOMENTAR ', ...
                'from EXM.EXM_DPSZ_LV_V ', ...
                'where (EXM_DPSZ_LV_V.VERZE =%d ', ...
                'and EXM_DPSZ_LV_V.KOD = ''%s'') ', ...
                'ORDER BY EXM_DPSZ_LV_V.RADA ASC'], data_version(iii), tserieslist{ii});
            if options.extension
                out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}), '');
            else
                out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, '', 'extension', 0));
            end

        else
            sql_string = sprintf(['select EXM_DPSZ_SS_V.SNIMEK, EXM_DPSZ_SS_V.KOD, EXM_DPSZ_SS_V.RADA, EXM_DPSZ_SS_V.TYP, EXM_DPSZ_SS_V.HODNOTA, EXM_DPSZ_SS_V.KOMENTAR ', ...
                'from EXM.EXM_DPSZ_SS_V ', ...
                'where (EXM_DPSZ_SS_V.SNIMEK =''%s'' ', ...
                'and EXM_DPSZ_SS_V.KOD = ''%s'') ', ...
                'ORDER BY EXM_DPSZ_SS_V.RADA ASC'], options.forecastlabel{iii}, tserieslist{ii});
            if options.extension
                out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, ''));
            else
                out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, '', 'extension', 0));
            end

        end
        for iii = 2:length(options.forecastlabel)
            if isempty(options.forecastlabel{iii})
                % get the last possible version
                sql_string = sprintf(['select EXM_DPSZ_LV_V.VERZE, EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.RADA, EXM_DPSZ_LV_V.TYP, EXM_DPSZ_LV_V.HODNOTA, EXM_DPSZ_LV_V.KOMENTAR ', ...
                    'from EXM.EXM_DPSZ_LV_V ', ...
                    'where (EXM_DPSZ_LV_V.VERZE =', ...
                    '(select max(EXM_DPSZ_LV_V.VERZE) from  EXM.EXM_DPSZ_LV_V where EXM_DPSZ_LV_V.KOD = ''%s'')', ...
                    'and EXM_DPSZ_LV_V.KOD = ''%s'') ', ...
                    'ORDER BY EXM_DPSZ_LV_V.RADA ASC'], tserieslist{ii}, tserieslist{ii});
                if options.extension
                    out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, ''));
                else
                    out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, '', 'extension', 0));
                end
            elseif ~isnan(data_version(iii))
                sql_string = sprintf(['select EXM_DPSZ_LV_V.VERZE, EXM_DPSZ_LV_V.KOD, EXM_DPSZ_LV_V.RADA, EXM_DPSZ_LV_V.TYP, EXM_DPSZ_LV_V.HODNOTA, EXM_DPSZ_LV_V.KOMENTAR ', ...
                    'from EXM.EXM_DPSZ_LV_V ', ...
                    'where (EXM_DPSZ_LV_V.VERZE =%d ', ...
                    'and EXM_DPSZ_LV_V.KOD = ''%s'') ', ...
                    'ORDER BY EXM_DPSZ_LV_V.RADA ASC'], data_version(iii), tserieslist{ii});
                if options.extension
                    out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, ''));
                else
                    out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, '', 'extension', 0));
                end
            else
                sql_string = sprintf(['select EXM_DPSZ_SS_V.SNIMEK, EXM_DPSZ_SS_V.KOD, EXM_DPSZ_SS_V.RADA, EXM_DPSZ_SS_V.TYP, EXM_DPSZ_SS_V.HODNOTA, EXM_DPSZ_SS_V.KOMENTAR ', ...
                    'from EXM.EXM_DPSZ_SS_V ', ...
                    'where (EXM_DPSZ_SS_V.SNIMEK =''%s'' ', ...
                    'and EXM_DPSZ_SS_V.KOD = ''%s'') ', ...
                    'ORDER BY EXM_DPSZ_SS_V.RADA ASC'], options.forecastlabel{iii}, tserieslist{ii});
                if options.extension
                    out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, ''));
                else
                    out_DB = dbmerge(out_DB, querydb(tserieslist{ii}, DB, sql_string, TranslateTable, options.forecastlabel{iii}, '', 'extension', 0));
                end
            end

        end

    end
end

%%Close DB connection
DB.release;

%% Extra clipping
% !!! mixed frequency in database destroys data labels
ts_list = dbnames(out_DB, 'Classfilter', 'tseries');
if ~isempty(options.sdate) || ~isempty(options.edate)
    for ii = 1:length(ts_list)
        % empty fills with last/first available point
        if isempty(options.edate)
            options.edate = get(out_DB.(ts_list{ii}), 'last');
        end
        if isempty(options.sdate)
            options.sdate = get(out_DB.(ts_list{ii}), 'first');
        end

        switch get(out_DB.(ts_list{ii}), 'freq')
            case 1 % yearly
                dbclip_range = yy(options.sdate):yy(options.edate);
                out_DB.(ts_list{ii}) = resize(out_DB.(ts_list{ii}), dbclip_range);
            case 4 % quarterly
                dbclip_range = qq(options.sdate, 1):qq(options.edate, 4);
                out_DB.(ts_list{ii}) = resize(out_DB.(ts_list{ii}), dbclip_range);
            case 12 % monthly
                dbclip_range = mm(options.sdate, 1):mm(options.edate, 12);
                out_DB.(ts_list{ii}) = resize(out_DB.(ts_list{ii}), dbclip_range);
            case 0 % daily
                dbclip_range = dd(options.sdate, 1, 1):dd(options.edate, 12, 31);
                out_DB.(ts_list{ii}) = resize(out_DB.(ts_list{ii}), dbclip_range);
            otherwise
                error('MyFunction:Wrong date', 'Unknown frequency for clipping in database');
        end
    end
end

end

%% Main function end

%% Support function for single query to ts
function [dbts] = querydb(tseriesname, DB, sql_string, TranslateTable, nameext, filename, varargin)

default = { ...
    'flag', '', ...
    'extension', 1; ... %
    };

%--parse options using IRIS --%
options = passopt(default, varargin{:});

dbts = struct();
%disp(tseriesname); %debug
%get from database and make single tseries database
[~, Table] = adodb_query(DB, sql_string);
% handle only non empty results
if ~isempty(Table)
    %tseriesname
    %get frequency
    switch Table{end, 4}
        case 'Y'
            range_start = yy(str2double(Table{1, 3}(1:4)));
            range_end = yy(str2double(Table{end, 3}(1:4)));
        case 'Q'
            range_start = qq(str2double(Table{1, 3}(1:4)), str2double(Table{1, 3}(end)));
            range_end = qq(str2double(Table{end, 3}(1:4)), str2double(Table{end, 3}(end)));
        case 'M'
            range_start = mm(str2double(Table{1, 3}(1:4)), str2double(Table{1, 3}(6:7)));
            range_end = mm(str2double(Table{end, 3}(1:4)), str2double(Table{end, 3}(6:7)));
        case 'D' % not continuos data comm sheets
            range_start = dd(str2double(Table{1, 3}(1:4)), str2double(Table{1, 3}(6:7)), str2double(Table{1, 3}(8:9)));
            range_end = dd(str2double(Table{end, 3}(1:4)), str2double(Table{end, 3}(6:7)), str2double(Table{end, 3}(8:9)));
        otherwise
            error('MyFunction:Wrong date', 'Unknown frequency in database');
    end
    % Rename and comment
    if ~isempty(TranslateTable)
        ind_rename = find(strcmp(TranslateTable(:, 1), tseriesname));
        % if optional translations are present, destination filename is
        % used to decide for translation
        if length(ind_rename) > 1
            multi_translate_index = find(strcmp(TranslateTable(ind_rename, 5), filename));
            if isempty(multi_translate_index) % empty file destination for all other translations
                multi_translate_index = find(strcmp(TranslateTable(ind_rename, 5), ''));
            end
            ind_rename = ind_rename(multi_translate_index);
        end
        if isempty(ind_rename)
            warning([tseriesname, ' not in dictionary!']);
            if isempty(nameext)
                % actual version for mising translate
                if Table{end, 4} == 'D'
                    dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, Table(1, 6));
                else
                    dbts.(genvarname(char(tseriesname))) = tseries(range_start:range_end, str2double(Table(:, 5)), Table(1, 6));
                end
            else
                % given version renaming mising translate rename
                if Table{end, 4} == 'D'
                    if options.extension
                        dbts.([genvarname(char(tseriesname)), '_', nameext]) = tseries_daily(Table, Table(1, 6));
                    else
                        dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, Table(1, 6));
                    end
                else
                    if options.extension
                        dbts.([genvarname(char(tseriesname)), '_', nameext]) = tseries(range_start:range_end, str2double(Table(:, 5)), Table(1, 6));
                    else
                        dbts.(genvarname(char(tseriesname))) = tseries(range_start:range_end, str2double(Table(:, 5)), Table(1, 6));
                    end
                end
            end
        else
            % translated with extension
            if isempty(nameext)
                % actual version
                if Table{end, 4} == 'D'
                    dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, TranslateTable(ind_rename, 7));
                else
                    dbts.(char(TranslateTable(ind_rename, 4))) = tseries(range_start:range_end, str2double(Table(:, 5)), TranslateTable(ind_rename, 7));
                end
            else
                % given version renaming
                if Table{end, 4} == 'D'
                    if options.extension
                        dbts.([genvarname(char(tseriesname)), '_', nameext]) = tseries_daily(Table, TranslateTable(ind_rename, 7));
                    else
                        dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, TranslateTable(ind_rename, 7));
                    end
                else
                    if options.extension
                        dbts.([char(TranslateTable(ind_rename, 4)), '_', nameext]) = tseries(range_start:range_end, str2double(Table(:, 5)), TranslateTable(ind_rename, 7));
                    else
                        dbts.(char(TranslateTable(ind_rename, 4))) = tseries(range_start:range_end, str2double(Table(:, 5)), TranslateTable(ind_rename, 7));
                    end
                end
            end
        end
    else %  no translation table
        if strcmp(options.flag, 'INT') % jednotka is extracted for flag INT
            if isempty(nameext)
                % actual version renaming
                if Table{end, 4} == 'D'
                    dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, Table(1, 6));
                else
                    dbts.(genvarname(char(tseriesname))) = tseries(range_start:range_end, str2double(Table(:, 5)), [Table{1, 6}, '|', Table{1, 7}]);
                end
            else
                % given version renaming
                if Table{end, 4} == 'D'
                    if options.extension
                        dbts.([genvarname(char(tseriesname)), '_', nameext]) = tseries_daily(Table, Table(1, 6));
                    else
                        dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, Table(1, 6));
                    end
                else
                    if options.extension
                        dbts.([genvarname(char(tseriesname)), '_', nameext]) = tseries(range_start:range_end, str2double(Table(:, 5)), [Table{1, 6}, '|', Table{1, 7}]);
                    else
                        dbts.(genvarname(char(tseriesname))) = tseries(range_start:range_end, str2double(Table(:, 5)), [Table{1, 6}, '|', Table{1, 7}]);
                    end
                end
            end
        else
            if isempty(nameext)
                % actual version renaming
                if Table{end, 4} == 'D'
                    dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, Table(1, 6));
                else
                    dbts.(genvarname(char(tseriesname))) = tseries(range_start:range_end, str2double(Table(:, 5)), Table(1, 6));
                end
            else
                % given version renaming
                if Table{end, 4} == 'D'
                    if options.extension
                        dbts.([genvarname(char(tseriesname)), '_', nameext]) = tseries_daily(Table, Table(1, 6));
                    else
                        dbts.(genvarname(char(tseriesname))) = tseries_daily(Table, Table(1, 6));
                    end
                else
                    if options.extension
                        dbts.([genvarname(char(tseriesname)), '_', nameext]) = tseries(range_start:range_end, str2double(Table(:, 5)), Table(1, 6));
                    else
                        dbts.(genvarname(char(tseriesname))) = tseries(range_start:range_end, str2double(Table(:, 5)), Table(1, 6));
                    end
                end
            end
        end
    end
else
    warning('QueryDB:TSnotfound', 'Tseries not found: %s', tseriesname);
end

end

%% Support function for daily data initialization
function [daily_ts] = tseries_daily(Table, ts_comment)
% create ts from noncontinuous daily data
daily_ts = tseries;
for ii = 1:length(Table)
    daily_ts(dd(str2double(Table{ii, 3}(1:4)), str2double(Table{ii, 3}(6:7)), str2double(Table{ii, 3}(8:9)))) = str2double(Table(ii, 5));
    daily_ts = comment(daily_ts, ts_comment);
end
end