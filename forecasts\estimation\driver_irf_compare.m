%% Estimated model analysis
% Driver to create irf
% Requires names of variables to be consistent through all model!!!

clear all; close all;
disp([sprintf('\n'), 'DRIVER_IRF Original vs estimated model']);


load('GLOBALSETTINGS.mat');
disp(['ID: ', ZB_ID]);

% load settings
settings = load_settings(ZB_ID);

settings.doGraphs			= 0;  % Draw Matlab Graphs - yes/no
settings.doPPT			= 0;  % Export Graphs to PPT - yes/no (doGraphs required)
settings.doReport			= 1;  % Create report file - yes/no (set format in settingsions)
settings.doSaving			= 0;  % Export results to CSV - yes/no

settings.responseLength	= 32;			% Length of responses (# of quarters)
settings.hitPeriod		= [4];%[1:8 12 16 20];		% Shock(s) hit in this periods (matrix), e.g. [1:4 7]
settings.varTransform		= 1;			% Transformations of variables (zz_) on/off - TODO: DOKONCIT!!!
settings.varnamedisp		= 1;			% 1 - display variable names, 0 - display comments

shockType				= 'b';			% Expected/unexpected/both 'e'/'u'/'b' (must be string!)

settings.formatSubplot	= [3,3];		% e.g. [2,3] - 2 rows, 3 columns;
settings.lgndInSubplot	= 0;			% adds legend in this subplot

settings.one_graph_compare = 1;

% names of shocks or 'all' for all shocks
shockList		= {...        
                    'eps_Istar'
                    'eps_y_star_gap'
                    'eps_pstar_other_tilde'
                    'eps_y_star_trend'
                    'eps_USDEUR'
                    'eps_pstar_tilde'
                    'eps_p_ener_ex_oil_tilde'
                    'eps_pstar_RP_tilde'
                    'eps_dot_cpi_star_tilde'
                    'eps_i_star_eq'				...
                    };
        
% names of responses or 'all' for all responses
responseList	= {...
 				'i_star'...
                'y_star_gap'...
                'dot_cpi_star_tilde'...
                'dot_p_ener_ex_oil_tilde'...
                'dot_pstar_other_tilde'...
                'r_star_gap'...
                'z_gap'...
                'dot_usdeur'...
                'dot_pstar_tilde'...
				};
           
%% Set models

% Models already prepared in solved model structure format (including
% path!), and model nickname
inputList = {...
% 	MODEL PREFIX						NICKNAME
%	ID									legend
 	'2022sz05'                          'orig'
    '2022sz05-ZBestim'                  'estim'
	};

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Run impulse responses
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

[irf,m] = impulse_responses(inputList,shockList,responseList,shockType,settings);
                    
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
