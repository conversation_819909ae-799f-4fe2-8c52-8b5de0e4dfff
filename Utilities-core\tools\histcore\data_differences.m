function [out_diff] = data_differences(dbasenew,dbaseold, end_hist, range_for_comp, in_digit,histdir, heading)
    % data_differences functions shows the differences in the variables used
    % for the model g3 in computation of the observable variables by histdata
    % and fcastdata functions.
    %
    % Use this function to quickly identify the changes in data that may affect
    % the filtering and forecasting
    %
    % Last revision: FB, Oct 2019
    
    list_diff = {'usdeur', 'net4_euribor3m', 'net4_euribor1y', 'net4_eonia3m','net4_shadoweuribor3m',...
        'net4_eonia1y', 'brent', 'ppi_emu', 'ppi_emu_energy', 'ppi_emu_other', 'cpi_emu', 'gdp_emu'};
    
    hn = dbbatch(dbasenew, '$0', 'resize(dbasenew.$0, range_for_comp);', 'namelist', list_diff);
    ho = dbbatch(dbaseold, '$0', 'resize(dbaseold.$0, range_for_comp);', 'namelist', list_diff);
    
    %% Compute differences
    diff    = dbfun(@(x,y) [x-y],hn,ho);
    
    for i=1:length(list_diff)
        range = get(eval(['diff.',list_diff{i},]),'range');
        eval(['tmp = diff.',list_diff{i},'(range);']);
        tmp = tmp.*10^in_digit;
        tmp = round(tmp);
        tmp = tmp.*10^(-in_digit);
        diff.(char(list_diff{i})) = tseries(range,tmp);
    end;
    
    out_diff = diff;
    %% Create the report
    
    sty = struct(); 
    sty.line.lineWidth = 1;
    sty.line.lineStyle = '-';
    sty.line.Color = {[0 0 1],[0 0.5 0],[1 0 0],[0.5273    0.8047    0.9792]};
    % sty.line.Color = {[0 0 1],[0 0.5 0],[1 0 0],[1.0000    0.8398         0]};
    sty.highlight.faceColor = {[0.92,0.92,0.92],[0.85,0.85,0.85]};
%     sty.legend.location = 'SouthOutside';
%     sty.legend.Orientation = 'Horizontal';

    x = report.new(heading);

    
    graph_list={'usdeur','usdeur','net4_shadoweuribor3m','net4_shadoweuribor3m'};
    plot_range = range_for_comp;
    for i=1:length(graph_list)       
        startr = get(hn.(graph_list{i}),'first');
        endr = get(hn.(graph_list{i}),'last');
        if startr > plot_range(1)
            plot_range = startr:plot_range(end);
        end
        if endr < plot_range(end)
            plot_range = plot_range(1):endr;
        end
    end
    x.figure('','fontsize', 'large','range=',plot_range,'subplot=',[3 2]','dateFormat', 'YY:P','style',sty);  
    for i=1:length(graph_list)
        if mod(i,2) == 1 % odd number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('\Delta',diff.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        elseif mod(i,2) == 0 % even number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('new',hn.(graph_list{i}));
            x.series('old',ho.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        end
    end
        
    graph_list={'net4_pribor3m','net4_pribor3m','net4_pribor1y','net4_pribor1y','net4_euribor3m','net4_euribor3m'};
    plot_range = range_for_comp;
    for i=1:length(graph_list)       
        startr = get(hn.(graph_list{i}),'first');
        endr = get(hn.(graph_list{i}),'last');
        if startr > plot_range(1)
            plot_range = startr:plot_range(end);
        end
        if endr < plot_range(end)
            plot_range = plot_range(1):endr;
        end
    end
    x.figure('','fontsize', 'large','range=',plot_range,'subplot=',[3 2]','dateFormat', 'YY:P','style',sty);    
    for i=1:length(graph_list)
        if mod(i,2) == 1 % odd number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('\Delta',diff.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        elseif mod(i,2) == 0 % even number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('new',hn.(graph_list{i}));
            x.series('old',ho.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        end
    end
    
    graph_list={'net4_euribor1y','net4_euribor1y','net4_eonia3m','net4_eonia3m','net4_eonia1y','net4_eonia1y'};
    plot_range = range_for_comp;
    for i=1:length(graph_list)       
        startr = get(hn.(graph_list{i}),'first');
        endr = get(hn.(graph_list{i}),'last');
        if startr > plot_range(1)
            plot_range = startr:plot_range(end);
        end
        if endr < plot_range(end)
            plot_range = plot_range(1):endr;
        end
    end
    x.figure('','fontsize', 'large','range=',plot_range,'subplot=',[3 2]','dateFormat', 'YY:P','style',sty);
    for i=1:length(graph_list)
        if mod(i,2) == 1 % odd number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('\Delta',diff.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        elseif mod(i,2) == 0 % even number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('new',hn.(graph_list{i}));
            x.series('old',ho.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        end
    end
    
    graph_list={'cpi_emu','cpi_emu','petrol','petrol','brent','brent'};
    plot_range = range_for_comp;
    for i=1:length(graph_list)       
        startr = get(hn.(graph_list{i}),'first');
        endr = get(hn.(graph_list{i}),'last');
        if startr > plot_range(1)
            plot_range = startr:plot_range(end);
        end
        if endr < plot_range(end)
            plot_range = plot_range(1):endr;
        end
    end
    x.figure('','fontsize', 'large','range=',plot_range,'subplot=',[3 2]','dateFormat', 'YY:P','style',sty);
    for i=1:length(graph_list)
        if mod(i,2) == 1 % odd number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('\Delta',diff.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        elseif mod(i,2) == 0 % even number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('new',hn.(graph_list{i}));
            x.series('old',ho.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        end
    end
    
    graph_list={'ppi_emu','ppi_emu','ppi_emu_energy','ppi_emu_energy','ppi_emu_other','ppi_emu_other'};
    plot_range = range_for_comp;
    for i=1:length(graph_list)       
        startr = get(hn.(graph_list{i}),'first');
        endr = get(hn.(graph_list{i}),'last');
        if startr > plot_range(1)
            plot_range = startr:plot_range(end);
        end
        if endr < plot_range(end)
            plot_range = plot_range(1):endr;
        end
    end
    x.figure('','fontsize', 'large','range=',plot_range,'subplot=',[3 2]','dateFormat', 'YY:P','style',sty);
    for i=1:length(graph_list)
        if mod(i,2) == 1 % odd number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('\Delta',diff.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        elseif mod(i,2) == 0 % even number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('new',hn.(graph_list{i}));
            x.series('old',ho.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        end
    end

    graph_list={'gdp_emu','gdp_emu','gdp_emu_trend','gdp_emu_trend','gdp_emu_gap','gdp_emu_gap'};
    plot_range = range_for_comp;
    for i=1:length(graph_list)       
        startr = get(hn.(graph_list{i}),'first');
        endr = get(hn.(graph_list{i}),'last');
        if startr > plot_range(1)
            plot_range = startr:plot_range(end);
        end
        if endr < plot_range(end)
            plot_range = plot_range(1):endr;
        end
    end
    x.figure('','fontsize', 'large','range=',plot_range,'subplot=',[3 2]','dateFormat', 'YY:P','style',sty);
    for i=1:length(graph_list)
        if mod(i,2) == 1 % odd number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('\Delta',diff.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        elseif mod(i,2) == 0 % even number
            if isempty(char(get(hn.(graph_list{i}),'comment')))
                x.graph(char(graph_list{i}),'legend',true);
            else
                x.graph(get(hn.(graph_list{i}),'comment'),'legend',true);
            end
            x.series('new',hn.(graph_list{i}));
            x.series('old',ho.(graph_list{i}));
            x.highlight('',range_for_comp(1):end_hist);
        end
    end
    
    x.publish([histdir 'compare_diff.pdf'],'maketitle=',true, 'display', false);
