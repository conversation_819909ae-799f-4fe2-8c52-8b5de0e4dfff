function [dbm db] = fcastdata_FB(db, dbf, settings, SS)

%% Standard data transformations

%--Convert Foreign Variables--%
db.i_star                   = (1 + db.net4_euribor3m/100)^(1/4);
db.i_star_shadow            = (1 + db.net4_shadoweuribor3m/100)^(1/4);
db.i_star_eu                = (1 + db.net4_shadoweuribor3m/100)^(1/4);
db.i_star_us                = (1 + db.net4_ussofr3m/100)^(1/4);
db.i_star_eq                = (1 + db.equilibrium_i_star/100)^(1/4);
db.i_star_eonia             = (1 + db.net4_eonia3m/100)^(1/4);

db.dot_usdeur               = db.usdeur/db.usdeur{-1};

db.cpi_star_tilde           = db.cpi_emu;
db.pstar_tilde              = db.ppi_emu;
db.pstar_energy_tilde      	= db.ppi_emu_energy;
db.pstar_other_tilde      	= db.ppi_emu_other;
db.dot_cpi_star_tilde       = db.cpi_star_tilde/db.cpi_star_tilde{-1};
db.dot_pstar_tilde          = db.pstar_tilde/db.pstar_tilde{-1};
db.dot_pstar_energy_tilde   = db.pstar_energy_tilde/db.pstar_energy_tilde{-1};
db.dot_pstar_other_tilde	= db.pstar_other_tilde/db.pstar_other_tilde{-1};

db.y_star                   = db.gdp_emu;
db.dot_y_star               = db.y_star/db.y_star{-1};

if ~settings.FB_gdp_decomp
    db.y_star_gap               = exp(db.gdp_emu_gap);
    db.y_star_trend             = db.gdp_emu_trend;
    db.dot_y_star_trend         = db.y_star_trend/db.y_star_trend{-1};
    db.y_star_trend_fund        = db.gdp_emu_trend_fund;
    db.dot_y_star_trend_fund    = db.gdp_emu_trend_fund/db.gdp_emu_trend_fund{-1};
    db.dot_y_star_trend_shift   = (1 + db.gdp_emu_trend_shift / 100)^(1/4);
end

%% Data for fcast_plan

%--Interest Rates--%
dbm.i_star                  = db.i_star;
dbm.i_star_eu               = db.i_star_eu;
dbm.i_star_us               = db.i_star_us;
dbm.i_star_shadow           = db.i_star_shadow;
dbm.i_star_eq               = db.i_star_eq;
dbm.i_star_eonia            = db.i_star_eonia;

%--Financial Markets--%
dbm.usdeur                  = db.usdeur;
dbm.dot_usdeur              = db.dot_usdeur;

%--Prices--%
dbm.dot_cpi_star_tilde      = db.dot_cpi_star_tilde;
dbm.dot_pstar_tilde         = db.dot_pstar_tilde;
dbm.dot_pstar_energy_tilde  = db.dot_pstar_energy_tilde; 
dbm.dot_pstar_other_tilde   = db.dot_pstar_other_tilde;

%--Real Economic Activity--%
dbm.dot_y_star              = db.dot_y_star;

if settings.FB_gdp_decomp
    dbm.y_star_gap              = dbf.y_star_gap;
    dbm.dot_y_star_trend        = dbf.dot_y_star_trend;
    dbm.dot_y_star_trend_fund   = dbf.dot_y_star_trend_fund;
    dbm.dot_y_star_trend_shift  = dbf.dot_y_star_trend_shift;
else
    dbm.y_star_gap              = db.y_star_gap;
    dbm.dot_y_star_trend        = db.dot_y_star_trend;
    dbm.dot_y_star_trend_fund   = db.dot_y_star_trend_fund;
    dbm.dot_y_star_trend_shift  = db.dot_y_star_trend_shift;
end

%% Expert judgements - won't appear in reports

disp('fcastdata: Tuning data in model after transformations');


%% Weighted average of 3M EURIBOR and its shadow counterpart (for UIP)
dbm = shadowRates_average(dbm,settings);

end

%<eof>