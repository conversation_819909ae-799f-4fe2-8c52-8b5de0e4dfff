function [ decomp_rls ] = decomp_causality_rls(kf, m, SS, d_old , d_new, ...
				str, rlsCausality, detail_level, decomp_range, sr_opt, limit, ...
				fcast_plan_obs_decomp, fcast_plan_surprise_obs_decomp)
			
islog = get(m, 'log');
			
endog_vars = get(m,'xList');
endog_vars = [endog_vars regexprep(endog_vars, '(\w*)$', '$1_exp')];

warning('doObsFree = 1 for causality in release, sorry...');

% init
switch detail_level
	case {0}
		store_matrix	= NaN(length(decomp_range), length(rlsCausality), length(endog_vars));
		input_datenames	= cell(length(rlsCausality),1);
		input_vect		= NaN(length(rlsCausality),2);
		input_names		= cell(1,length(rlsCausality));
	case {1,2}
		store_matrix	= NaN(length(decomp_range), length(rlsCausality)*length(sr_opt.cmp.trans_rng), length(endog_vars));
		input_datenames	= cell(length(rlsCausality)*length(sr_opt.cmp.trans_rng),1);
		input_vect		= NaN(length(rlsCausality)*length(sr_opt.cmp.trans_rng),2);
		input_names		= cell(1,length(rlsCausality)*length(sr_opt.cmp.trans_rng));
end
truediffmat		= NaN(length(decomp_range), length(endog_vars));

s_old = d_old;
% d_mix = dbclip(d_old*dbnames(d_old,'nameFilter','^obs_\w*'), sr_opt.new.hrng);
switch str
	case 'exo'
		d_mix = dbclip(d_old*dbnames(d_old,'nameFilter','^obs_\w*'), sr_opt.old.hrng);
	case 'endo'
		d_mix = dbclip(d_old*dbnames(d_old,'nameFilter','^obs_\w*'), sr_opt.new.hrng) + dbclip(d_old*rlsCausality, sr_opt.old.hrng);
	otherwise
		error('Wrong input argument - must be ''exo'' or ''endo''.');
end
h = waitbar(0,'Please wait...');
t=0; counter = 0;
switch detail_level
	case {0}
		dat = sr_opt.cmp.trans_rng; 
		for ix = 1:length(rlsCausality)
			tic;
			counter = counter+1;
			% generate simulation
			d_mix.(rlsCausality{ix})(dat) = d_new.(rlsCausality{ix})(dat);
			[s_new] = datamodel_replicate(d_mix, SS, ...
				'kalman', kf, ...
				'model', m, ...
				'range_hist', sr_opt.new.hrng, ...
				'range_fcast', sr_opt.new.comprng, ...
				'limit', limit);
			% compute store_matrix
			for ivar = 1 : length(endog_vars)
				if isempty(regexp(endog_vars{ivar},'\w*_exp$','ONCE'))
					if islog.(endog_vars{ivar})
						kth_diff = log(imag(s_new.(char(endog_vars(ivar)))(decomp_range)) ./ ...
							imag(s_old.(char(endog_vars(ivar)))(decomp_range)));
						store_matrix(:,counter,ivar) = kth_diff;
					else
						kth_diff = imag(s_new.(char(endog_vars(ivar)))(decomp_range)) - ...
							imag(s_old.(char(endog_vars(ivar)))(decomp_range));
						store_matrix(:,counter,ivar) = kth_diff;
					end
				else
					if islog.(endog_vars{ivar-length(endog_vars)/2})
						kth_diff = log(real(s_new.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range)) ./ ...
							real(s_old.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range)));
						store_matrix(:,counter,ivar) = kth_diff;
					else
						kth_diff = real(s_new.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range)) - ...
						real(s_old.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range));
						store_matrix(:,counter,ivar) = kth_diff;
					end
				end    
			end
			% fill in others
			input_names{1,counter} = rlsCausality{ix};
			input_datenames{counter,1} = ['all__obsrls__' rlsCausality{ix}];
			% prepare for next step
			s_old = s_new;
			t=t+toc;
			waitbar(counter/(length(rlsCausality)),h, ...
				['Processing: ' strrep(rlsCausality{ix},'_','\_') ', ETA: ' ...
				num2str((length(rlsCausality)-counter)*t/counter/60,'%4.2f') ' minutes.']);
		end
	case {1,2}
		for dat = sr_opt.cmp.trans_rng
			for ix = 1:length(rlsCausality)
				tic;
				counter = counter+1;
				% generate simulation
				d_mix.(rlsCausality{ix})(dat) = d_new.(rlsCausality{ix})(dat);
				[s_new] = datamodel_replicate(d_mix, SS, ...
					'kalman', kf, ...
					'model', m, ...
					'range_hist', sr_opt.new.hrng, ...
					'range_fcast', sr_opt.new.comprng, ...
					'limit', limit);
				% compute store_matrix
				for ivar = 1 : length(endog_vars)
					if isempty(regexp(endog_vars{ivar},'\w*_exp$','ONCE'))
						if islog.(endog_vars{ivar})
							kth_diff = log(imag(s_new.(char(endog_vars(ivar)))(decomp_range)) ./ ...
								imag(s_old.(char(endog_vars(ivar)))(decomp_range)));
							store_matrix(:,counter,ivar) = kth_diff;
						else
							kth_diff = imag(s_new.(char(endog_vars(ivar)))(decomp_range)) - ...
								imag(s_old.(char(endog_vars(ivar)))(decomp_range));
							store_matrix(:,counter,ivar) = kth_diff;
						end
					else
						if islog.(endog_vars{ivar-length(endog_vars)/2})
							kth_diff = log(real(s_new.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range)) ./ ...
								real(s_old.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range)));
							store_matrix(:,counter,ivar) = kth_diff;
						else
							kth_diff = real(s_new.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range)) - ...
							real(s_old.(char(endog_vars{ivar-length(endog_vars)/2}))(decomp_range));
							store_matrix(:,counter,ivar) = kth_diff;
						end
					end    
				end
				% fill in others
				input_names{1,counter} = rlsCausality{ix};
				input_datenames{counter,1} = [char(dat2str(dat)) '__obsrls__' rlsCausality{ix}];
				% prepare for next step
				s_old = s_new;
				t=t+toc;
				waitbar(counter/(length(rlsCausality)*length(sr_opt.cmp.trans_rng)),h, ...
					['Processing: ' char(dat2str(dat)) ' ' strrep(rlsCausality{ix},'_','\_') ', ETA: ' ...
					num2str((length(rlsCausality)*length(sr_opt.cmp.trans_rng)-counter)*t/counter/60,'%4.2f') ' minutes.']);
			end
		end
end
close(h);

% truediffmat
for ivar = 1 : length(endog_vars)
    if isempty(regexp(endog_vars{ivar},'\w*_exp$','ONCE'))
        if islog.(endog_vars{ivar})
            truediffmat(:,ivar) = log(imag(d_new.(endog_vars{ivar})(decomp_range)) ./ ...
				imag(d_old.(endog_vars{ivar})(decomp_range))) ;
        else
            truediffmat(:,ivar) = imag(d_new.(endog_vars{ivar})(decomp_range)) - ...
				imag(d_old.(endog_vars{ivar})(decomp_range)) ;
        end
    else
        if islog.(endog_vars{ivar-length(endog_vars)/2})
            truediffmat(:,ivar) = log(real(d_new.(endog_vars{ivar-length(endog_vars)/2})(decomp_range)) ./ ...
				real(d_old.(endog_vars{ivar-length(endog_vars)/2})(decomp_range))) ;
        else
            truediffmat(:,ivar) = real(d_new.(endog_vars{ivar-length(endog_vars)/2})(decomp_range)) - ...
				real(d_old.(endog_vars{ivar-length(endog_vars)/2})(decomp_range)) ;
        end
    end    
end

decomp_rls.store_matrix		= store_matrix;
decomp_rls.truediffmat		= truediffmat;
decomp_rls.input_datenames	= input_datenames;
decomp_rls.input_vect		= input_vect;
decomp_rls.input_names		= input_names;
decomp_rls.decomp_range		= decomp_range;
decomp_rls.fut_rng			= sr_opt.new.comprng;
decomp_rls.trans_rng		= sr_opt.old.start_pred:sr_opt.new.ehist;
decomp_rls.hist_rng			= sr_opt.old.hrng;
decomp_rls.endog_vars		= endog_vars;
decomp_rls.d_old			= d_old;
decomp_rls.d_new			= d_new;
decomp_rls.islog			= islog;

check_decomposition(decomp_rls, 'limit', limit);