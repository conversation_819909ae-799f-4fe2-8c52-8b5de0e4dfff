%**************************************************************************
function SS = setparam_FB(settings,varargin)
%**************************************************************************
P = struct();

%% Load input
default = { ...
    'filtering',		1, ...
    };

P = passopt(default,varargin{:});

%% ON/OFF Toggles
ass(P,'FB_gdp_decomp',	settings.FB_gdp_decomp);       	% Foreign block (FB): 1 - foreign GDP decomposed endogenously, 0 - foreign GDP decomposition as observations
ass(P,'FB_prem_decomp',	settings.FB_prem_decomp); 
%% Long run trends
% SHOULD be consistent with a setting in the g3+ model
ass(P,'foreign_output_growth',                  1.0159^(1/4));
ass(P,'dot_pstar_tilde_',                       1.02^(1/4));
ass(P,'dot_pstar_energy_tilde_',                P.dot_pstar_tilde_);
ass(P,'dot_cpi_star_tilde_',                    1.02^(1/4));
ass(P,'energy_share_ppi_star',                  0.28);
ass(P,'i_star_',                                1.025^(1/4));
ass(P,'i_star_us_',                             1.0325^(1/4));
ass(P,'prem_usdeur_',                           P.i_star_us_/P.i_star_);
ass(P,'usdeur_',                                1.2);
ass(P,'dot_usdeur_',                            1);


%% Calling the STEADY-STATE solution (ssmodel_ZB.m)
%=======================================================
SS = ssmodel_FB(P,settings);

%% Dynamic parameters
ass(SS,'a_ystar_AR',                            0.75);
ass(SS,'a_ystar_IR',                            0.1126);
ass(SS,'a_ystar_RER',                           0.0165);
ass(SS,'a_pstar_ystar',                         0.1075);
ass(SS,'a_pstar_AR',                            0.1867);
ass(SS,'a_pstar_RER',                           0.0101);
ass(SS,'a_istar_ystar',                         0.1975);
ass(SS,'a_istar_pi',                            2.0);
ass(SS,'a_istar_AR',                            0.7910);
ass(SS,'rho_prem_usdeur',                       0.3340);
ass(SS,'a_usdeur',                              0.025);
ass(SS,'a_cpistar_pstar',                       1/4);

%% AR processes parameters
ass(SS,'rho_usdeur',                            0.6808);
ass(SS,'rho_shadow_rate',                       0.7244);
ass(SS,'rho_dot_z_eq',                          0.6810);
ass(SS,'rho_pstar_RP_tilde',                    0.6168);
ass(SS,'rho_y_star_trend_fund',                 0.7509);
ass(SS,'rho_pstar_energy_tilde',                0.6923);
ass(SS,'rho_i_star_eq',                         0.8000);
ass(SS,'rho_i_star_us',                         0.8000);
ass(SS,'rho_cpistar',                           0.3);

%% Tunes
ass(SS,'tune_y_star_gap',                       SS.y_star_gap);    
ass(SS,'tune_dot_y_star_trend_fund',            SS.dot_y_star_trend_fund); 
ass(SS,'tune_dot_y_star_trend_shift',           SS.dot_y_star_trend_shift); 
ass(SS,'tune_dot_pstar_other_tilde',            SS.dot_pstar_other_tilde);
ass(SS,'tune_dot_cpi_star_tilde',               SS.dot_cpi_star_tilde);

%% SE of structural shocks
scaling  = 1.5 ;
ass(SS,'std_eps_Istar',                         0.0081   * scaling );   ass(SS,'std_eps_exp_Istar',                     0);
ass(SS,'std_eps_y_star_gap',                    0.0186   * scaling );   ass(SS,'std_eps_exp_y_star_gap',                0);
ass(SS,'std_eps_dot_y_star_trend_shift',        0.0005   * scaling );   ass(SS,'std_eps_exp_dot_y_star_trend_shift',    0);
ass(SS,'std_eps_dot_y_star_trend_fund',         0.0020   * scaling );   ass(SS,'std_eps_exp_dot_y_star_trend_fund',     0);
ass(SS,'std_eps_pstar_other_tilde',             0.0084   * scaling );   ass(SS,'std_eps_exp_pstar_other_tilde',         0);      
ass(SS,'std_eps_USDEUR',                        0.0033   * scaling );   ass(SS,'std_eps_exp_USDEUR',                    0);            
ass(SS,'std_eps_pstar_tilde',                   0.001    * scaling );   ass(SS,'std_eps_exp_pstar_tilde',               0); 
ass(SS,'std_eps_pstar_energy_tilde',            0.0547   * scaling );   ass(SS,'std_eps_exp_pstar_energy_tilde',        0); 
ass(SS,'std_eps_pstar_RP_tilde',                0.003    * scaling );   ass(SS,'std_eps_exp_pstar_RP_tilde',            0);  
ass(SS,'std_eps_energy_share_ppi_star_gap',     0.0075   * scaling );   ass(SS,'std_eps_exp_energy_share_ppi_star_gap', 0);  
ass(SS,'std_eps_prem_usdeur',                   0.0250   * scaling );   ass(SS,'std_eps_exp_prem_usdeur',               0);
ass(SS,'std_eps_dot_cpi_star_tilde',            0.0100   * scaling );   ass(SS,'std_eps_exp_dot_cpi_star_tilde',        0); %0.0100
ass(SS,'std_eps_i_star_eq',                     0.0010   * scaling );   ass(SS,'std_eps_exp_i_star_eq',                 0);
ass(SS,'std_eps_i_star_us',                     0.0010   * scaling );   ass(SS,'std_eps_exp_i_star_us',                 0);
ass(SS,'std_eps_shadow_rate',                   0.0010   * scaling );   ass(SS,'std_eps_exp_shadow_rate',               0);   
ass(SS,'std_eps_dot_z_eq',                      0.0222   * scaling );   ass(SS,'std_eps_exp_dot_z_eq',                  0);
   
%% SE of measurement errors
ass(SS,'std_omega_Y_STAR',                      0.001);
ass(SS,'std_omega_I_STAR',                      0.001);
ass(SS,'std_omega_I_STAR_EQ',                   0.001);
ass(SS,'std_omega_I_STAR_EU',                   0.001);
ass(SS,'std_omega_I_STAR_US',                   0.001);
ass(SS,'std_omega_USDEUR',                      0.01);  
ass(SS,'std_omega_PSTAR_ENERGY_TILDE',          1);      
ass(SS,'std_omega_PSTAR_OTHER_TILDE',           0.025); 
ass(SS,'std_omega_PSTAR_TILDE',                 0.001);
ass(SS,'std_omega_CPI_STAR_TILDE',              0.001); % 0.001

if ~settings.FB_gdp_decomp
    ass(SS,'std_omega_Y_STAR_GAP',             	0.001);
    ass(SS,'std_omega_Y_STAR_TREND',           	0.001);
    ass(SS,'std_omega_DOT_Y_STAR_TREND_SHIFT',	0.001);
end


if ~settings.FB_prem_decomp
    ass(SS,'std_omega_PREM_USDEUR',             0.001);
end
end

% <end>
