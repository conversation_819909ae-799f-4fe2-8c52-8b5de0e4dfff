function printgroups2file(groups,groups_nms)
% Prints groups and groups_nms to the file named GROUPS.TXT in the current
% directory in the format used in READ_GROUPS.M

%- MAIN FUNCTION ---------------------------------------------------------%
fid = fopen('groups.txt','w');

% groups_nms
fprintf(fid, '%s\n', 'groups_nms = {');
for i = 1:size(groups_nms,1)
	fprintf(fid, '\t''%s''\t''%s''\t''%s''\n', ...
		char(groups_nms(i,1)),char(groups_nms(i,2)),char(groups_nms(i,3)));
end
fprintf(fid, '%s\n', '};');

% groups
fprintf(fid, '%s\n', 'groups = { ...');
for i = 1:size(groups,2)
	fprintf(fid, '\t%s', '{');
	for j = 1:size(groups{i},2)
		fprintf(fid, '''%s''%s\n\t\t', char(groups{i}{j}),', ...');
	end
	fprintf(fid, '\t%s\n', '}, ...');
end
fprintf(fid, '%s\n', '};');

fclose(fid);

end %- of MAIN FUNCTION --------------------------------------------------%

