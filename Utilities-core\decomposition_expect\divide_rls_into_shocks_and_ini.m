function [decomp_rls_out] = divide_rls_into_shocks_and_ini(decomp_rls,m_new,kf_new,f_free_mix,f_free_new,sr_opt,...
    fcast_plan_obs_decomp,is_detailed_obs,is_detailed_fix,is_detailed_eps,decomp_range,limit,expectations)

eps_names   = dbnames(f_free_new,'nameFilter','^eps_\w*');
db_pom		= dbclip(f_free_new,sr_opt.old.hrng);
db_pom = dbextend(db_pom,f_free_new*eps_names);

db_pom =  simulate(m_new, db_pom, sr_opt.old.comprng);
db_pom      =  dbextend(db_pom,f_free_new*eps_names);
f_free_new_ehist_old  = dbextend(f_free_new,db_pom);

[ decomp_rls_shocks ] = decomp_all(m_new, f_free_mix , f_free_new_ehist_old, ...
    decomp_range, ...
    'plan', fcast_plan_obs_decomp, ...
    'simrng', sr_opt.old.comprng, ...
    'expect_range', sr_opt.new.comprng, ...
    'ini2obs', false,...
    'is_detailed_obs_', is_detailed_obs, ...
    'is_detailed_fix_', is_detailed_fix, ...
    'is_detailed_eps_', is_detailed_eps, ...
	'expectations', expectations);

s           = regexp(decomp_rls_shocks.input_datenames,'__','split');
ndn         = length(decomp_rls_shocks.input_datenames);
indate      = cell(ndn,1);
intype      = cell(ndn,1);
inname      = cell(ndn,1);
for i = 1:ndn
	indate{i} = char(s{i}(1));
	intype{i} = char(s{i}(2));
	inname{i} = char(s{i}(3));
end

nfar        = length(decomp_rls.input_datenames);
n_ini       = sum (strcmp(intype,'ini'));
n_shocks    = sum (strcmp(intype,'res'));
nvars       = length(decomp_rls_shocks.endog_vars);
nper        = length(decomp_rls_shocks.decomp_range);

ini_storematrix_out = zeros(nfar,n_ini);

jj=0;
for ii = 1:ndn
    if strcmp(intype{ii},'ini')
        jj=jj+1;        
        [~,index_endogvars] = ismember(inname{ii},decomp_rls.endog_vars);
        [~,index_range]     = ismember(indate{ii},dat2str(decomp_rls.decomp_range));
        ini_storematrix_out(:,jj) = decomp_rls.store_matrix(index_range,:,index_endogvars)./sum(decomp_rls.store_matrix(index_range,:,index_endogvars));
    end  
end

ini_storematrix_in    = reshape(permute(decomp_rls_shocks.store_matrix(:,strcmp(intype,'ini'),:),[2 1 3]),n_ini,nper*nvars);
store_matrix_1        = ini_storematrix_out*ini_storematrix_in;
store_matrix_2        = reshape(permute(decomp_rls_shocks.store_matrix(:,strcmp(intype,'res'),:),[2 1 3]),n_shocks,nper*nvars);

store_matrix          = [store_matrix_1;store_matrix_2];
store_matrix          = permute(reshape(store_matrix,nfar+n_shocks,nper,nvars),[2 1 3]); 

[~,index]           = ismember(decomp_range,sr_opt.old.hrng);
index               = index>0;
store_matrix(index,1:nfar,:) = decomp_rls.store_matrix(index,:,:);

d_old               = f_free_new;
d_new               = f_free_new_ehist_old;
islog               = decomp_rls.islog;
endog_vars          = decomp_rls.endog_vars;
input_names         = [decomp_rls.input_names, decomp_rls_shocks.input_names(n_ini+1:end), 'rls_exp_change'];
input_datenames     = [decomp_rls.input_datenames;decomp_rls_shocks.input_datenames(n_ini+1:end); 'all__inimod__rls_exp_change'];
input_vect          = [decomp_rls.input_vect;decomp_rls_shocks.input_vect(n_ini+1:end,:); NaN, 31];
aux = size(store_matrix,2)+1;
for k = 1 : length(endog_vars)
    if islog.(endog_vars{k})
        store_matrix(:,aux,k) = ...
            log(d_old.(char(endog_vars(k)))(decomp_range) ./ ...
            d_new.(char(endog_vars(k)))(decomp_range));
    else
        store_matrix(:,aux,k) = ...
            d_old.(char(endog_vars(k)))(decomp_range) - ...
            d_new.(char(endog_vars(k)))(decomp_range);
    end
end
    
decomp_rls_out.store_matrix     = store_matrix;
decomp_rls_out.input_datenames  = input_datenames;
decomp_rls_out.input_names      = input_names;
decomp_rls_out.input_vect       = input_vect;
decomp_rls_out.truediffmat      = decomp_rls.truediffmat;
decomp_rls_out.decomp_range     = decomp_rls.decomp_range;
decomp_rls_out.fut_rng          = decomp_rls.fut_rng;
decomp_rls_out.trans_rng        = decomp_rls.trans_rng;
decomp_rls_out.hist_rng         = decomp_rls.hist_rng;
decomp_rls_out.endog_vars       = decomp_rls.endog_vars;
decomp_rls_out.d_old            = decomp_rls.d_old;
decomp_rls_out.d_new            = decomp_rls.d_new;
decomp_rls_out.islog            = decomp_rls.islog;

check_decomposition(decomp_rls_out, 'limit', limit);

end